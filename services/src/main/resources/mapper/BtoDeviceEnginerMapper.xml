<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.BtoDeviceEnginerMapper">

    <select id="selectStationIDByName" resultType="String">
        select plant_id
        from bto_station_list
        where name = #{name}
    </select>
    <select id="selectBySN" resultType="entityDTO.BtoDeviceEnginerDTO">
        select *
        from bto_device_list
        where device_sn = #{sn}
    </select>
    <select id="btostationBase" resultType="java.lang.Integer">
        UPDATE bto_station_list
        set orentation= #{orentation}
        where plant_id = #{plantId}
    </select>
    <select id="btostationList" resultType="java.lang.Integer">
        UPDATE bto_station_base
        set orentation= #{orentation}
        where plant_id = #{plantId}
    </select>

    <select id="getDeviceCode" resultType="java.util.HashMap">
        SELECT `name1` plantName,
        plant_id plantId,
        wisdom_device_sn IMEI,
        device_sn SN
        FROM v1_station_device
        WHERE
        <if test=" type == 'nameStr'.toString ">
            `name1`LIKE CONCAT('%',#{value},'%')
        </if>
        <if test="type == 'phone'.toString ">
            tel=#{value}
        </if>
    </select>
    <delete id="deleteBySN">
        DELETE  FROM bto_version WHERE sn = #{updateDeviceFormDTO.oldDeviceSN}
    </delete>
    <update id="updateDeviceList" >
        UPDATE `bto_device_list`
        SET datalogger_sn=#{updateDeviceFormDTO.newDeviceSN},
            device_sn=#{updateDeviceFormDTO.newDeviceSN},
            device_address=#{updateDeviceFormDTO.deviceAddress},
            manu_facturer=#{updateDeviceFormDTO.manufacturer},
            model=#{updateDeviceFormDTO.model},
            create_time=now()
        WHERE device_sn = #{updateDeviceFormDTO.oldDeviceSN}
    </update>
    <update id="updateDeviceModules">
        UPDATE `bto_device_modules`
        SET sn=#{updateDeviceFormDTO.newDeviceSN}
        WHERE sn = #{updateDeviceFormDTO.oldDeviceSN}
    </update>
</mapper>