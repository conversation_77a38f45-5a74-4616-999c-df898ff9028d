package com.botong.services.service.impl;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYBtoDeviceListMapper;
import com.botong.services.service.HYBtoDeviceListService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entityDTO.*;
import javafx.beans.binding.LongExpression;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import vo.EfficiencyVo;

import javax.annotation.Resource;
import java.text.Collator;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class HYBtoDeviceListServiceImpl extends ServiceImpl<HYBtoDeviceListMapper, BtoDeviceList> implements HYBtoDeviceListService {
    @Resource
    private HYBtoDeviceListMapper hyBtoDeviceListMapper;

    @Resource
    private HYBtoDeviceListService hyBtoDeviceListService;

/*    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'Deviceonline' ", cacheManager = "HY_Web_Cache")
    public Integer Deviceonline() {
        Date date = new Date();
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String tableNme2 = time.format(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String tableName = simpleDateFormat.format(date) + "%";
        Integer deviceonline = hyBtoDeviceListMapper.deviceOnline(tableName, tableNme2);
        if (deviceonline <= 0) {
            return 0;
        }
        return deviceonline;
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'DeviceAllNum' ", cacheManager = "HY_Web_Cache")
    public Integer DeviceAllNum() {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<BtoDeviceList>();
        queryWrapper.eq("special", 1);
        Number deviceAllNum = baseMapper.selectCount(queryWrapper);
        return deviceAllNum.intValue();
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'DeviceStatus2' ", cacheManager = "HY_Web_Cache")
    public Integer DeviceStatus2() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String tableName = simpleDateFormat.format(date) + "%";
        return hyBtoDeviceListMapper.deviceAlert(tableName);
    }*/

    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'workEfficiencyRanking' ", cacheManager = "HY_Web_Cache")
    public HashMap<String, Object> workEfficiencyRanking() {
        List<EfficiencyVo> efficiencyVos = baseMapper.workEfficiencyRanking();
        Collections.sort(efficiencyVos, new Comparator<EfficiencyVo>() {
            @Override
            public int compare(EfficiencyVo vo1, EfficiencyVo vo2) {
                //获取中文环境
                Comparator<Object> com = Collator.getInstance(java.util.Locale.CHINA);
                return com.compare(vo1.getStationName(), vo2.getStationName());
            }
        });
        for (int i = 0; i < efficiencyVos.size(); i++) {
            String subStationName = efficiencyVos.get(i).getStationName().substring(4);
            efficiencyVos.get(i).setStationName(subStationName);
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("total", efficiencyVos.size() + 1);
        hashMap.put("list", efficiencyVos);
        return hashMap;
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'DeviceInformation'.concat(#plantUid) ", cacheManager = "HY_Web_Cache")
    public List<BtoDeviceList> DeviceInformation(String plantUid) {
//        Date date = new Date();
//        Date now20 = new Date(date.getTime() - 1200000);
//        Date now10 = new Date(date.getTime() - 600000);
//        SimpleDateFormat simpleDateFormat10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//        SimpleDateFormat simpleDateFormat20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //表名
        //截取当前时间前10分钟
//        String Nowtime10 = (simpleDateFormat10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
//        String Nowtime20 = (simpleDateFormat20.format(now20)).substring(0, 15) + "%";
        return hyBtoDeviceListMapper.DeviceInformation(plantUid);
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'DeviceInformationSN'.concat(#plantUid)", cacheManager = "HY_Web_Cache")
    public List<BtoDeviceList> DeviceInformationSN(String plantUid) {
        return hyBtoDeviceListMapper.DeviceInformationSN(plantUid);
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'DeviceInformationTU'.concat(#plantUid) ", cacheManager = "HY_Web_Cache")
    public List<TUBIAO> DeviceInformationTU(String plantUid) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        //表名
        String tableName = simpleDateFormat.format(date);
        return hyBtoDeviceListMapper.DeviceInformationTU(plantUid, tableName);
    }

    @Override
    public List<DeviceMonitorUListDTO> DeviceMonitorUlist() {
        return hyBtoDeviceListMapper.DeviceMonitorUlist();

    }

    @Override
    public List<sheBeiJianCe> collectionMaxMin(String snName, String tableNme, String tabletime2) {
        return hyBtoDeviceListMapper.collectionMaxMin(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> collectioAvg(String snName, String tableNme, String tabletime2) {
        return hyBtoDeviceListMapper.collectioAvg(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> Devicecurrent(String snName, String tableNme, String tabletime2) {
        return hyBtoDeviceListMapper.Devicecurrent(snName, tableNme, tabletime2);
    }

    @Override
    public List<HashMap<String, Object>> EquipmentMonitoringplantUid(String plantId) {
        List<HashMap<String, Object>> equipmentMonitoringList = hyBtoDeviceListMapper.EquipmentMonitoringplantUid(plantId);
        //在线时长
        equipmentMonitoringList.forEach(equipmentMonitoring -> {
            String betweenTimeMsStr = equipmentMonitoring.get("betweenTimeMs") == null ? "0" : equipmentMonitoring.get("betweenTimeMs").toString();
            long betweenDayMs = Long.parseLong(betweenTimeMsStr) * 1000;
            String onlineDuration = DateUtil.formatBetween(betweenDayMs, BetweenFormatter.Level.HOUR);
            equipmentMonitoring.put("onlineDuration", onlineDuration);
            if ("1".equals(equipmentMonitoring.get("status").toString()) || "3".equals(equipmentMonitoring.get("status").toString())) {
                equipmentMonitoring.replace("status", "在线");
            } else if ("2".equals(equipmentMonitoring.get("status").toString())) {
                equipmentMonitoring.replace("status", "告警");
            } else if ("0".equals(equipmentMonitoring.get("status").toString())) {
                equipmentMonitoring.replace("status", "离线");
            }
        });
        return equipmentMonitoringList;
    }

    @Override
    public List<sheBeiJianCe> DeviceVoltage(String snName, String tableName, String tableName2) {
        return hyBtoDeviceListMapper.DeviceVoltage(snName, tableName, tableName2);
    }

    @Override
    public List<sheBeiJianCe> DeviceFrequency(String snName, String tableName, String tableName2) {
        return hyBtoDeviceListMapper.DeviceFrequency(snName, tableName, tableName2);
    }

    @Override
    public List<sheBeiJianCe> Devicetemperature(String snName, String tableName, String tableName2) {
        return hyBtoDeviceListMapper.Devicetemperature(snName, tableName, tableName2);
    }

    @Override
    public List<sheBeiJianCe> DeviceActivePower(String snName, String tableName, String tableName2) {
        return hyBtoDeviceListMapper.DeviceActivePower(snName, tableName, tableName2);
    }

    @Override
    public List<BtoDeviceList> getUserInverterNum(String plantId) {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("datalogger_sn", "device_status");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @Cacheable(cacheNames = "HY_Web3M", key = "'DeviceNum'", cacheManager = "HY_Web_Cache_3M")
    public HashMap<String, Integer> DeviceNum() {
        HashMap<String, Integer> deviceNumMap = new HashMap<>();
        //获取所有逆变器数量
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<BtoDeviceList>();
        queryWrapper.eq("special", 1);
        Long deviceAllNumL = baseMapper.selectCount(queryWrapper);
        Integer deviceAllNum = deviceAllNumL.intValue();
        //获取在线逆变器数量
        Date date = new Date();
//        Date near_date = new Date(date.getTime() - 10 * 60 * 1000);
//        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String curTime = simpleDateFormat.format(date).substring(0, 10) + "%";
//        String nearTime = simpleDateFormat.format(near_date).substring(0, 15) + "%";
        //获取在线逆变器数量
        Integer onlineDeviceNum = hyBtoDeviceListMapper.deviceOnline();
        //获取告警逆变器数量
        Integer alertDeviceNum = hyBtoDeviceListMapper.deviceAlert(curTime);
        //获取正常逆变器数量
        Integer normalDeviceNum = onlineDeviceNum - alertDeviceNum;
        //获取离线逆变器数量
        Integer offlineDeviceNum = deviceAllNum - onlineDeviceNum;
        //获取自检提示数
        Integer selectSelfInspectionNum = hyBtoDeviceListMapper.selectSelfInspectionNum();

        deviceNumMap.put("deviceAllNum", deviceAllNum);
        deviceNumMap.put("onlineDeviceNum", onlineDeviceNum);
        deviceNumMap.put("alertDeviceNum", alertDeviceNum);
        deviceNumMap.put("normalDeviceNum", normalDeviceNum);
        deviceNumMap.put("offlineDeviceNum", offlineDeviceNum);
        deviceNumMap.put("selectSelfInspectionNum", selectSelfInspectionNum);
        return deviceNumMap;
    }

    @Override
    public HashMap<String, Object> mergecollectionMaxMin(String snName, String tableName, String tableName2) {
        HashMap<String, Object> iacMaxmap = new HashMap<>();
        HashMap<String, Object> iacMinmap = new HashMap<>();
        HashMap<String, Object> vacMaxmap = new HashMap<>();
        HashMap<String, Object> vacMinmap = new HashMap<>();
        HashMap<String, Object> ActivePowerMaxmap = new HashMap<>();
        HashMap<String, Object> ActivePowerMinmap = new HashMap<>();
        HashMap<String, Object> FaxMaxmap = new HashMap<>();
        HashMap<String, Object> FaxMinmap = new HashMap<>();
        HashMap<String, Object> TemperatureMinmap = new HashMap<>();
        HashMap<String, Object> TemperatureMaxmap = new HashMap<>();
        HashMap<String, Object> avg = new HashMap<>();

        HashMap<String, Object> map = new HashMap<>();

        List<sheBeiJianCe> list1 = hyBtoDeviceListService.Devicecurrent(snName, tableName, tableName2);//电流
        List<sheBeiJianCe> list2 = hyBtoDeviceListService.DeviceVoltage(snName, tableName, tableName2);//电压
        List<sheBeiJianCe> list3 = hyBtoDeviceListService.DeviceFrequency(snName, tableName, tableName2);//频率
        List<sheBeiJianCe> list4 = hyBtoDeviceListService.Devicetemperature(snName, tableName, tableName2);//温度
        List<sheBeiJianCe> list5 = hyBtoDeviceListService.DeviceActivePower(snName, tableName, tableName2);//有功功率
        List<sheBeiJianCe> list6 = hyBtoDeviceListService.collectioAvg(snName, tableName, tableName2);//平均值


        //电流最大值
        sheBeiJianCe iacMaxA = list1.stream().max(Comparator.comparing(sheBeiJianCe::getIac1)).get();
        sheBeiJianCe iacMaxB = list1.stream().max(Comparator.comparing(sheBeiJianCe::getIac2)).get();
        sheBeiJianCe iacMaxC = list1.stream().max(Comparator.comparing(sheBeiJianCe::getIac3)).get();

        //电流最小值
        sheBeiJianCe iacMinA = list1.stream().min(Comparator.comparing(sheBeiJianCe::getIac1)).get();
        sheBeiJianCe iacMinB = list1.stream().min(Comparator.comparing(sheBeiJianCe::getIac2)).get();
        sheBeiJianCe iacMinC = list1.stream().min(Comparator.comparing(sheBeiJianCe::getIac3)).get();


        //电压最大值
        sheBeiJianCe vacMaxA = list2.stream().max(Comparator.comparing(sheBeiJianCe::getVac1)).get();
        sheBeiJianCe vacMaxB = list2.stream().max(Comparator.comparing(sheBeiJianCe::getVac2)).get();
        sheBeiJianCe vacMaxC = list2.stream().max(Comparator.comparing(sheBeiJianCe::getVac3)).get();
        //电压最小值
        sheBeiJianCe vacMinA = list2.stream().min(Comparator.comparing(sheBeiJianCe::getVac1)).get();
        sheBeiJianCe vacMinB = list2.stream().min(Comparator.comparing(sheBeiJianCe::getVac2)).get();
        sheBeiJianCe vacMinC = list2.stream().min(Comparator.comparing(sheBeiJianCe::getVac3)).get();

        //功率最大值
        sheBeiJianCe PowerMax = list5.stream().max(Comparator.comparing(sheBeiJianCe::getActivePower)).get();
        //功率最小值
        sheBeiJianCe Powermin = list5.stream().min(Comparator.comparing(sheBeiJianCe::getActivePower)).get();

        //频率最大值
        sheBeiJianCe FacMax = list3.stream().max(Comparator.comparing(sheBeiJianCe::getFac1)).get();
        //频率最小值
        sheBeiJianCe Facmin = list3.stream().min(Comparator.comparing(sheBeiJianCe::getFac1)).get();

        //温度最大值
        sheBeiJianCe TemperatureMax = list4.stream().max(Comparator.comparing(sheBeiJianCe::getTemperature)).get();
        //温度最小值
        sheBeiJianCe Temperaturemin = list4.stream().min(Comparator.comparing(sheBeiJianCe::getTemperature)).get();

        iacMaxmap.put("iacMaxA", iacMaxA.getIac1());
        iacMaxmap.put("iacMaxATime", iacMaxA.getTime());
        iacMaxmap.put("iacMaxB", iacMaxB.getIac2());
        iacMaxmap.put("iacMaxBTime", iacMaxB.getTime());
        iacMaxmap.put("iacMaxC", iacMaxC.getIac3());
        iacMaxmap.put("iacMaxCTime", iacMaxC.getTime());


        iacMinmap.put("iacMinA", iacMinA.getIac1());
        iacMinmap.put("iacMinATime", iacMinA.getTime());
        iacMinmap.put("iacMinB", iacMinB.getIac2());
        iacMinmap.put("iacMinBTime", iacMinB.getTime());
        iacMinmap.put("iacMinC", iacMinC.getIac3());
        iacMinmap.put("iacMinCTime", iacMinC.getTime());

        vacMaxmap.put("vacMaxA", vacMaxA.getVac1());
        vacMaxmap.put("vacMaxATime", vacMaxA.getTime());
        vacMaxmap.put("vacMaxB", vacMaxB.getVac2());
        vacMaxmap.put("vacMaxBTime", vacMaxA.getTime());
        vacMaxmap.put("vacMaxC", vacMaxC.getVac3());
        vacMaxmap.put("vacMaxCTime", vacMaxC.getTime());

        vacMinmap.put("vacMinA", vacMinA.getVac1());
        vacMinmap.put("vacMinATime", vacMinA.getVac1());
        vacMinmap.put("vacMinB", vacMinB.getTime());
        vacMinmap.put("vacMinBTime", vacMinB.getVac2());
        vacMinmap.put("vacMinC", vacMinC.getVac3());
        vacMinmap.put("vacMinCTime", vacMinC.getTime());

        ActivePowerMaxmap.put("PowerMax", PowerMax.getActivePower());
        ActivePowerMaxmap.put("PowerMaxTime", PowerMax.getTime());
        ActivePowerMinmap.put("Powermin", Powermin.getActivePower());
        ActivePowerMinmap.put("PowerminTime", Powermin.getTime());

        FaxMaxmap.put("FacMax", FacMax.getFac1());
        FaxMaxmap.put("FacMaxTime", FacMax.getTime());
        FaxMinmap.put("Facmin", Facmin.getFac1());
        FaxMinmap.put("FacMinTime", Facmin.getTime());

        TemperatureMaxmap.put("TemperatureMax", TemperatureMax.getTemperature());
        TemperatureMaxmap.put("TemperatureMaxTime", TemperatureMax.getTime());
        TemperatureMinmap.put("Temperaturemin", Temperaturemin.getTemperature());
        TemperatureMinmap.put("TemperatureminTime", Temperaturemin.getTime());


        map.put("iacMaxmap", iacMaxmap);
        map.put("iacMinmap", iacMinmap);
        map.put("vacMaxmap", vacMaxmap);
        map.put("vacMinmap", vacMinmap);
        map.put("ActivePowerMaxmap", ActivePowerMaxmap);
        map.put("ActivePowerMinmap", ActivePowerMinmap);
        map.put("FaxMaxmap", FaxMaxmap);
        map.put("FaxMinmap", FaxMinmap);
        map.put("TemperatureMinmap", TemperatureMinmap);
        map.put("TemperatureMaxmap", TemperatureMaxmap);

        //平均值
        for (int i = 0; i < list6.size(); i++) {
            sheBeiJianCe a = list6.get(i);//直接拿这个a去点get或者set就行了
            avg.put("iac1", a.getIac1());
            avg.put("iac2", a.getIac2());
            avg.put("iac3", a.getIac3());
            avg.put("vac1", a.getVac1());
            avg.put("vac2", a.getVac2());
            avg.put("vac3", a.getVac3());
            avg.put("ActivePower", a.getActivePower());
            avg.put("Temperature", a.getTemperature());
            avg.put("fac", a.getFac1());
        }
        map.put("avg", avg);

        return map;
    }


    @Override
    public Object deviceDetail(String plantId) {
        ArrayList<HashMap<String, String>> deviceDetailMapList = new ArrayList<>();
        //根据用户名查询该用户所属的所有电站ID
        String[] plantIds = {plantId};
        //根据电站ID查询所属的所有逆变器SN
        List<String> SNs = hyBtoDeviceListMapper.selectSnByPlantId(plantIds);
        for (String sn : SNs) {
            HashMap<String, String> deviceDetailMap = hyBtoDeviceListMapper.deviceDetail(sn);
            if (deviceDetailMap != null && deviceDetailMap.size() > 0) {
                deviceDetailMapList.add(deviceDetailMap);
            }
        }
        if (deviceDetailMapList.size() > 0) {
            return deviceDetailMapList;
        } else {
            return "该电站的逆变器无详细信息";
        }
    }

    @Override
    public List<HashMap<String,String>> totalEnergyAndPower(String plantId, String date) {
        String tableName = date.replace("-", "");
        List<HashMap<String,String>> totalEnergyAndPowerList = hyBtoDeviceListMapper.totalEnergyAndPower(tableName, plantId, date);
        if (totalEnergyAndPowerList==null || totalEnergyAndPowerList.size()<=0){
            String totalEnergy = hyBtoDeviceListMapper.totalEnergy(plantId);
            HashMap<String, String> totalEnergyMap = new HashMap<>();
            totalEnergyMap.put("totalEnergy", totalEnergy);
            totalEnergyAndPowerList.add(totalEnergyMap);
        }
        return totalEnergyAndPowerList;
    }

    @Override
    public PageInfo<DeviceListDTO> deviceList(DeviceListForm deviceListForm) {
        PageHelper.startPage(deviceListForm.getPage(),deviceListForm.getPageSize());
        List<DeviceListDTO> deviceListDTOS = hyBtoDeviceListMapper.deviceList(deviceListForm.getStatusArray());
        PageInfo<DeviceListDTO> deviceListInfo = new PageInfo<>(deviceListDTOS);
        return deviceListInfo;
    }

}
