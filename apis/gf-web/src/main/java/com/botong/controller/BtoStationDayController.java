package com.botong.controller;

import com.botong.services.service.BtoStationDayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import utils.AjaxResult;


/**
 * 电站日发电量
 *
 * <AUTHOR> @date 2022-08-15 09:45:17
 */
@RestController
@RequestMapping("btostationday")
@Api(tags = "电站日发电量")
@CrossOrigin
public class BtoStationDayController {

    @Autowired
    private BtoStationDayService btoStationDayService;

    @ApiOperation("电站月发电量")
    @GetMapping("StationMothEnergy")
    public AjaxResult StationMothEnergy(){
       return AjaxResult.success("操作成功",btoStationDayService.StationMothEnergy());
    }

    @ApiOperation("电站功率日")
    @GetMapping("powerStationDay")
    public AjaxResult powerStationDay(){
        return AjaxResult.success("查询成功",btoStationDayService.powerStationDay());
    }


}
