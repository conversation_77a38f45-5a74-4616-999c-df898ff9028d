package vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class EfficiencyVo implements Comparable<EfficiencyVo>, Serializable {

    private String plantId;
    private String stationName;
    private String realTimePower;
    private String peakPower;
    private String workEfficiency;

    //覆写compareto()的方法
    @Override
    public int compareTo(EfficiencyVo efficiencyVo) {
        return this.stationName.compareTo(efficiencyVo.getStationName());
    }
}