<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYIncomeStatisticMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.IncomeStatisticDTO" id="IncomeStatistic">
        <result property="plant_id" column="plant_id"/>
        <result property="sn" column="sn"/>
        <result property="name" column="name"/>
        <result property="plant_uid" column="plant_uid"/>
        <result property="today_energy" column="today_energy"/>
        <result property="month_energy" column="month_energy"/>
        <result property="year_energy" column="year_energy"/>
        <result property="time" column="time"/>
        <result property="date" column="date"/>
        <result property="energy" column="energy"/>
    </resultMap>
    <select id="IncomeStatisticsDay" resultMap="IncomeStatistic">
        SELECT
        a.plant_id,
        a.plant_uid,
        name,
        time,
        sum(today_energy) today_energy
        FROM
        `v_station_base` a
        LEFT JOIN (
        SELECT
        plant_id,
        today_energy,
        time
        FROM
        v_device_list aa
        LEFT	JOIN (
        SELECT sn,power,today_energy,time
        FROM ${emm}
        WHERE
        time LIKE '${time2}'

        GROUP BY sn,time
        ORDER BY time desc
        ) bb
        ON aa.device_sn = bb.sn
        GROUP BY
        sn
        ) b ON a.plant_id = b.plant_id
        where
        <foreach collection="plant_id" item="plant_id" separator="or">
            a.plant_id='${plant_id}'
        </foreach>
        GROUP BY a.plant_id
    </select>
    <select id="IncomeStatisticsMonth" resultMap="IncomeStatistic">
        SELECT
        plant_id,
        LEFT ( date, 7 ) time,
        sum(energy) energy
        FROM
        v_day
        WHERE
        date like'${time}' and
        <foreach collection="plant_id" item="plant_id" separator="or">
            plant_id='${plant_id}'
        </foreach>
        GROUP BY
        plant_id

    </select>
    <select id="IncomeStatisticsYear" resultMap="IncomeStatistic">
        SELECT
        plant_id,
        LEFT ( date, 4 ) time,
        sum( energy ) energy
        FROM
        v_day
        WHERE
        date LIKE '${time}'
        AND
        <foreach collection="plant_id" item="plant_id" separator="or">
            plant_id='${plant_id}'
        </foreach>
        GROUP BY
        plant_id
    </select>
    <select id="IncomeStatisticsAllDay" resultMap="IncomeStatistic">
        SELECT
            a.plant_id,
            a.plant_uid,
            name,
            time,
            sum(today_energy) today_energy
        FROM
            `v_station_base` a
            LEFT JOIN (
            SELECT
            plant_id,
            today_energy,
            time
            FROM
            v_device_list aa
            LEFT	JOIN (
            #---
            SELECT sn,power,today_energy,time
            FROM ${emm}
            WHERE
            time LIKE '${time2}'

            GROUP BY sn,time
            ORDER BY time desc
            ) bb
            ON aa.device_sn = bb.sn
            GROUP BY
            sn
            ) b
        ON
            a.plant_id = b.plant_id
        WHERE a.special = '1'
        GROUP BY a.plant_id
    </select>

    <select id="IncomeStatisticsAllMonth" resultMap="IncomeStatistic">
        SELECT
            a.plant_id,
            LEFT ( date, 7 ) time,
            sum( energy ) energy
        FROM
            v_hy a
            LEFT JOIN v_day b ON a.plant_id = b.plant_id
        WHERE
            date LIKE '${time}'
        GROUP BY
            a.plant_id

    </select>

    <select id="IncomeStatisticsAllYear" resultMap="IncomeStatistic">
        SELECT
            a.plant_id,
            LEFT ( date, 4 ) time,
            sum( energy ) energy
        FROM
            v_hy a
            LEFT JOIN v_day b ON a.plant_id = b.plant_id
        WHERE
            date LIKE '${time}'
        GROUP BY
            a.plant_id
    </select>
    <select id="IncomeStatisticsByCondition" resultType="entityDTO.IncomeStatisticsDTO">
        SELECT
            a.plant_id plantId,
            b.plant_uid plantUid,
            b.NAME plantName,
            LEFT ( a.date, ${length} ) time,
            sum( a.energy ) energy
        FROM
            v_day a
                LEFT JOIN v_station_list b ON a.plant_id = b.plant_id
        WHERE
            b.special = '1'
          <if test="plantIds!=null and plantIds.length>0">
              AND  a.plant_id IN
                   <foreach collection="plantIds" item="plantId" index="index" open="(" close=")" separator=",">
                        #{plantId}
                   </foreach>
          </if>
          AND LEFT ( a.date, ${length}) = '${date}'
        GROUP BY
            a.plant_id
    </select>
</mapper>