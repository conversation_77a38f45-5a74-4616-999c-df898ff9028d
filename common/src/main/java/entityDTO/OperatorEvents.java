package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class OperatorEvents implements Serializable {
    //站点名称
    private String name;
    //用户电话
    private Long ownerPhone;
    //地区
    private String city;
    private String address;
    //电站uid
    private String plantUid;
    //运维器sn码
    private String wisdomDevicesn;
    //故障名称
    private String alarmname;
    //故障时间
    private String alarmtime;
    //a点别名
    private String apointname;
    //b点别名
    private String bpointname;
    //c点别名
    private String cpointname;
    //a点电压
    private String apointvoltage;
    //b点电压
    private String bpointvoltage;
    //c点电压
    private String cpointvoltage;
    //d点电压
    private String dpointvoltage;
    //结束事件
    private String endtime;

    /**
     *     故障恢复状态：0：未恢复  1：已恢复
     */
    private String alarmRecoveryStatus;

}
