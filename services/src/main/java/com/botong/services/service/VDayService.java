package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entity.VDay;

import java.util.List;

public interface VDayService extends IService<VDay> {
    List<VDay> selectMonth(String plantId);

    List<VDay> selectMonthAll(String plantId, String date);

    List<VDay> selectYear(String plantId);

    List<VDay> selectYieldDay(String plantId );

    List<VDay> selectYieldDayAll(String plantId , String month);

    List<VDay> selectYieldMonth(String plantId,String month);

    List<VDay> selectYieldMonthAll(String plantId);

    List<VDay> selectYieldYear(String plantId, String year);

}
