package entityDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 电站信息修改表单--实体类
 * <AUTHOR>
 */
@Data
public class UpdateStationInfoForm implements Serializable {
    @ApiModelProperty(value = "电站名称",notes = "电站名称")
    private String stationName;
    @ApiModelProperty(value = "电站ID",notes = "电站ID")
    private String plantId;
    @ApiModelProperty(value = "装机容量",notes = "装机容量")
    private String peakPower;
    @ApiModelProperty(value = "电表编号",notes = "电表编号")
    private String meterId;
}
