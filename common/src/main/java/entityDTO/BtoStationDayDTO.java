package entityDTO;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BtoStationDayDTO implements Serializable {


    private String time;

    private String power;
    /**
     * 电站id
     */
    private String plantId;

    /**
     * 日期
     */
    private Date date;

    /**
     * 发电量
     */
    private Float energy;

    /**
     *
     */
    private Date updateDay;

    private Double Mothenergy;
}
