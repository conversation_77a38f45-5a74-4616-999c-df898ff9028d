package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.VDayMapper;
import com.botong.services.service.VDayService;
import entity.VDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class VDayServiceImpl extends ServiceImpl<VDayMapper, VDay> implements VDayService {

    @Autowired
    private VDayMapper vDayMapper;

    @Override
    public List<VDay> selectMonth(String plantId){
        String format = DateUtil.format(new Date(), "yyyy-MM");
      //  String format = "2022-07";
                String month = format + "%";
        List<VDay> vDays = vDayMapper.selectMonth(month,plantId);
        return vDays;
    }


    @Override
    public List<VDay> selectMonthAll(String plantId, String date){
        String month = date + "%";
        List<VDay> vDays = vDayMapper.selectMonth(month,plantId);
        return vDays;
    }


    @Override
    public List<VDay> selectYear(String plantId) {
        List<VDay> vDays = vDayMapper.selectYear(plantId);
        return vDays;
    }


    @Override
    public List<VDay> selectYieldDay(String plantId ) {
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String date = format + "%";
        String table = DateUtil.format(new Date(), "yyyyMMdd");
        List<VDay> vDays = vDayMapper.selectYield(plantId,date,table);
        return vDays;
    }

    @Override
    public List<VDay> selectYieldDayAll(String plantId , String day1) {

        String[] split = day1.split("-");
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            stringBuffer.append(split[i]);
        }
        String table = stringBuffer.toString();    //yyyyMMdd
        String day = day1 + "%";
        List<VDay> vDays = vDayMapper.selectYield(plantId, day, table);
        return vDays;
    }

    @Override
    public List<VDay> selectYieldMonth(String plantId ,String month) {
        String month1 = month + "%";
        List<VDay> vDays = vDayMapper.selectYieldMonth(plantId , month1);
        return vDays;
    }

    @Override
    public List<VDay> selectYieldMonthAll(String plantId ) {
        String format = DateUtil.format(new Date(), "yyyy");
        String month = format + "%";
        List<VDay> vDays = vDayMapper.selectYieldMonthAll(plantId , month);
        return vDays;
    }

    @Override
    public List<VDay> selectYieldYear(String plantId, String year1) {
        String year = year1 + "%";
        List<VDay> vDays = vDayMapper.selectYieldYear(plantId , year);
        return vDays;
    }
}
