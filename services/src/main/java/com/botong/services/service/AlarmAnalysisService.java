package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.AlarmAnalysis;
import entityDTO.AlarmAnalysisConditionDTO;
import entityDTO.OperatorEvents;
import utils.AjaxResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public interface AlarmAnalysisService extends IService<AlarmAnalysis> {
    PageInfo<AlarmAnalysis> AlarmAnalysisListForm(Integer page, Integer pagesize);

    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery 实时报警——时间区间查询——精确时分秒 plantUid time 非全
    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery time plantUid全
    PageInfo<AlarmAnalysis> AlarmAnalysisTimeIntervalQuery(String startTime, String endTime, int page, int pagesize,String plantUid);

    PageInfo<AlarmAnalysis> AlarmAnalysisTconditionQuery(String startTime, int page, int pagesize, String name, String name1, String sn);


    PageInfo<AlarmAnalysis> AlarmAnalysisPolymerizationAll(String startTime, String endTime, String page, String pageSize);

    //多个
    PageInfo<AlarmAnalysis> AlarmAnalysisPolymerization( String page, String pageSize, String plantUid);

    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarm(String startTime, String endTime, String page, String pageSize,int grade);

    //列表表格
    PageInfo<AlarmAnalysis> AlarmAnalysisListFormplantUid(Integer page, Integer pageSize, String plantUid);

    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll(String startTime, String endTime, String page, String pageSize, int grade, String plantUid);

    List<AlarmAnalysis> AlarmAnalysisTimeIntervalAll(String time1, String time2);

    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll3(Integer page, Integer pageSize,String name1,String startTime,String endTime,String grade);

    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmQuann(String page, String pageSize);

    Object AlarmAnalysisPolymerizationplantUid(String startTime, String endTime, String page, String pageSize, String plantUid);

    Object totalAlarmStatistics();

    Object totalAlarmStatisticsTime(String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIME(String time, String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMETU(String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgeren(String time, String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenyaoxin(String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennyaoxin(String time, String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEstatistics(String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennstatistics(String time, String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEEquipmentStatistics(String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenEquipmentStatistics(String time, String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMELevelStatistics(String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennLevelStatistics(String time, String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEyaoxinlist(String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenlist(String time, String plantUid);

    PageInfo<OperatorEvents> OperatorEventData( Integer page, Integer pageSize,String alarmtime, String city,String endtime);

    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll2(Integer page, Integer pageSize, String name1, String grade);

    PageInfo<AlarmAnalysis> OperatorData(Integer page,Integer pageSize,String startTime, String endTime,String name);

    Object OperatorDataMonth(Integer page, Integer pageSize, String startTime, String endTime, String name);

    AjaxResult AlarmAnalysisByCondition(AlarmAnalysisConditionDTO alarmAnalysisConditionDTO) throws ClassNotFoundException, NoSuchFieldException;

    /**
     * 查询事件-数据对照表
     * @return
     */
    HashMap<String, String[]> getEventMean();
}
