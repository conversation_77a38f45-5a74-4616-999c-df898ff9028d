package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entityDTO.HYAstationList;
import entityDTO.userListLogin;

import java.util.List;

public interface HYAuserlogionService extends IService<userListLogin> {


    int userlogin(String username, String password);

    List<userListLogin> frontPage(String userUid);

    int TotalNumberOfAlarms(String userUid);

    List<userListLogin> TotalNumberOfAlarmsDetails(String userUid);

    List<userListLogin> AlarmNumberClassification(String userUid);

    List<userListLogin> OfflineNumberClassification(String userUid);

    List<userListLogin> frontPageplantid(String userUid);

    List<userListLogin> PowerStationList(Integer a,Integer b);

    List<userListLogin> ifSmartOM(String plantId);


    List<userListLogin> ifSmartOMYueShang(String plantId,String time);
    List<userListLogin> ifSmartOMYueXia(String plantId,String time);
    List<userListLogin> ifSmartOMNianShang(String plantId,String time);
    List<userListLogin> ifSmartOMNianXia(String plantId,String time);

    int updatePassword(String userUid, String password);


    int registeredUser(String cUserName, String cUsertel, String cUserEmail, String updateTime, String delFlag);

    Object versionView();

    List<userListLogin> PowerStationListName(Integer a,Integer b,String name);

    List<userListLogin> ifSmartOMALLShang(String plantId);

    List<userListLogin> ifSmartOMALLXia(String plantId);

    List<HYAstationList> accordingToTiemenergy(int sort, int a, int b);

    Object versionViewipa();
}
