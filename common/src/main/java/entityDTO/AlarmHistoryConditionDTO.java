package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class AlarmHistoryConditionDTO implements Serializable {
    /**
     * 查询条件：开始时间
     */
    private String startTime;
    /**
     * 查询条件：结束时间
     */
    private String endTime;
    /**
     * 页码
     */
    private String page;
    /**
     * 页面大小
     */
    private String pageSize;
    /**
     * 筛选条件1：级别
     */
    private String grade;
    /**
     * 筛选条件2：电站名字符串
     */
    private String stationName;

}
