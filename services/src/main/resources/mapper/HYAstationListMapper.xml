<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYAstationListMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.HYAstationList" id="HYAstationListMap">
        <result property="name" column="name"/>
        <result property="address1" column="address1"/>
        <result property="power" column="power"/>
        <result property="creatEDate" column="creatDate"/>
        <result property="todayenergy" column="todayenergy"/>
        <result property="totalenergy" column="totalenergy"/>
        <result property="plantId" column="plantId"/>
        <result property="wisdomdevicesn" column="wisdomdevicesn"/>
        <result property="peakPower" column="peak_power"/>
    </resultMap>
    <select id="selectListt" resultType="entityDTO.HYAstationList">
        SELECT a.plant_id                              plantId,
               a.address1                              address1,
               a.wisdom_device_sn,
               a.plant_uid                             plantUid,
               a.`name1`                               plantname,
               a.peak_power                            peakPower,
               a.plant_type                            TYPE,
               a.ctrl                                  ctrl,
               a.create_date                           createDate,
               a.status1                               `status`,
               CASE
                   WHEN b.update_time > DATE_SUB(now(), INTERVAL 30 MINUTE) THEN sum(DISTINCT (b.power))
                   ELSE 0 END                          realPower,
               CASE
                   WHEN LEFT(b.update_time, 10) = CURDATE() THEN sum(DISTINCT (b.today_energy))
                   ELSE 0 END                          todayenergy,
               sum(DISTINCT (b.total_energy))          totalenergy,
               CONCAT(LEFT(b.update_time, 15), '0:00') time
        FROM v1_station_device a
                 LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
          AND a.deleted = '0'
        GROUP BY a.plant_id
        ORDER BY a.create_date DESC
    </select>
    <select id="selectStationInfo" parameterType="string" resultType="vo.StationInfoVo">
        SELECT plant_id,
               `name`,
               peak_power,
               orentation,
               create_date,
               address1,
               owner_phone
        FROM bto_station_list
        WHERE special = '1'
          AND plant_id = #{stationId}
    </select>
    <select id="map" resultType="vo.LonLatVo">
        SELECT plant_id,
               longitude,
               latitude
        FROM v_station_base
        WHERE plant_id = #{stationId}
    </select>
    <select id="getDeviceIdByStationId" resultType="java.lang.String">
        SELECT sn
        FROM v1_station_device
        WHERE plant_id = #{stationId}
    </select>

    <select id="selectStaionByCondition" resultType="entityDTO.HYAstationList">
        SELECT
        a.plant_id plantId,
        a.address1 address1,
        a.wisdom_device_sn wisdomdevicesn,
        a.plant_uid plantUid,
        a.`name1` plantname,
        a.peak_power,
        a.town,
        a.plant_type plant_type,
        a.ctrl ctrl,
        a.create_date createDate,
        a.status1 `status`,
        CASE
        WHEN MAX(b.update_time) > DATE_SUB( now(), INTERVAL 30 MINUTE ) THEN sum(DISTINCT ( b.power ))
        ELSE 0 END realPower,
        CASE
        WHEN LEFT ( MAX(b.update_time), 10 ) = CURDATE() THEN sum(DISTINCT( b.today_energy ))
        ELSE 0 END todayenergy,
        SUM(DISTINCT ( b.total_energy )) totalenergy,
        CONCAT( LEFT ( MAX(b.update_time), 15 ), '0:00' ) time
        FROM
        v1_station_device a
        LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
        <where>
            a.special='1' AND a.deleted='0'
            <if test="plantType!=null">
                AND a.plant_type = #{plantType}
            </if>
            <if test="stationName!=null and stationName!=''">
                AND a.name1 LIKE CONCAT('%',#{stationName},'%')
            </if>
            <if test="town!=null and town!=''">
                AND a.town = #{town}
            </if>
            <if test="status!=null and status!='' ">
                AND a.status1 = #{status}
            </if>
            <if test="minPeakPower!=null and minPeakPower!='' and maxPeakPower!=null and maxPeakPower!=''">
                AND (a.peak_power BETWEEN #{minPeakPower} AND #{maxPeakPower})
            </if>
        </where>
        GROUP BY a.plant_id
        ORDER BY a.create_date DESC
    </select>
    <select id="getDevicePvInfoBySN" resultType="entity.DevicePvInfo">
        SELECT a.plant_id    plantId,
               a.orentation  orientation,
               b.sn          SN,
               b.pv1_dc1     pv1OnDc1,
               b.pv1_dc2     pv1OnDc2,
               b.pv1_dc3     pv1OnDc3,
               b.pv1_dc4     pv1OnDc4,
               b.pv2_dc1     pv2OnDc1,
               b.pv2_dc2     pv2OnDc2,
               b.pv2_dc3     pv2OnDc3,
               b.pv2_dc4     pv2OnDc4,
               b.pv3_dc1     pv3OnDc1,
               b.pv3_dc2     pv3OnDc2,
               b.pv3_dc3     pv3OnDc3,
               b.pv3_dc4     pv3OnDc4,
               b.pv4_dc1     pv4OnDc1,
               b.pv4_dc2     pv4OnDc2,
               b.pv4_dc3     pv4OnDc3,
               b.pv4_dc4     pv4OnDc4,
               b.pv5_dc1     pv5OnDc1,
               b.pv5_dc2     pv5OnDc2,
               b.pv5_dc3     pv5OnDc3,
               b.pv5_dc4     pv5OnDc4,
               b.pv6_dc1     pv6OnDc1,
               b.pv6_dc2     pv6OnDc2,
               b.pv6_dc3     pv6OnDc3,
               b.pv6_dc4     pv6OnDc4,
               b.pv7_dc1     pv7OnDc1,
               b.pv7_dc2     pv7OnDc2,
               b.pv7_dc3     pv7OnDc3,
               b.pv7_dc4     pv7OnDc4,
               b.pv8_dc1     pv8OnDc1,
               b.pv8_dc2     pv8OnDc2,
               b.pv8_dc3     pv8OnDc3,
               b.pv8_dc4     pv8OnDc4,
               b.pv9_dc1     pv9OnDc1,
               b.pv9_dc2     pv9OnDc2,
               b.pv9_dc3     pv9OnDc3,
               b.pv9_dc4     pv9OnDc4,
               b.pv10_dc1     pv10OnDc1,
               b.pv10_dc2     pv10OnDc2,
               b.pv10_dc3     pv10OnDc3,
               b.pv10_dc4     pv10OnDc4,
               b.pv11_dc1     pv11OnDc1,
               b.pv11_dc2     pv11OnDc2,
               b.pv11_dc3     pv11OnDc3,
               b.pv11_dc4     pv11OnDc4,
               b.pv12_dc1     pv12OnDc1,
               b.pv12_dc2     pv12OnDc2,
               b.pv12_dc3     pv12OnDc3,
               b.pv12_dc4     pv12OnDc4,
               b.update_time updateTime
        FROM bto_station_list a
                 LEFT JOIN bto_device_modules b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
          AND sn = #{SN}
    </select>
    <select id="getDeviceInfoByPlantId" resultType="java.util.HashMap">
        SELECT special,
               plant_id   plantId,
               `name`     stationName,
               orentation orientation
        FROM bto_station_list
        WHERE plant_id = #{plantId}
    </select>
    <select id="selectCompanyId" resultType="java.util.HashMap">
        SELECT c_user_uid  userUid,
               c_user_name userName
        FROM v_company
    </select>
    <select id="deviceModel" resultType="java.util.HashMap">
        SELECT Manufacturer,
               type,
               model
        FROM bto_device_type
    </select>

    <update id="updateDevicePvInfo" parameterType="entity.DevicePvInfo">
        UPDATE bto_device_modules SET
        pv1_dc1 = #{devicePvInfo.pv1OnDc1},
        pv1_dc2 = #{devicePvInfo.pv1OnDc2},
        pv1_dc3 = #{devicePvInfo.pv1OnDc3},
        pv1_dc4 = #{devicePvInfo.pv1OnDc4},
        pv2_dc1 = #{devicePvInfo.pv2OnDc1},
        pv2_dc2 = #{devicePvInfo.pv2OnDc2},
        pv2_dc3 = #{devicePvInfo.pv2OnDc3},
        pv2_dc4 = #{devicePvInfo.pv2OnDc4},
        pv3_dc1 = #{devicePvInfo.pv3OnDc1},
        pv3_dc2 = #{devicePvInfo.pv3OnDc2},
        pv3_dc3 = #{devicePvInfo.pv3OnDc3},
        pv3_dc4 = #{devicePvInfo.pv3OnDc4},
        pv4_dc1 = #{devicePvInfo.pv4OnDc1},
        pv4_dc2 = #{devicePvInfo.pv4OnDc2},
        pv4_dc3 = #{devicePvInfo.pv4OnDc3},
        pv4_dc4 = #{devicePvInfo.pv4OnDc4},
        pv5_dc1 = #{devicePvInfo.pv5OnDc1},
        pv5_dc2 = #{devicePvInfo.pv5OnDc2},
        pv5_dc3 = #{devicePvInfo.pv5OnDc3},
        pv5_dc4 = #{devicePvInfo.pv5OnDc4},
        pv6_dc1 = #{devicePvInfo.pv6OnDc1},
        pv6_dc2 = #{devicePvInfo.pv6OnDc2},
        pv6_dc3 = #{devicePvInfo.pv6OnDc3},
        pv6_dc4 = #{devicePvInfo.pv6OnDc4},
        pv7_dc1 = #{devicePvInfo.pv7OnDc1},
        pv7_dc2 = #{devicePvInfo.pv7OnDc2},
        pv7_dc3 = #{devicePvInfo.pv7OnDc3},
        pv7_dc4 = #{devicePvInfo.pv7OnDc4},
        pv8_dc1 = #{devicePvInfo.pv8OnDc1},
        pv8_dc2 = #{devicePvInfo.pv8OnDc2},
        pv8_dc3 = #{devicePvInfo.pv8OnDc3},
        pv8_dc4 = #{devicePvInfo.pv8OnDc4},
        pv9_dc1 = #{devicePvInfo.pv9OnDc1},
        pv9_dc2 = #{devicePvInfo.pv9OnDc2},
        pv9_dc3 = #{devicePvInfo.pv9OnDc3},
        pv9_dc4 = #{devicePvInfo.pv9OnDc4},
        pv10_dc1 = #{devicePvInfo.pv10OnDc1},
        pv10_dc2 = #{devicePvInfo.pv10OnDc2},
        pv10_dc3 = #{devicePvInfo.pv10OnDc3},
        pv10_dc4 = #{devicePvInfo.pv10OnDc4},
        pv11_dc1 = #{devicePvInfo.pv11OnDc1},
        pv11_dc2 = #{devicePvInfo.pv11OnDc2},
        pv11_dc3 = #{devicePvInfo.pv11OnDc3},
        pv11_dc4 = #{devicePvInfo.pv11OnDc4},
        pv12_dc1 = #{devicePvInfo.pv12OnDc1},
        pv12_dc2 = #{devicePvInfo.pv12OnDc2},
        pv12_dc3 = #{devicePvInfo.pv12OnDc3},
        pv12_dc4 = #{devicePvInfo.pv12OnDc4},
        <foreach collection="devicePvInfo.kValueMap.entrySet()" item="value" index="key" separator=",">
            ${key} = #{value}
        </foreach>
        WHERE sn = #{devicePvInfo.SN};
    </update>
    <!--校验SN是否存在-->
    <select id="isdeviceListExist" resultType="java.lang.Integer">
        select count(1)
        from bto_device_list
        where device_sn = #{deviceSN};
    </select>
    <select id="isdeviceModulesExist" resultType="java.lang.Integer">
        select count(1)
        from bto_device_modules
        where sn = #{sn};
    </select>
    <select id="deviceList" resultType="java.util.HashMap">
        SELECT a.plant_id     plantId,
               a.device_sn    deviceSN,
               CASE
                   WHEN b.update_time > DATE_SUB(now(), INTERVAL 30 MINUTE) THEN b.power
                   ELSE 0 END realTimePower,
               CASE
                   WHEN LEFT(b.update_time, 10) = CURDATE() THEN b.today_energy
                   ELSE 0 END todayEnergy,
               CASE
                   WHEN YEAR(b.update_time) = YEAR(now()) THEN b.year_energy
                   ELSE 0 END yearEnergy,
               b.total_energy totalEnergy,
               b.update_time  updateTime
        FROM bto_device_list a
                 LEFT JOIN bto_device_total b ON a.device_sn = b.device_sn
        WHERE a.plant_id = #{plantId}
        GROUP BY a.device_sn
    </select>
    <insert id="addDevicePvInfo">
        INSERT INTO bto_device_modules( plant_id,
        sn,
        capacity,
        pv1_dc1,
        pv1_dc2,
        pv1_dc3,
        pv1_dc4,
        pv2_dc1,
        pv2_dc2,
        pv2_dc3,
        pv2_dc4,
        pv3_dc1,
        pv3_dc2,
        pv3_dc3,
        pv3_dc4,
        pv4_dc1,
        pv4_dc2,
        pv4_dc3,
        pv4_dc4,
        pv5_dc1,
        pv5_dc2,
        pv5_dc3,
        pv5_dc4,
        pv6_dc1,
        pv6_dc2,
        pv6_dc3,
        pv6_dc4,
        pv7_dc1,
        pv7_dc2,
        pv7_dc3,
        pv7_dc4,
        pv8_dc1,
        pv8_dc2,
        pv8_dc3,
        pv8_dc4,
        pv9_dc1,
        pv9_dc2,
        pv9_dc3,
        pv9_dc4,
        pv10_dc1,
        pv10_dc2,
        pv10_dc3,
        pv10_dc4,
        pv11_dc1,
        pv11_dc2,
        pv11_dc3,
        pv11_dc4,
        pv12_dc1,
        pv12_dc2,
        pv12_dc3,
        pv12_dc4,
        k1,
        k2,
        k3,
        k4,
        k5,
        k6,
        k7,
        k8,
        k9,
        k10,
        k11,
        k12,
        k13,
        k14,
        k15,
        k16,
        k17,
        k18,
        k19,
        k20,
        k21,
        k22,
        create_date) values (
        #{addDeviceForm.plantId},
        #{addDeviceForm.SN},
        #{addDeviceForm.capacity},
        #{addDeviceForm.pv1OnDc1},
        #{addDeviceForm.pv1OnDc2},
        #{addDeviceForm.pv1OnDc3},
        #{addDeviceForm.pv1OnDc4},
        #{addDeviceForm.pv2OnDc1},
        #{addDeviceForm.pv2OnDc2},
        #{addDeviceForm.pv2OnDc3},
        #{addDeviceForm.pv2OnDc4},
        #{addDeviceForm.pv3OnDc1},
        #{addDeviceForm.pv3OnDc2},
        #{addDeviceForm.pv3OnDc3},
        #{addDeviceForm.pv3OnDc4},
        #{addDeviceForm.pv4OnDc1},
        #{addDeviceForm.pv4OnDc2},
        #{addDeviceForm.pv4OnDc3},
        #{addDeviceForm.pv4OnDc4},
        #{addDeviceForm.pv5OnDc1},
        #{addDeviceForm.pv5OnDc2},
        #{addDeviceForm.pv5OnDc3},
        #{addDeviceForm.pv5OnDc4},
        #{addDeviceForm.pv6OnDc1},
        #{addDeviceForm.pv6OnDc2},
        #{addDeviceForm.pv6OnDc3},
        #{addDeviceForm.pv6OnDc4},
        #{addDeviceForm.pv7OnDc1},
        #{addDeviceForm.pv7OnDc2},
        #{addDeviceForm.pv7OnDc3},
        #{addDeviceForm.pv7OnDc4},
        #{addDeviceForm.pv8OnDc1},
        #{addDeviceForm.pv8OnDc2},
        #{addDeviceForm.pv8OnDc3},
        #{addDeviceForm.pv8OnDc4},
        #{addDeviceForm.pv9OnDc1},
        #{addDeviceForm.pv9OnDc2},
        #{addDeviceForm.pv9OnDc3},
        #{addDeviceForm.pv9OnDc4},
        #{addDeviceForm.pv10OnDc1},
        #{addDeviceForm.pv10OnDc2},
        #{addDeviceForm.pv10OnDc3},
        #{addDeviceForm.pv10OnDc4},
        #{addDeviceForm.pv11OnDc1},
        #{addDeviceForm.pv11OnDc2},
        #{addDeviceForm.pv11OnDc3},
        #{addDeviceForm.pv11OnDc4},
        #{addDeviceForm.pv12OnDc1},
        #{addDeviceForm.pv12OnDc2},
        #{addDeviceForm.pv12OnDc3},
        #{addDeviceForm.pv12OnDc4},
        <foreach collection="addDeviceForm.kValueMap.entrySet()" item="value" index="key" separator=",">
            #{value}
        </foreach>
        ,now())
    </insert>
    <insert id="addDeviceListInfo">
        INSERT INTO bto_device_list( device_id, user_uid, plant_id, datalogger_sn, device_sn, device_address
                                   , manu_facturer, model, type, special, create_time)
        values (#{addDeviceForm.deviceId}, #{addDeviceForm.userUid},
                #{addDeviceForm.plantId}, #{addDeviceForm.dataloggerSN},
                #{addDeviceForm.deviceSN}, #{addDeviceForm.deviceAddress},
                #{addDeviceForm.manuFacturer},
                #{addDeviceForm.model}, #{addDeviceForm.type},
                #{addDeviceForm.special}, now());
    </insert>


</mapper>