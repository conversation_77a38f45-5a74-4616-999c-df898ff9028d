<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.UserListMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoUserList" id="AlarmAnalysisMap">
        <result property="delFlag" column="delFlag"/>
        <result property="updateDate" column="updateDate"/>
        <result property="updateTime" column="updateTime"/>
        <result property="createTime" column="createTime"/>
        <result property="cuserUid" column="cuserUid"/>
        <result property="cuserName" column="cuserName"/>
        <result property="cuserRegtime" column="cuserRegtime"/>
        <result property="cuserId" column="cuserId"/>
        <result property="cuserTel" column="cuserTel"/>
        <result property="cuserEmail" column="cuserEmail"/>
    </resultMap>

    <resultMap type="entityDTO.userRole" id="userRoleMap">
        <result property="cUserId" column="cUserId"/>
        <result property="roleId" column="roleId"/>
    </resultMap>
    <resultMap type="entityDTO.returnPrimaryId" id="returnPrimaryIdMap">
        <result property="plantId" column="plantId"/>
        <result property="plantUid" column="plantUid"/>
        <result property="sn" column="sn"/>
    </resultMap>
    <insert id="registeredUser">
        INSERT INTO bto_user_list (c_user_id, c_user_uid, c_user_name, c_user_tel, del_flag, c_user_regtime,
                                   update_date, c_user_email, update_time, create_time, password, special, enginer_id,
                                   password)
        VALUES (1, #{uuid}, #{cUserName}, #{cUsertel}, 0, #{regtiTime}, #{regtiTime}, #{cUserEmail}, #{regtiTime},
                #{regtiTime}, 123456, #{special}, #{engnierId}, ${password})
    </insert>
    <update id="updatePassword">
        UPDATE bto_user_list
        SET password=#{password}
        WHERE c_user_uid = #{userUid};
    </update>

    <select id="selectListt" resultMap="AlarmAnalysisMap">
        select *
        from bto_user_list
    </select>

    <!--根据用户id查询角色id集合-->
    <select id="UserRoleQuery" resultType="java.lang.Object">
        select role_id roleId
        from user_role
        where c_user_id = #{userUid}
    </select>
    <select id="getPermissionId" resultType="entityDTO.roleresoure">
        SELECT rr.resoure_id
        FROM user_role ur
                 LEFT JOIN role_resoure rr ON ur.role_id = rr.role_id
        WHERE ur.c_user_id = #{userUid}
    </select>
    <select id="getroleResourceId" resultType="entityDTO.roleresoure">
        select resoure_id
        from role_resoure
        where role_id = #{roleId}
    </select>
    <select id="listResource" resultType="entityDTO.Resource">
        select id, path, name
        from resource
    </select>
    <select id="listRole" resultType="entityDTO.Role">
        select *
        from role
    </select>
    <select id="returnPrimaryId" resultType="entityDTO.returnPrimaryId">
        SELECT
        plant_id plantId,
        plant_uid plantUid,
        sn sn
        FROM
        v1_station_device
        WHERE
        <if test="plantId!=null and plantId!=''">
            plant_id = '${plantId}'
        </if>
        <if test="plantUid!=null and plantUid!='' ">
            plant_uid = '${plantUid}'
        </if>
    </select>


</mapper>