package entityDTO;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class userListLogin implements Serializable {



    /**
     * 用户id
     */
    private Integer cUserId;

    /**
     * 用户uid
     */
    private String cUserUid;

    /**
     * 用户姓名
     */
    private String cUserName;

    /**
     * 用户电话
     */
    private String cUserTel;

    /**
     * 帐号状态（0：正常，1：已删除）
     */
    private Integer delFlag;

    /**
     * 用户注册日期
     */
    private Date cUserRegtime;

    /**
     * 用户档案更新时间
     */
    private Date updateDate;

    /**
     * 用户注册邮箱
     */
    private String cUserEmail;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Date createTime;

    //密码
    private String password;
    //电站名称
    private String name;
    private String peakPower;
    //电站数量
    private int stationNum;
    //日发电量
    private String todayenergy;
    //月发电量
    private String monthenergy;
    //年发电量
    private String yearenergy;
    //总发电量
    private String totalenergy;
    //告警总数
    private int alertNum;
    //报警代码
    private int alarmCode;
    //报警信息
    private String alarmMessage;
    //结束时间
    private String endTime;
    //级别
    private int grade;
    //告警事件名称
    private String mean;
    //创建时间
    private String startTime;
    //电站id
    private Long plantId;
    //电站数量
    private int plantIdNum;
    //电站uid
    private String plantUid;

    private String sn;
    private String sumpower;
    private String createDate;
    private String address1;
    private int jishu;
    private int status;

    private String wisdomdevicesn;
    private String t;
    private String pvele;
    private String useele;
    private String selfele;
    private String sellele;
    private String buyele;
    private String time;
}
