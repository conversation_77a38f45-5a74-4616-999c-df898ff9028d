package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.sheBeiJianCe;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceMonitorMapper extends BaseMapper<sheBeiJianCe> {
    List<sheBeiJianCe> collectionMaxMin(@Param("snName") String snName, @Param("tableNme") String tableNme, @Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> collectioAvg(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> Devicecurrent(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);
}
