package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.deviceRealtimeDataMapper;
import com.botong.services.service.deviceRealtimeDataService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.BtoDeviceRealTimeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class deviceRealtimeDataServiceImpl extends ServiceImpl<deviceRealtimeDataMapper, BtoDeviceRealTimeDTO> implements deviceRealtimeDataService {

    @Autowired
    private deviceRealtimeDataMapper deviceRealtimeDataMapper;
    @Override
    public PageInfo<BtoDeviceRealTimeDTO> selectDeviceRealtimeData(String time, String sn, Integer page, Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page),Integer.valueOf(pageSize));
        List<BtoDeviceRealTimeDTO> btoDeviceRealTimeLunbos=deviceRealtimeDataMapper.selectdeviceRealtimeData(time,sn,page,pageSize);
        PageInfo<BtoDeviceRealTimeDTO> btoDeviceRealTimeDTOPageInfo=new PageInfo<>(btoDeviceRealTimeLunbos);
        return btoDeviceRealTimeDTOPageInfo;
    }
}
