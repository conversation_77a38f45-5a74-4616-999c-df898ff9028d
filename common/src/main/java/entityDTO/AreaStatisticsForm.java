package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 区域统计-日月年数据--页面表单实体
 */
@Data
@ApiModel("区域统计-日月年数据--页面表单实体")
public class AreaStatisticsForm implements Serializable {
    /**
     * 传递时间
     */
    @ApiModelProperty(value = "传递时间",notes = "传递时间(可传递日月年字符串)")
    String time;
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称",notes = "区域名称")
    String town;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码",notes = "页码")
    Integer page;
    /**
     * 页面大小
     */
    @ApiModelProperty(value = "页面大小",notes = "页面大小")
    Integer pageSize;
    /**
     * 总条目数
     */
}
