package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entity.BtoOperation;
import entity.BtoOperationDay;

import java.util.HashMap;
import java.util.List;


/**
 * 运维器数据
 *
 * <AUTHOR> @date 2022-08-19 14:24:48
 */
public interface HYBtoOperationDayService extends IService<BtoOperationDay> {


    List<BtoOperationDay> getUserBuySellInfoByMonth(String plantUid, String month);

    List<BtoOperationDay> getUserBuySellInfoByYear(String plantUid, String year);

    List<BtoOperationDay> getUserBuySellALLInfo(String plantUid);

    HashMap<String, String> WisdomDeviceInformation(String plantId);
}

