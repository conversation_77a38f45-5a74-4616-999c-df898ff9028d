package com.botong.services.utils;

import com.dd.plist.NSDictionary;
import com.dd.plist.NSString;
import com.dd.plist.PropertyListParser;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR>
 * @description: 苹果ipa版本
 * @date 2022/11/10 14:51
 */
public class ReadApkUtil {

    /**
     * 读取ipa
     */
    public static String readIPA(String ipaURL) throws Exception {
            String ios;
            File file = new File(ipaURL);
            InputStream is = new FileInputStream(file);
            ZipInputStream zipIns = new ZipInputStream(is);
            ZipEntry ze;
            InputStream infoIs = null;
            NSDictionary rootDict = null;
            while ((ze = zipIns.getNextEntry()) != null) {
                if (!ze.isDirectory()) {
                    String name = ze.getName();
                    if (null != name &&
                            name.toLowerCase().contains(".app/info.plist")) {
                        ByteArrayOutputStream _copy = new
                                ByteArrayOutputStream();
                        int chunk = 0;
                        byte[] data = new byte[1024];
                        while (-1 != (chunk = zipIns.read(data))) {
                            _copy.write(data, 0, chunk);
                        }
                        infoIs = new ByteArrayInputStream(_copy.toByteArray());
                        rootDict = (NSDictionary) PropertyListParser.parse(infoIs);
                        break;
                    }
                }
            }
            // 应用版本名
            NSString parameters = (NSString) rootDict.objectForKey("CFBundleShortVersionString");
            ios=parameters.toString();
        return ios;
    }
}