package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entityDTO.BtoDeviceBindingDTO;
import entityDTO.BtoDeviceEnginerDTO;
import entityDTO.UpdateDeviceFormDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

@Service
public interface BtoDeviceEnginerService extends IService<BtoDeviceEnginerDTO> {
    /**
     *     根据工程师ID查询所属所有电站的名字和电站ID
     *     param enginerId
     */
    String getStationUIDByName(String name);

    /**
     * 绑定逆变器，并返回结果
     * @param btoDeviceBindingDTO
     * @return
     */
    Integer setDevice(BtoDeviceBindingDTO btoDeviceBindingDTO);

    List<BtoDeviceEnginerDTO> getDeviceBySN(String sn);

    /**
     * 根据电站名或手机号码查询逆变器SN和IMEI
     * @param value
     * @param type
     * @return
     */
    List<HashMap<String, String>> getDeviceCode(String value, String type);

    /**
     * 更新逆变器
     * @param updateDeviceFormDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    String updateDevice(UpdateDeviceFormDTO updateDeviceFormDTO) throws IllegalAccessException;
}

