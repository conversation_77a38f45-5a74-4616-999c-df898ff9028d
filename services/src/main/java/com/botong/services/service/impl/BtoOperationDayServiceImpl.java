package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.botong.services.mapper.BtoOperationDayMapper;
import com.botong.services.service.BtoOperationDayService;
import entity.BtoOperationDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;


/**
 * 运维器数据 impl
 *
 * <AUTHOR> @date 2022-08-19 14:24:48
 */
@Service
public class BtoOperationDayServiceImpl extends ServiceImpl<BtoOperationDayMapper, BtoOperationDay> implements BtoOperationDayService {

    @Autowired
    private BtoOperationDayMapper btoOperationDayMapper;
    @Override
    public List<BtoOperationDay> getUserBuySellInfoByMonth(String plantUid, String month) {
        List<BtoOperationDay> userBuySellInfoByMonth = btoOperationDayMapper.getUserBuySellInfoByMonth(plantUid, month);
        return userBuySellInfoByMonth;
    }

    @Override
    public List<BtoOperationDay> getUserBuySellInfoByYear(String plantUid, String year) {
        List<BtoOperationDay> getUserBuySellInfoByYear = btoOperationDayMapper.getUserBuySellInfoByYear(plantUid, year);
        return getUserBuySellInfoByYear;
    }
    @Override
    public List<BtoOperationDay> getUserBuySellALLInfo(String plantUid) {
        List<BtoOperationDay> getUserBuySellALLInfo = btoOperationDayMapper.getUserBuySellALLInfo(plantUid);
        return getUserBuySellALLInfo;
    }
}
