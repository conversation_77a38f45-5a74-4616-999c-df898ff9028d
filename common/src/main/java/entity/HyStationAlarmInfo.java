package entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class HyStationAlarmInfo {
    /**
     * 机器类型
     */
    private String machineType;
    /**
     * 告警开始时间
     */
    private String startTime;
    /**
     * 告警结束时间
     */
    private String updateTime;
    /**
     * 告警信息（英文）
     */
    private String alarmMessage;
    /**
     * 告警状态
     */
    private String status;
    /**
     * 告警信息（中文）
     */
    private String mean;
    /**
     * 电站Id
     */
    private String plantId;
    /**
     * 电站Uid
     */
    private String plantUid;
    /**
     * 电站名
     */
    private String stationName;
    /**
     * 用户名
     */
    private String userName;
}
