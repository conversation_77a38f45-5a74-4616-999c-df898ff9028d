<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.BtoOperationDayMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoOperationDay" id="btoOperationDayMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="wisdomDeviceSn" column="wisdom_device_sn"/>
        <result property="pvTel" column="pv_tel"/>
        <result property="loadTel" column="load_tel"/>
        <result property="autoTel" column="auto_tel"/>
        <result property="sellTel" column="sell_tel"/>
        <result property="buyTel" column="buy_tel"/>
        <result property="time" column="date"/>
        <result property="chartDateType" column="chart_date_type"/>
        <result property="chartMonth" column="chart_month"/>
    </resultMap>
    <select id="getUserBuySellInfoByMonth" resultMap="btoOperationDayMap">
        SELECT plant_uid,
               wisdom_device_sn,
               pv_tel,
               load_tel,
               auto_tel,
               sell_tel,
               buy_tel,
               time date,
               chart_date_type
        FROM bto_operation_day
        WHERE plant_uid = '${plantUid}'
          AND time LIKE '%${month}%'
        group by time
        order by time ASC
    </select>
    <select id="getUserBuySellInfoByYear" resultMap="btoOperationDayMap">
        SELECT plant_uid,
               wisdom_device_sn,
               sum(pv_tel)   pv_tel,
               sum(load_tel) load_tel,
               sum(auto_tel) auto_tel,
               sum(sell_tel) sell_tel,
               sum(buy_tel)  buy_tel,
               left(time, 7) date,
               chart_date_type
        FROM bto_operation_day
        WHERE plant_uid = '${plantUid}'
          AND time LIKE '%${year}%'
        group by date;#年
    </select>
    <select id="getUserBuySellALLInfo" resultMap="btoOperationDayMap">
        SELECT plant_uid,
               wisdom_device_sn,
               sum(pv_tel)    pv_tel,
               sum(load_tel) load_tel,
               sum(auto_tel) auto_tel,
               sum(sell_tel)  sell_tel,
               sum(buy_tel)  buy_tel,
               LEFT(time, 4) date,
               chart_date_type
        FROM bto_operation_day
        WHERE plant_uid = '${plantUid}'
        GROUP BY date
        ORDER BY date;#总
    </select>
    <select id="WisdomDeviceInformation" resultType="java.util.HashMap">
        SELECT
            wisdom_device_sn sn,
            update_time updateTime,
            apv,
            bpv,
            cpv,
            dpv,
            wisdom_type wisdomType
        FROM
            v_operation
        WHERE
            plant_id = '${plantId}'
        order by update_time desc
        limit 1
    </select>



</mapper>