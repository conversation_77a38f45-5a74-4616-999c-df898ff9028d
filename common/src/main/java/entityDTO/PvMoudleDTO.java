package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/4 14:22
 */
@Data
@ApiModel("光伏组件&和电网实体-APP")
public class PvMoudleDTO implements Serializable {
    @ApiModelProperty("PV1电流")
    private String ipv1;
    @ApiModelProperty("PV2电流")
    private String ipv2;
    @ApiModelProperty("PV3电流")
    private String ipv3;
    @ApiModelProperty("PV4电流")
    private String ipv4;
    @ApiModelProperty("PV5电流")
    private String ipv5;
    @ApiModelProperty("PV6电流")
    private String ipv6;
    @ApiModelProperty("PV7电流")
    private String ipv7;
    @ApiModelProperty("PV8电流")
    private String ipv8;
    @ApiModelProperty("PV9电流")
    private String ipv9;
    @ApiModelProperty("PV10电流")
    private String ipv10;
    @ApiModelProperty("PV11电流")
    private String ipv11;
    @ApiModelProperty("PV12电流")
    private String ipv12;

    @ApiModelProperty("PV1电压")
    private String vpv1;
    @ApiModelProperty("PV2电压")
    private String vpv2;
    @ApiModelProperty("PV3电压")
    private String vpv3;
    @ApiModelProperty("PV4电压")
    private String vpv4;
    @ApiModelProperty("PV5电压")
    private String vpv5;
    @ApiModelProperty("PV6电压")
    private String vpv6;
    @ApiModelProperty("PV7电压")
    private String vpv7;
    @ApiModelProperty("PV8电压")
    private String vpv8;
    @ApiModelProperty("PV9电压")
    private String vpv9;
    @ApiModelProperty("PV10电压")
    private String vpv10;
    @ApiModelProperty("PV11电压")
    private String vpv11;
    @ApiModelProperty("PV12电压")
    private String vpv12;
    @ApiModelProperty("AC1电流")
    private String iac1;
    @ApiModelProperty("AC2电流")
    private String iac2;
    @ApiModelProperty("AC3电流")
    private String iac3;
    @ApiModelProperty("AC1电压")
    private String vac1;
    @ApiModelProperty("AC2电压")
    private String vac2;
    @ApiModelProperty("AC3电压")
    private String vac3;
    @ApiModelProperty("频率1")
    private String fac1;
    @ApiModelProperty("频率2")
    private String fac2;
    @ApiModelProperty("频率3")
    private String fac3;
    @ApiModelProperty("组串1")
    private String pv1;
    @ApiModelProperty("组串2")
    private String pv2;
    @ApiModelProperty("组串3")
    private String pv3;
    @ApiModelProperty("组串4")
    private String pv4;
    @ApiModelProperty("组串5")
    private String pv5;
    @ApiModelProperty("组串6")
    private String pv6;
    @ApiModelProperty("组串7")
    private String pv7;
    @ApiModelProperty("组串8")
    private String pv8;
    @ApiModelProperty("组串9")
    private String pv9;
    @ApiModelProperty("组串10")
    private String pv10;
    @ApiModelProperty("组串11")
    private String pv11;
    @ApiModelProperty("组串12")
    private String pv12;
    @ApiModelProperty("数据更新时间")
    private String dateTime;


}
