<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.VDayMapper">
    <resultMap type="entity.VDay" id="vDayMap">
        <result property="plantId" column="plant_id"/>
        <result property="date" column="date"/>
        <result property="energy" column="energy"/>
        <result property="updateDay" column="update_day"/>
    </resultMap>

    <select id="selectMonth" resultType="entity.VDay">
        SELECT
            plant_id,
            date,
            energy
        FROM
            v_day
        WHERE
            date LIKE #{month}
          AND plant_id = #{plantId}
        ORDER BY
            date;
    </select>


    <select id="selectYear" resultType="entity.VDay">
        SELECT
            plant_id,
            LEFT ( date, 7 ) `month`,
            sum( energy ) sum
        FROM
            v_day
        WHERE
            plant_id = #{plantId}
        GROUP BY
            `month`
        ORDER BY
            date;
    </select>
    
    <select id="selectYield" resultType="entity.VDay">
        SELECT
            plant_id,
            count( sn ) r,
            sum( today_energy ) sum_energy ,
            time
        FROM
            (
            SELECT
            plant_id,
            a.sn,
            today_energy,
            time
            FROM
            v1_station_device a
            LEFT JOIN bto_device_${table} b ON a.sn = b.sn
            WHERE
            time LIKE #{date}
            AND plant_id = #{plantId}
            GROUP BY
            SN,
            time
            ORDER BY
            time DESC
            ) qw
        GROUP BY
            time,
            plant_id

    </select>


    <select id="selectYieldAll" resultType="entity.VDay">
        SELECT
            plant_id,
            LEFT ( date, 10 ) `day`,
            energy
        FROM
            v_day
        WHERE
            plant_id = #{plantId}
          AND date LIKE #{day};
    </select>


    <select id="selectYieldMonth" resultType="entity.VDay">
        SELECT
            plant_id,
            left(date,10) `month`,
            energy
        FROM
            v_day
        where plant_id = #{plantId}
          and date like #{month}
    </select>

    <select id="selectYieldMonthAll" resultType="entity.VDay">
        SELECT
            plant_id,
            left(date,7) `month`,
            sum(energy) `sum`
        FROM
            v_day
        where plant_id = #{plantId}
          and date like #{month}
        GROUP BY `month`;
    </select>


    <select id="selectYieldYear" resultType="entity.VDay">
        SELECT
            plant_id,
            left(date,7) `year`,
            sum(energy) sum
        FROM
            v_day
        where plant_id = #{plantId}
          and date like #{year}
        group by `year`

    </select>


</mapper>