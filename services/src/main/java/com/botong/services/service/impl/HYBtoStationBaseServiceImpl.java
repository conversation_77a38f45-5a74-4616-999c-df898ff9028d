package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYBtoStationBaseMapper;
import com.botong.services.service.HYBtoDeviceService;
import com.botong.services.service.HYBtoStationBaseService;
import entity.BtoStationBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import vo.ElectricityStatisticsVo;
import vo.StationBaseVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class HYBtoStationBaseServiceImpl extends ServiceImpl<HYBtoStationBaseMapper, BtoStationBase> implements HYBtoStationBaseService {
    @Autowired
    private HYBtoDeviceService hyBtoDeviceService;
    @Override
    public Object getAllStation() {
        return baseMapper.selectCount(null);
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB",key = " 'getDeforestation' ",cacheManager = "HY_Web_Cache")
    public HashMap<String, Object> getDeforestation() {
        ElectricityStatisticsVo electricityStatisticsVo = hyBtoDeviceService.inverterElectricityInfo().get(0);
        float allElectricity = Float.parseFloat(electricityStatisticsVo.getAllElectricity());
        float dayElectricity = Float.parseFloat(electricityStatisticsVo.getDayElectricity());
        HashMap<String, Object> hashMap = new HashMap<>();
        //节约标准煤（万吨）
        hashMap.put("coal", 0.0001229 * (allElectricity / 10));
        //日CO2减排（吨）
        hashMap.put("dayEmission", dayElectricity / 10 * 0.00009971220715594546);
        //累计CO2减排（万吨）
        hashMap.put("allEmission", 0.00009969593412803057 * allElectricity);
        //累计减少砍伐（万棵）
        hashMap.put("felling", 0.0001772885734520353 * allElectricity);
        return hashMap;
    }
    @Override
    public ArrayList<StationBaseVo> municipalNumber() {
        ArrayList<StationBaseVo> stationBaseVos = new ArrayList<>();
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("city", "state");
        queryWrapper.groupBy("city");
        for (BtoStationBase btoStationBase : baseMapper.selectList(queryWrapper)) {
            queryWrapper.clear();
            StationBaseVo stationBaseVo = new StationBaseVo();
            stationBaseVo.setNames(btoStationBase.getCity());
            stationBaseVo.setProvince(btoStationBase.getState());
            queryWrapper.eq("city", btoStationBase.getCity());
            stationBaseVo.setNums(baseMapper.selectCount(queryWrapper));
            stationBaseVos.add(stationBaseVo);
        }
        return stationBaseVos;
    }


    @Override
    public Object getProvincialStations() {
        List<BtoStationBase> btoStationBases = new ArrayList<BtoStationBase>();
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("special", 1);
            btoStationBases = baseMapper.selectList(queryWrapper);
        return btoStationBases;
    }
    @Override
    public BtoStationBase getLatitudeAndLongitude(String plantId) {
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("longitude", "latitude");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper);
    }
}
