package entityDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlarmAnalysis implements Serializable {
    private String sn;
    private String startTime;
    private String updateTime;
    private String alarmMessage;
    private String mean;
    private int STATUS;
    private String plantId;
    private String plantUid;
    private String name1;
    private String NAME;
    private Long tel;
    private int grade;
    private int NUM;
    private String q;
    private int yaoxin;
    private int nb;
    private String name;
    private String city;
    private String wisdomdevicesn;
    private String pvele;
    private String useele;
    private String selfele;
    private String sellele;
    private String buyele;
    private String time;
    private String address;
    private String stationName;
    private String createdate;

}
