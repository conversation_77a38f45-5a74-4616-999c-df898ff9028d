package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entity.BtoStationList;
import entityDTO.AllStationList;
import entityDTO.BTOstationListDTO;
import entityDTO.StationLunBo;
import entityDTO.UpdateStationInfoForm;
import org.springframework.stereotype.Service;
import vo.StationListVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public interface HYBtoStationListService extends IService<BtoStationList> {

    PageInfo<AllStationList> ListOfTotalPowerStations();


    //光伏监控中心——树
    List<BtoStationList> GFmonitoringCenter();

//    PageInfo<AllStationList> ListOfTotalPowerStationsstationName(String page, String pageSize, String stationName);

    Object ListOfTotalPowerStationsPhone(String plantUid);

    List<StationListVo> getAllStationName();

    String getIdByUId(String plantId);

    String getInstalledCapacityById(String plantId);

    /**
     * 电站数量及状态列表
     * @return
     */
    HashMap<String,Integer> stationAndDeviceNum();

    /**
     * 总装机容量及日-月-总发电量
     * @return
     */
    HashMap<String,Double> stationAllenergyAndAllpeakPower();

    /**
     * 电站列表--修改电站信息
     * @param updateStationInfoForm
     * @return
     */
    String updateStationInfo(UpdateStationInfoForm updateStationInfoForm);
}
