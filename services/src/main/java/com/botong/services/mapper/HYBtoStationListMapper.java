package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import entity.BtoStationList;
import entityDTO.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface HYBtoStationListMapper extends BaseMapper<BtoStationList> {

    Integer Stationonline();

    Integer alarmNum(@Param("curTime") String curTime);

    List<BtoStationList> selectBtoStationList();

//    BTOstationListDTO StationAllenergy(@Param("tableName") String tableName);

    Page<siteCarouselPage> selectPages(Page<siteCarouselPage> page, QueryWrapper<Object> wrapper);

    /**
     * 电站列表 & 按条件筛选
     */
//    List<AllStationList> ListOfTotalPowerStations(@Param("time") String time, @Param("time2")String time2);

    List<AllStationList> ListOfTotalPowerStationsByName();

    Object ListOfTotalPowerStationsPhone(@Param("time")String time,@Param("plantUid") String plantUid);

    Integer selectCountAlarm(@Param("alarmTime") String alarmTime);

//    HashMap<String,Double> stationAllenergy();

    Double stationTodayEnergy();

    Double stationMonthEnergy();

    Double stationTotalEnergy();

    Integer alarmOperatorNum();

    Integer selectSelfInspectionNum();

    /**
     * 电站列表--修改电站信息(bto_station_base、bto_station_list)
     * @param updateStationInfoForm
     * @return
     */
    Integer updateStationBaseInfo(@Param("updateStationInfoForm") UpdateStationInfoForm updateStationInfoForm);

    Integer updateStationListInfo(@Param("updateStationInfoForm") UpdateStationInfoForm updateStationInfoForm);
}
