<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYBtoStationListMapper">

    <resultMap type="entityDTO.stationNum" id="stationNumDTO">
        <result property="offline" column="offline"/>
        <result property="online" column="online"/>
        <result property="alert" column="alert"/>
        <result property="normal" column="normal"/>
    </resultMap>

    <resultMap type="entityDTO.AllStationList" id="AllStationList">
        <result property="plantId" column="plantId"/>
        <result property="distribution" column="distribution"/>
        <result property="status" column="status"/>
        <result property="name" column="name"/>
        <result property="stationName" column="stationName"/>
        <result property="peakPower" column="peakPower"/>
        <result property="deviceCount" column="deviceCount"/>
        <result property="plantType" column="plantType"/>
        <result property="time" column="time"/>
        <result property="createDate" column="createDate"/>
        <result property="realTimePower" column="realTimePower"/>
        <result property="workEfficiency" column="workEfficiency"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="monthEnergy" column="monthEnergy"/>
        <result property="yearEnergy" column="yearEnergy"/>
        <result property="hoursperday" column="hoursperday"/>
        <result property="totalEnergy" column="totalEnergy"/>
        <result property="city" column="city"/>
        <result property="sn" column="sn"/>
        <result property="plantUid" column="plantUid"/>
        <result property="ownerPhone" column="ownerPhone"/>
        <result property="owner" column="owner"/>
        <result property="createdate" column="createdate"/>
        <result property="address1" column="address1"/>
    </resultMap>
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoStationList" id="btoStationListMap">
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="userUid" column="user_uid"/>
        <result property="status" column="status"/>
        <result property="country" column="country"/>
        <result property="city" column="city"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="peakPower" column="peak_power"/>
        <result property="createDate" column="create_date"/>
        <result property="currentPower" column="current_power"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="planType" column="plan_type"/>
        <result property="installer" column="installer"/>
        <result property="operator" column="operator"/>
        <result property="owner" column="owner"/>
        <result property="ownerPhone" column="owner_phone"/>
        <result property="wisdomDeviceSn" column="wisdom_device_sn"/>
        <result property="imageUrl" column="image_url"/>
        <result property="locale" column="locale"/>
        <result property="plantNo" column="plant_no"/>
        <result property="ownerEmail" column="owner_email"/>
        <result property="ownerName" column="owner_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.BTOstationListDTO" id="btoStationListMapDTO">
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="userUid" column="user_uid"/>
        <result property="status" column="status"/>
        <result property="country" column="country"/>
        <result property="city" column="city"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="peakPower" column="peak_power"/>
        <result property="createDate" column="create_date"/>
        <result property="currentPower" column="current_power"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="planType" column="plan_type"/>
        <result property="installer" column="installer"/>
        <result property="operator" column="operator"/>
        <result property="owner" column="owner"/>
        <result property="ownerPhone" column="owner_phone"/>
        <result property="wisdomDeviceSn" column="wisdom_device_sn"/>
        <result property="imageUrl" column="image_url"/>
        <result property="locale" column="locale"/>
        <result property="plantNo" column="plant_no"/>
        <result property="ownerEmail" column="owner_email"/>
        <result property="ownerName" column="owner_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="monthEnergy" column="monthEnergy"/>
        <result property="totalEnergy" column="totalEnergy"/>
    </resultMap>
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.siteCarouselPage" id="siteCarouselPage">
        <result property="stationName" column="stationName"/>
        <result property="stationAdress" column="stationAdress"/>
        <result property="OutputPower" column="OutputPower"/>
        <result property="stationCapacity" column="stationCapacity"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="AllEnergy" column="AllEnergy"/>
        <result property="CO2" column="CO2"/>
        <result property="creatTime" column="creatTime"/>
        <result property="status" column="status"/>
        <result property="offlineTime" column="offlineTime"/>

    </resultMap>
    <select id="alarmNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE special = '1'
          AND status = '2'
    </select>
    <select id="Stationonline" resultType="java.lang.Integer">
        SELECT count(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE special = '1'
          AND status IN (1, 2, 3)
    </select>
    <select id="selectBtoStationList" resultType="entity.BtoStationList">
        select plant_id plantId, plant_uid plantUid, user_id userId, user_uid userUid, name
        from bto_station_list
    </select>
    <select id="selectPages" resultType="entityDTO.siteCarouselPage">
    </select>
    <!--    <select id="ListOfTotalPowerStations" resultMap="AllStationList">-->
    <!--        SELECT a.plant_id         plantId,-->
    <!--               a.plant_uid        plantuId,-->
    <!--               a.NAME             NAME,-->
    <!--               a.create_date      createDate,-->
    <!--               a.peak_power       peakPower,-->
    <!--               COUNT(a.device_sn) deviceCount,-->
    <!--               a.plant_type       TYPE,-->
    <!--               b.TIME             TIME,-->
    <!--               SUM(POWER)         realTimePower,-->
    <!--               SUM(today_energy)  todayEnergy,-->
    <!--               SUM(total_energy)  totalEnergy,-->
    <!--               a.city             city-->
    <!--        FROM `v1_base_device` a-->
    <!--                 LEFT JOIN (-->
    <!--            SELECT plant_id,-->
    <!--                   sn,-->
    <!--                   POWER,-->
    <!--                   today_energy,-->
    <!--                   total_energy,-->
    <!--                   TIME-->
    <!--            FROM v_device_list aa-->
    <!--                     LEFT JOIN (SELECT sn, POWER, today_energy, total_energy, TIME-->
    <!--                                FROM bto_device_${time}-->
    <!--                                WHERE TIME LIKE '${time2}'-->
    <!--                                GROUP BY SN, TIME-->
    <!--                                ORDER BY TIME DESC) bb ON aa.device_sn = bb.sn-->
    <!--            GROUP BY sn-->
    <!--        ) b ON a.plant_id = b.plant_id-->
    <!--        WHERE a.special = '1'-->
    <!--        GROUP BY a.plant_id-->
    <!--        ORDER BY a.create_date DESC-->
    <!--    </select>-->
    <select id="ListOfTotalPowerStationsByName" resultMap="AllStationList">
        SELECT a.plant_id plantId,
               a.plant_uid plantuId,
               a.`name1` stationName,
               a.peak_power peakPower,
               a.plant_type TYPE,
               a.create_date createDate,
               a.city city,
               a.status1 `status`,
               COUNT(DISTINCT (a.device_sn)) deviceCount,
                    CASE WHEN MAX(b.update_time) > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                        THEN SUM(DISTINCT (b.power)) ELSE 0 END realTimePower,
                    CASE WHEN LEFT(MAX(b.update_time), 10) = CURDATE()
                        THEN SUM(DISTINCT (b.today_energy)) ELSE 0 END todayEnergy,
                    CASE WHEN MONTH(MAX(b.update_time)) = MONTH(NOW())
                        THEN SUM(DISTINCT (b.month_energy)) ELSE 0 END monthEnergy,
                    CASE WHEN YEAR(MAX(b.update_time)) = YEAR(NOW())
                        THEN SUM(DISTINCT (b.year_energy)) ELSE 0 END yearEnergy,
                    CASE WHEN c.time = CURDATE() AND apv &lt; 189 AND bpv &lt; 189 AND cpv &lt; 189
                        THEN '市电停电' WHEN c.time = CURDATE() AND apv > 189 AND bpv &lt; 189 AND cpv &lt; 189
                        THEN '失压开关故障' WHEN c.time = CURDATE() AND apv > 189 AND bpv > 189 AND cpv &lt; 189
                        THEN '过流开关故障' WHEN c.time = CURDATE() AND status1=1 AND apv &lt; 189
                        THEN'市电采样异常' WHEN c.time = CURDATE() AND status1=1 AND apv > 189 AND bpv &lt; 189
                        THEN'失压开关采样异常' WHEN c.time = CURDATE() AND status1=1 AND apv > 189 AND bpv > 189 AND cpv &lt; 189
                        THEN'过流开关采样异常' ELSE '正常' END distribution,
                    SUM(DISTINCT (b.total_energy)) totalEnergy,
                    CONCAT(LEFT(MAX(b.update_time), 15), '0:00') TIME
                    FROM v1_station_device a
                 LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
                 LEFT JOIN bto_operation c ON a.plant_uid = c.plant_uid
        WHERE a.special = '1' AND a.deleted = '0'
        GROUP BY a.plant_id
        ORDER BY a.create_date DESC
    </select>
    <select id="ListOfTotalPowerStationsPhone" resultType="java.util.HashMap">
        SELECT
            a.plant_id plantId,
            a.plant_uid plantUid,
            a.`name`,
            a.`owner`,
            a.owner_Phone ownerPhone,
            a.create_date time,
            a.peak_power peakPower,
            a.address1 address1,
            a.orentation,
            max( b.update_time ) time1,
            sum( b.power ) power,
            sum( b.today_energy ) today_energy,
            sum( b.total_energy ) totalEnergy
        FROM
            bto_station_list a
                LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
        WHERE
            a.plant_uid = #{plantUid}
    </select>
    <select id="selectCountAlarm" resultType="java.lang.Integer">
            SELECT count(*)
            FROM v_device_event a
                     LEFT JOIN v1_station_device b ON a.sn = b.sn
            WHERE b.special = '1'
              AND a.status = '0'
              AND start_time LIKE #{alarmTime}
              AND alarm_message not like 'PV%exception';
    </select>
    <!--    <select id="stationAllenergy" resultType="java.util.HashMap">-->
    <!--        SELECT-->
    <!--            sum( today_energy ) todayEnergy,-->
    <!--            sum( month_energy ) monthEnergy,-->
    <!--            sum( total_energy ) totalEnergy-->
    <!--        FROM-->
    <!--            bto_device_energy a-->
    <!--                LEFT JOIN v1_station_device b ON a.sn = b.device_sn-->
    <!--        WHERE-->
    <!--            b.special = '1'-->
    <!--          AND LEFT ( date_time, 10 )= curdate();-->
    <!--    </select>-->

    <select id="stationTodayEnergy" resultType="java.lang.Double">
        SELECT sum(today_energy)
        FROM bto_device_total
        WHERE special = '1'
          AND LEFT(update_time, 10) = curdate();#日
    </select>
    <select id="stationMonthEnergy" resultType="java.lang.Double">
        SELECT sum(month_energy)
        FROM bto_device_total
        WHERE special = '1'
          AND LEFT(update_time, 7) = left(curdate(), 7);#月
    </select>
    <select id="stationTotalEnergy" resultType="java.lang.Double">
        SELECT sum(total_energy)
        FROM bto_device_total
        WHERE special = '1';#总
    </select>
    <select id="alarmOperatorNum" resultType="java.lang.Integer">
        SELECT COUNT(a.alarm_name)
        FROM bto_operation_fault a
                 LEFT JOIN bto_station_list b ON a.plant_uid = b.plant_uid
        WHERE b.special = '1'
          AND LEFT(alarm_time, 10) = CURDATE()
          AND end_time > DATE_SUB(now(), INTERVAL 5 MINUTE)
    </select>
    <select id="selectSelfInspectionNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE special = '1'
          AND status = '3'
    </select>
    <update id="updateStationBaseInfo">
        UPDATE bto_station_base
        SET `name`=#{updateStationInfoForm.stationName},
            peak_power=#{updateStationInfoForm.peakPower}
        where plant_id = #{updateStationInfoForm.plantId}
    </update>
    <update id="updateStationListInfo">
        UPDATE bto_station_list
        SET `name`=#{updateStationInfoForm.stationName},
            peak_power=#{updateStationInfoForm.peakPower},
            meter_id=#{updateStationInfoForm.meterId}
        where plant_id = #{updateStationInfoForm.plantId}
    </update>

</mapper>