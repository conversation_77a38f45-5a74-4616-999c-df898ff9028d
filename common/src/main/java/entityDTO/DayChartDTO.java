package entityDTO;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/4 16:42
 */
@Data
@AllArgsConstructor
public class DayChartDTO implements Serializable {
    private static final long serialVersionUID = 4932960887141434288L;
    private String inverterSN;
    private String power;
    private String todayEnergy;
    private String dateTime;

}
