package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYARespondentsMapper;
import com.botong.services.service.HYABtoDeviceService;
import com.botong.services.service.HYARespondentsService;
import com.botong.services.service.HYAstationListService;
import entity.BtoDevice;
import entity.BtoUserList;
import entityDTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import utils.AjaxResult;
import vo.OperatorInfoVO;
import vo.StationInfoVo;
import vo.StationListVo;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/10/14 20:05
 */
@Service
public class HYARespondentsServiceImpl extends ServiceImpl<HYARespondentsMapper, HYARespondentsDTO> implements HYARespondentsService {

    @Autowired
    private HYARespondentsMapper hyaRespondentsMapper;


    @Autowired
    private HYABtoDeviceService btoDeviceService;

    @Autowired
    private HYAstationListService hyAstationListService;

    @Override
    public List<HYARespondentsDTO> SelEctRespondentsList(String plantId) {
        return hyaRespondentsMapper.SelEctRespondentsList();
    }

    //随机生成第二个id

    @Override
    public AjaxResult AddEctRespondentsList(String userUid,String username,String author,String plantId) {
        String uuid2 = UUID.randomUUID().toString().toUpperCase();
        boolean flag = false;
        String msg1 = "";
        String msg2 = "";
        String msg3 = "";
        String msg4 = "";
        int status1 =  0;
        int status2 =  0;
        int status3 =  0;
        int status = 0;
        username=username+"heyuanapp";  //注册子账号用户名默认带heyuanapp
        //用户创建时间
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String CuserRegtime= simpleDateFormat.format(date);
        HYABtoUserListDTO hyaBtoUserListDTO = new HYABtoUserListDTO();
        //需要从电站表中取数据——电站信息
        List<HYABtoUserListDTO> userlist=hyaRespondentsMapper.SelUserList(plantId);
        //查询用户表中是否存在该用户名
        hyaBtoUserListDTO = hyaRespondentsMapper.seltest(username);
        //查询该用户是否是河源管理员
                int i = hyaRespondentsMapper.selectRepeatHyAdmin();

        int plantCount = hyaRespondentsMapper.selectplantIdRepeat(plantId,username);
        //判断子账号表中该管理员子账号数量：大于等于2:表示超过数量限制
        if(hyaRespondentsMapper.selectCountAccountByUserId(userUid)<2) {
            //查询用户表中是否存在该用户名，不存在则插入用户表
            if (hyaBtoUserListDTO == null) {
                //把子账号插入用户表
                hyaRespondentsMapper.AddUserList(uuid2, username, CuserRegtime);
                flag = true;
                msg1 = "用户表插入成功";
                status1 =1;

            }
            if(hyaRespondentsMapper.selectRepeat(username)<=0){
                //将用户信息存入子账户表
                for (HYABtoUserListDTO hyaBtoUserListDTO1 : userlist) {
                    hyaBtoUserListDTO1.setRespondentId(uuid2);
                    hyaBtoUserListDTO1.setCUserName(hyaBtoUserListDTO1.getCUserName());
                    hyaBtoUserListDTO1.setName(username);
                    hyaBtoUserListDTO1.setCUserUid(userUid);
                    hyaBtoUserListDTO1.setAuthor(Integer.valueOf(author));
                    hyaBtoUserListDTO1.setCreateTime(CuserRegtime);
                    hyaRespondentsMapper.AddUserPondents(hyaBtoUserListDTO1);
                    msg2 = "子账户表插入成功";
                    status2 = 2;
                }
            }
            if (plantCount<=0){
            //插入关系表
            for (HYABtoUserListDTO hyaBtoUserListDTO2:userlist){
                if(flag == false){
                    String cUserUid = hyaBtoUserListDTO.getCUserUid();
                    hyaBtoUserListDTO2.setPlantId(plantId);
                    hyaBtoUserListDTO2.setRespondentId(cUserUid);
                    hyaBtoUserListDTO2.setCreateTime(CuserRegtime);
                    hyaRespondentsMapper.AddUsersonPondents(hyaBtoUserListDTO2);
                }else {
                    hyaBtoUserListDTO2.setPlantId(plantId);
                    hyaBtoUserListDTO2.setRespondentId(uuid2);
                    hyaBtoUserListDTO2.setCreateTime(CuserRegtime);
                    hyaRespondentsMapper.AddUsersonPondents(hyaBtoUserListDTO2);
                }
                msg3 = "关系表插入成功";
                status3 = 4;
            }
            }else{
                msg4 = "该电站已被绑定";
            }
                status =status1 + status2 +status3;
                return AjaxResult.success(msg1+msg2+msg3+msg4,status);

        }else{
                Integer j = hyaRespondentsMapper.selectRepeat(username);
                if(j<=0){
                    status =status1 + status2 +status3;
                    return AjaxResult.error("用户数量超出限制",status);

                }else {
                    if(plantCount<=0){
                    for (HYABtoUserListDTO hyaBtoUserListDTO2:userlist){
                        if(flag == false){
                            String cUserUid = hyaBtoUserListDTO.getCUserUid();
                            hyaBtoUserListDTO2.setPlantId(plantId);
                            hyaBtoUserListDTO2.setRespondentId(cUserUid);
                            hyaBtoUserListDTO2.setCreateTime(CuserRegtime);
                            hyaRespondentsMapper.AddUsersonPondents(hyaBtoUserListDTO2);
                        }else {
                            hyaBtoUserListDTO2.setPlantId(plantId);
                            hyaBtoUserListDTO2.setRespondentId(uuid2);
                            hyaBtoUserListDTO2.setCreateTime(CuserRegtime);
                            hyaRespondentsMapper.AddUsersonPondents(hyaBtoUserListDTO2);
                        }
                        msg3 = "关系表插入成功";
                        status3 = 4;
                    }
                    }else{
                        status = status1 + status2 + status3;
                        return AjaxResult.error("该子账号已注册,且电站已存在"+msg1+msg2+msg3,status);
                    }
                    status =status1 + status2 +status3;
                    return AjaxResult.success("该子账号已注册"+"但是呢，"+msg1+msg2+msg3,status);

            }

        }
//        return AjaxResult.success(msg1+msg2+msg3,status);
    }

    @Override
    public int loginSelsubAccount(String userName) {
        List<stationListName> S=  hyaRespondentsMapper.loginSelsubAccount(userName);
        if (S.size()!=0){
            return 1;
        }else {
            return 0;
        }
    }

    @Override
    public List<HYARespondentsDTO> visitorList(String plantId) {
        return hyaRespondentsMapper.visitorList(plantId);
    }

    @Override
    public int checkSubAccount(String userName,String plantId,String userUid) {
        List<BtoUserList> list= hyaRespondentsMapper.selUser(userName);
        List<BtoUserList> list2= hyaRespondentsMapper.selUser2(plantId,userUid);
        List<BtoUserList> list3= hyaRespondentsMapper.selUser3(plantId,userUid);//用户名存在但没有绑定电站
        if (hyaRespondentsMapper.checkSubAccount(userName,plantId)!=null) {
            return 1;
        } else if (list.size()==1 && list2.size()==1){
            return 2;
        }else if (hyaRespondentsMapper.checkSubAccount(userName,plantId)==null) {
            return 0;
        }else if (list3.size()!=1){
            return 3;
        }else {
            return 666;
        }
    }

    @Override
    public Integer checkSubAccountNum(String userName,String accountName) {
        List<BtoUserList> LIST1 = hyaRespondentsMapper.checkSubAccountNum2(userName);//检查子账号数量
        List<BtoUserList> LIST2 = hyaRespondentsMapper.checkSubAccountNum3(accountName);//子账号是否存在
        List<BtoUserList> LIST3 = hyaRespondentsMapper.checkSubAccountNum4(accountName);//检查子账号id
        List<BtoUserList> LIST5=hyaRespondentsMapper.checkSubAccountNum(userName);
        String getid = null;
        List<BtoUserList> LIST4 = hyaRespondentsMapper.checkSubAccountNum5(getid, accountName);//并且是否绑定该电站
        if (LIST1.size() >= 2 && LIST2 == null && LIST4.size()!=0) {
            if (LIST5.size() >= 2){
                return 2;
            }
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public int removePowerStation(String plantId, String userUid,String password) {
        List<HYABtoUserListDTO> list= hyaRespondentsMapper.selPassword(userUid,password);//校验用户密码
        if ( (list.size()==1)) {
            if (hyaRespondentsMapper.removePowerStation(plantId, userUid) == null) {
                return 1;
            }
        } else if ((list.size()!=1)) {
                if (hyaRespondentsMapper.removePowerStation(plantId, userUid) == null) {
                    return 0;
                }
        }
            return 66;
    }


    @Override
    public int selUser(String userName) {
        List<BtoUserList> list= hyaRespondentsMapper.selUser(userName);
        if (list.size()==1){
            return 2;
        }
        return 2;
    }

    @Override
    public Object selectSubStationList(String userUid) {
        ArrayList<StationListVo> stationListVos = new ArrayList<>();
        List<String> plantIds = hyaRespondentsMapper.selectSubStationList(userUid);
        for (String plantId : plantIds) {
            List<String> sns = btoDeviceService.selectDeviceByStation(plantId);
            StationInfoVo stationInfoVo = hyAstationListService.selectStationInfo(plantId);
            for (String sn : sns) {
                StationListVo stationListVo = new StationListVo();
                BtoDevice electricityBySnToOne = btoDeviceService.getElectricityBySnToOne(sn);
                if (electricityBySnToOne!=null){
                    stationListVo.setPlantId(plantId);
                    stationListVo.setAllElectricity(electricityBySnToOne.getTotalEnergy());
                    stationListVo.setCurrentPower(electricityBySnToOne.getPower());
                    stationListVo.setDayElectricity(electricityBySnToOne.getTodayEnergy());
                    stationListVo.setAddress(stationInfoVo.getAddress1());
                    stationListVo.setName(stationInfoVo.getName());
                    stationListVos.add(stationListVo);
                }

            }
        }
        return stationListVos;
    }

    @Override
    @Cacheable(cacheNames = "hYSubAccDayMonthAll",key =" 'hYSubAccDayMonthAll_'+#p0",cacheManager = "HY_APP_Cache")
    public HashMap<String, String> hYSubAccDayMonthAll(String userUid) {
        Date date=new Date();
        //当前时间-10分钟
        Date near_date = new Date(date.getTime() - 10 * 60 * 1000);
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //当前时间-10分钟截取前15位，例如 “2022-10-24 10:1%”
        String nearTime=simpleDateFormat.format(near_date).substring(0, 15).concat("%");
        //当前时间截取前10位，例如 “2022-10-24%”
        String curTime = simpleDateFormat.format(date).substring(0, 10).concat("%");
        //表名后缀格式化
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyyMMdd");
        //做处理截取为8位作表名，例如“20221024”
//        String tableName= simpleDateFormat2.format(near_date);
        HashMap<String, String> map = new HashMap<>();
        //总电站数量
        String allStationNum = hyaRespondentsMapper.StationNum(userUid);
        //在线电站数量
        String onlineStationNum = hyaRespondentsMapper.powerStationOnline(userUid);
        //告警电站数量
        String countSn = hyaRespondentsMapper.AlarmPowerStation(userUid, curTime);
        // 总装机容量
        String sumPeakPower = hyaRespondentsMapper.TotalInstalledCapacity(userUid);
        //获取告警运维器数量
        Integer warnOperatorNum =hyaRespondentsMapper.getWarnOperatorNum(userUid);
        //告警记录数
        Integer warnRecords = hyaRespondentsMapper.AlarmInverter(userUid, curTime);
        warnRecords += warnOperatorNum;

        //离线电站数
        Integer offlineStation=Integer.parseInt(allStationNum)-Integer.parseInt(onlineStationNum);
        //正常电站数
        Integer normalStation=Integer.parseInt(onlineStationNum)-warnRecords;
        //总电站数
        map.put("allStationNum",allStationNum);
        //电站在线
        map.put("onlineStationNum",onlineStationNum);
        //告警电站
        map.put("warnStationNum",countSn);
        //总装机容量
        map.put("sumPeakPower",sumPeakPower);
        //告警逆变器
        map.put("warnRecords", String.valueOf(warnRecords));
        //离线
        map.put("offlineStationNum", String.valueOf(offlineStation));
        //正常
        map.put("normalStationNum", String.valueOf(normalStation));
        return  map;
    }

    @Override
    public String StationNum(String userUid) {
        return hyaRespondentsMapper.StationNum(userUid);
    }

    @Override
    public String powerStationOnline(String userUid) {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String day=simpleDateFormat.format(date);
        String day2=simpleDateFormat2.format(date);
        //传日期对应的表
        day="bto_device_"+day;
        day2=day2+"%";
        return hyaRespondentsMapper.powerStationOnline(userUid);
    }

    @Override
    public String AlarmPowerStation(String userUid) {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String day2=simpleDateFormat2.format(date);
        day2=day2+"%";
        return hyaRespondentsMapper.AlarmPowerStation(userUid,day2);
    }

    @Override
    public Integer AlarmInverter(String userUid) {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String day2=simpleDateFormat2.format(date);
        day2=day2+"%";
        return hyaRespondentsMapper.AlarmInverter(userUid,day2);
    }

    @Override
    public String TotalInstalledCapacity(String userUid) {
        return hyaRespondentsMapper.TotalInstalledCapacity(userUid);
    }

    @Override
    @Cacheable(cacheNames = "SubaccountPowerPlantAlarm",key =" 'TotalNumberOfAlarmsDetails_'+#p0",cacheManager = "HY_APP_Cache")
    public HashMap<String,Object> SubaccountPowerPlantAlarm(String userUid) {
        HashMap<String,Object> resultMap = new HashMap<>();
        Date date=new Date();
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String day2=simpleDateFormat2.format(date);
        day2=day2+"%";
        List<stationListName> alarmStationList = hyaRespondentsMapper.SubaccountPowerPlantAlarm(userUid, day2);
        ArrayList<OperatorInfoVO> alarmOperatorList = hyaRespondentsMapper.OperatorAlarmInformationList(userUid);
        resultMap.put("alarmStationList",alarmStationList);
        resultMap.put("alarmOperatorList",alarmOperatorList);
        return resultMap;
    }

    @Override
    public List<stationListName> stationListAccsucess(String userUid) {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyyMMdd");
        String day=simpleDateFormat2.format(date);
        //传日期对应的表
        day="bto_device_"+day;
        return hyaRespondentsMapper.stationListAccsucess(userUid,day);


    }

    @Override
    @Cacheable(cacheNames = "hYSubAccDayMonthAll2",key =" 'hYSubAccDayMonthAll2_'+#p0",cacheManager = "HY_APP_Cache")
    public HashMap<String,Double> hYSubAccDayMonthAll2(String userUid) {
        HashMap<String,Double> energyMap= new HashMap<>();
        Double dayElectricity = hyaRespondentsMapper.getDayElectricity(userUid);
        Double monthElectricity = hyaRespondentsMapper.getMonthElectricity(userUid);
        Double allElectricity = hyaRespondentsMapper.getAllElectricity(userUid);
        energyMap.put("dayElectricity",dayElectricity);
        energyMap.put("monthElectricity",monthElectricity);
//        energyMap.put("yearElectricity",yearElectricity);
        energyMap.put("allElectricity",allElectricity);
        return  energyMap;
    }


    @Override
    @Cacheable(cacheNames = "selectSubStationList",key =" 'selectSubStationList_'+#p0+'sort_'+#p1",cacheManager = "HY_APP_Cache")
    public HashMap<String, Object> selectSubStationList(String userUid, Integer sortWith) {

        HashMap<String, Object> hashMap = new HashMap<>();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String time = simpleDateFormat.format(new Date());

        List<StationListVo> stationListVos = baseMapper.selectSubStationList2(userUid, time, sortWith);
        hashMap.put("list", stationListVos);
        return hashMap;
    }

    @Override
    public List<NewHYAstationList> filterSubStationList(String userUid, StationConditionDTO stationConditionDTO) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String time = simpleDateFormat.format(new Date());
//        String status = stationConditionDTO.getStatus();
//        try {
//            switch (status){
//                case "在线": status = "1";
//                    break;
//                case "离线": status = "0";
//                    break;
//                default: break;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println("status为Null");
//            status = "";
//        }
//        stationConditionDTO.setStatus(status);
        List<NewHYAstationList> newHYAstationLists = baseMapper.filterSubStationList2(userUid, stationConditionDTO, time);
        return newHYAstationLists;
    }

}
