package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entityDTO.HYARespondentsDTO;
import entityDTO.NewHYAstationList;
import entityDTO.StationConditionDTO;
import entityDTO.stationListName;
import utils.AjaxResult;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/10/14 20:05
 */
public interface HYARespondentsService extends IService<HYARespondentsDTO> {

    List<HYARespondentsDTO> SelEctRespondentsList(String plantId);

    AjaxResult AddEctRespondentsList(String userUid, String username, String author, String plantId);


    int loginSelsubAccount(String userName);

    List<HYARespondentsDTO> visitorList(String plantId);

    int checkSubAccount(String userName,String plantId,String userUid);

    Integer checkSubAccountNum(String userName,String accountName);

    int removePowerStation(String plantId, String userUid,String password);

    Object selectSubStationList(String userUid);

    int selUser(String userName);

    HashMap<String, String> hYSubAccDayMonthAll(String userUid);

    String StationNum(String userUid);

    String powerStationOnline(String userUid);

    String AlarmPowerStation(String userUid);

    Integer AlarmInverter(String userUid);

    String TotalInstalledCapacity(String userUid);

    HashMap<String,Object> SubaccountPowerPlantAlarm(String userUid);

    List<stationListName> stationListAccsucess(String userUid);

    HashMap<String,Double> hYSubAccDayMonthAll2(String userUid);

    Object selectSubStationList(String userUid, Integer sortWith);

    List<NewHYAstationList> filterSubStationList(String userUid, StationConditionDTO stationConditionDTO);
}
