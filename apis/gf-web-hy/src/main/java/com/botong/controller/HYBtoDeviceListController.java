package com.botong.controller;


import com.botong.services.service.HYBtoDeviceListService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.DeviceListDTO;
import entityDTO.DeviceListForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;
import vo.EfficiencyVo;

import javax.annotation.Resource;
import java.text.Collator;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

@CrossOrigin
@RestController
@RequestMapping("btodevicelist")
@Api(tags = "逆变器列表")
public class HYBtoDeviceListController {
    @Resource
    private HYBtoDeviceListService hyBtoDeviceListService;

/*    @ApiOperation("逆变器状态_2_电站在线")
    @GetMapping("Deviceonline")
    public AjaxResult Deviceonline(){return AjaxResult.success("操作成功",hyBtoDeviceListService.Deviceonline());}

    @ApiOperation("逆变器数量")
    @GetMapping("DeviceAllNum")
    public AjaxResult DeviceAllNum(){return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceAllNum());}

    @ApiOperation("逆变器状态_2_电站告警")
    @GetMapping("DeviceStatus2")
    public AjaxResult DeviceStatus2(){return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceStatus2());}*/

    @ApiOperation("工作效率排名")
    @GetMapping("workEfficiencyRanking")
    public AjaxResult workEfficiencyRanking() {
        HashMap<String, Object> workEfficiencyRanking = hyBtoDeviceListService.workEfficiencyRanking();
        return AjaxResult.success("操作成功", workEfficiencyRanking);
    }

    @ApiOperation("实时监控——设备概况-逆变器信息")
    @GetMapping("DeviceInformation")
    public AjaxResult DeviceInformation(String plantUid){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceInformation(plantUid));
    }

    @ApiOperation("实时监控——设备监测-电压")
    @GetMapping("DeviceVoltage")
    public AjaxResult DeviceVoltage(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceVoltage(snName,tableName,tableName2));
    }

    @ApiOperation("实时监控——设备监测-频率")
    @GetMapping("DeviceFrequency")
    public AjaxResult DeviceFrequency(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceFrequency(snName,tableName,tableName2));
    }
    @ApiOperation("实时监控——设备监测-温度")
    @GetMapping("Devicetemperature")
    public AjaxResult Devicetemperature(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.Devicetemperature(snName,tableName,tableName2));
    }


    @ApiOperation("实时监控——设备监测-有功功率")
    @GetMapping("DeviceActivePower")
    public AjaxResult DeviceActivePower(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceActivePower(snName,tableName,tableName2));
    }
    @ApiOperation("实时监控——设备概况-逆变器对应的sn")
    @GetMapping("DeviceInformationSN")
    public AjaxResult DeviceInformationSN(String plantUid){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceInformationSN(plantUid));
    }

    @ApiOperation("实时监控——设备概况-逆变器对应的图表")
    @GetMapping("DeviceInformationTU")
    public AjaxResult DeviceInformationTU(String plantUid){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceInformationTU(plantUid));
    }

    @ApiOperation("实时监控——设备监测-列表")
    @GetMapping("DeviceMonitorUlist")
    public AjaxResult DeviceMonitorUlist(){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceMonitorUlist());
    }

    @ApiOperation("采集因子-最大值最小值")
    @GetMapping("collectionMaxMin")
    public AjaxResult collectionMaxMin(String snName,String tableNme,String tabletime2) {
        return AjaxResult.success("操作成功", hyBtoDeviceListService.collectionMaxMin(snName,tableNme,tabletime2));
    }

    @ApiOperation("采集因子-平均值")
    @GetMapping("collectioAvg")
    public AjaxResult collectioAvg(String snName,String tableName,String tableName2) {
        return AjaxResult.success("操作成功", hyBtoDeviceListService.collectioAvg(snName,tableName,tableName2));
    }

    @ApiOperation("实时监控——设备监测-电流")
    @GetMapping("Devicecurrent")
    public AjaxResult Devicecurrent(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.Devicecurrent(snName,tableName,tableName2));
    }

    @ApiOperation("通讯监控-设备监控_分页plantId")
    @GetMapping("EquipmentMonitoringplantId")
    public AjaxResult EquipmentMonitoringplantId(String plantId) {
        return AjaxResult.success("操作成功", hyBtoDeviceListService.EquipmentMonitoringplantUid(plantId));
    }

    @ApiOperation("逆变器数量及状态列表")
    @GetMapping("/deviceNum")
    public AjaxResult DeviceNum(){
        return AjaxResult.success("操作成功",hyBtoDeviceListService.DeviceNum());
    }

    @ApiOperation("融合接口采集因子，最大值最小值")
    @GetMapping("mergecollectionMaxMin")
    public AjaxResult mergecollectionMaxMin(String snName,String tableName,String tableName2) {
        return AjaxResult.success("操作成功", hyBtoDeviceListService.mergecollectionMaxMin(snName,tableName,tableName2));
    }

    @ApiOperation(value = "逆变器列表数据",response = DeviceListDTO.class)
    @PostMapping("deviceList")
    public AjaxResult deviceList(@RequestBody DeviceListForm deviceListForm) {
        return AjaxResult.success("操作成功", hyBtoDeviceListService.deviceList(deviceListForm));
    }

    @ApiOperation("逆变器详细信息")
    @GetMapping("deviceDetail")
    public AjaxResult deviceDetail(String plantId) {
        return AjaxResult.success("操作成功", hyBtoDeviceListService.deviceDetail(plantId));
    }

    @ApiOperation("查询单个电站总发电量和总功率(根据电站Uid)")
    @GetMapping("totalEnergyAndPower")
    public AjaxResult totalEnergyAndPower(String plantId,String date) {
        return AjaxResult.success("操作成功", hyBtoDeviceListService.totalEnergyAndPower(plantId,date));
    }


}
