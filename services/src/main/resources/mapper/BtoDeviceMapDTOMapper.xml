<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.BtoDeviceMapDTOMapper">
    <resultMap type="entityDTO.BtoDeviceMapDTO" id="btoDeviceMap">
        <result property="sn" column="sn"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalenergy" column="total_energy"/>
        <result property="time" column="time"/>
        <result property="plantId" column="plant_id"/>
        <result property="stationName" column="name1"/>
        <result property="peakPower" column="peakPower"/>
    </resultMap>

    <select id="selectBtoDeviceMap"  resultMap="btoDeviceMap">
        SELECT
            a.sn,
            a.today_energy,
            a.total_energy,
            a.time,
            b.plant_id,
            b.name1,
            a.power
        FROM
            bto_device_${table} a
            LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
            time LIKE #{b}
          AND plant_id = #{plantId}
          AND a.sn = #{sn}
        order by time desc limit 1;
    </select>
    
    <select id="selectDistinct" resultType="String">
        SELECT
            distinct  a.sn
        FROM
            bto_device_${table} a
            LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
            time LIKE '${b}'
          AND plant_id = '${plantId}';
    </select>

    <select id="selectBtoDeviceMapPlus"  resultMap="btoDeviceMap">
        SELECT
            a.sn,
            today_energy,
            time,
            b.plant_id,
            b.name1,
            a.power
        FROM
            bto_device_${table} a
            LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
            time LIKE #{time}
          AND a.sn = #{sn}
        order by time
    </select>


    <select id="selectBtoDeviceMapPlusMonth"  resultMap="btoDeviceMap">
        SELECT
            a.plant_id,
            a.date time,
            a.energy as today_energy,
            b.name1
        FROM
            v_day a   LEFT JOIN v1_station_device b
                                on a.plant_id = b.plant_id
        WHERE
            a.plant_id = #{plantId}
          AND date LIKE #{time}
        ORDER BY
            date
    </select>


    <select id="selectBtoDeviceMapPlusYear"  resultMap="btoDeviceMap">
        SELECT
            a.plant_id,
            left(a.date,7)  time,
            sum(a.energy) as today_energy,
            b.name1
        FROM
            v_day a   LEFT JOIN v1_station_device b
        on a.plant_id = b.plant_id
        WHERE
            a.plant_id = #{plantId}
          AND date LIKE #{time}
        GROUP BY
            time
    </select>

    <select id="selectBtoDeviceMap2" resultType="entityDTO.BtoDeviceMapDTO">
        SELECT
            a.sn,
            today_energy,
            a.total_energy,
            time,
            b.plant_id,
            b.name1,
            a.power
        FROM
            bto_device_${table} a
            LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
            time LIKE #{b}
          AND plant_id = #{plantId}
          AND a.sn = #{sn}
        ORDER BY
            time
    </select>
</mapper>