package com.botong.services.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.AlarmAnalysisMapper;
import com.botong.services.service.AlarmAnalysisService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.AlarmAnalysis;
import entityDTO.AlarmAnalysisConditionDTO;
import entityDTO.OperatorEvents;
import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import utils.AjaxResult;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AlarmAnalysisServiceImpl extends ServiceImpl<AlarmAnalysisMapper, AlarmAnalysis> implements AlarmAnalysisService {

    @Autowired
    private AlarmAnalysisMapper alarmAnalysisMapper;

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisListForm(Integer page, Integer pageSize) {
        QueryWrapper<Object> objectQueryWrapper = new QueryWrapper<>();
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time = simpleDateFormat.format(date);
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.selectListt(page, pageSize, time);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery 实时报警——时间区间查询——精确时分秒 plantUid time 非全
    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery time plantUid全

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisTimeIntervalQuery(String startTime, String endTime, int page, int pagesize,String plantUid) {
        List<String> splits= null;
        if(plantUid!=null && plantUid.length()>0 ){
             String[] list = plantUid.split(",");
             splits = Arrays.asList(list);
        }
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pagesize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisTimeIntervalQuery(page, pagesize, startTime, endTime,splits);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisTconditionQuery(String startTime, int page, int pagesize, String name, String name1, String sn) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pagesize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisTconditionQuery(page, pagesize, startTime, name,name1,sn);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }


    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisPolymerization( String page, String pageSize,String plantUid) {

        String[] split = plantUid.split(",");
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisPolymerization(page, pageSize,Arrays.asList(split));
            PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
            return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarm(String startTime, String endTime, String page, String pageSize,int grade) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarm(page, pageSize,endTime, startTime,grade);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisListFormplantUid(Integer page, Integer pageSize, String plantUid) {
        String[] split = plantUid.split(",");
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisListFormplantUid(page, pageSize,Arrays.asList(split));
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll(String startTime, String endTime, String page, String pageSize, int grade, String plantUid) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmAll(startTime,endTime,page, pageSize,plantUid,grade);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public List<AlarmAnalysis> AlarmAnalysisTimeIntervalAll(String time1, String time2) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
 //       List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisTimeIntervalAll(time1,time2);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
        return alarmAnalysisMapper.AlarmAnalysisTimeIntervalAll(time1,time2);
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll3(Integer page, Integer pageSize,String name1,String startTime,String endTime,String grade) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        if (name1==""){
            name1="%";
        }
        if (startTime==""){
            startTime="%";
        }
        if (endTime==""){
            endTime="%";
        }
        if (grade==""){
            grade= "%";
        }
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmAll3(page,pageSize,name1,startTime,endTime,grade);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmQuann(String page, String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmQuann(page, pageSize);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisPolymerizationplantUid(String startTime, String endTime, String page, String pageSize, String plantUid) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<String> list = new ArrayList<>();
        if(plantUid!=null &&!"".equals(plantUid)){
            String[] array = plantUid.split(",");
            if(array!=null&&array.length>0){
                list = Arrays.asList(array);
            }
        }


        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisPolymerizationplantUid(startTime,endTime, list);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public Object totalAlarmStatistics() {
        return alarmAnalysisMapper.totalAlarmStatistics();
    }

    @Override
    public Object totalAlarmStatisticsTime(String time) {
        return alarmAnalysisMapper.totalAlarmStatisticsTime(time);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIME(String time, String plantUid) {
        return baseMapper.totalAlarmStatisticsTIME(time,plantUid);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMETU(String time) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMETU(time);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgeren(String time, String plantUid) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgeren(time,plantUid);

    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenyaoxin(String time) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerenyaoxin(time);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennyaoxin(String time, String plantUid) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerennyaoxin(time,plantUid);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEstatistics(String time) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEstatistics(time);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennstatistics(String time, String plantUid) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerennstatistics(time,plantUid);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEEquipmentStatistics(String time) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEEquipmentStatistics(time);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenEquipmentStatistics(String time, String plantUid) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerenEquipmentStatistics(time,plantUid);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMELevelStatistics(String time) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMELevelStatistics(time);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennLevelStatistics(String time, String plantUid) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerennLevelStatistics(time,plantUid);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEyaoxinlist(String time) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEyaoxinlistt(time);
    }

    @Override
    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenlist(String time, String plantUid) {
        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerenlist(time,plantUid);
    }

    @Override
    public PageInfo<OperatorEvents> OperatorEventData(Integer page, Integer pageSize, String alarmtime, String city,String endtime) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<OperatorEvents> stationLunBos = alarmAnalysisMapper.OperatorEventData(page, pageSize, alarmtime,city,endtime);
        PageInfo<OperatorEvents> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll2(Integer page, Integer pageSize, String name1, String grade) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        if (name1==""){
            name1="%";
        }
        if (grade==""){
            grade= "%";
        }
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmAll2(page,pageSize,name1,grade);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AlarmAnalysis> OperatorData(Integer page, Integer pageSize, String startTime, String endTime,String name) {
        PageHelper.startPage(page,pageSize);
        if (name==null){
            name="%";
        }
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.OperatorData(page, pageSize,endTime, startTime,name);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public Object OperatorDataMonth(Integer page, Integer pageSize, String startTime, String endTime, String name) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.OperatorDataMonth(page, pageSize,endTime, startTime,name);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public AjaxResult AlarmAnalysisByCondition(AlarmAnalysisConditionDTO alarmAnalysisConditionDTO) throws ClassNotFoundException, NoSuchFieldException {
        //获取前端传递的页码page和页面大小pageSize
        String page = alarmAnalysisConditionDTO.getPage();
        String pageSize = alarmAnalysisConditionDTO.getPageSize();
        //获取查询类型：0:列表查询 1:聚合查询
        Integer type = alarmAnalysisConditionDTO.getType();
        //获取查询时间:   开始时间和结束时间 需要进行拼接
        String startTime = alarmAnalysisConditionDTO.getStartTime().concat(" 00:00:00");
        String endTime = alarmAnalysisConditionDTO.getEndTime().concat(" 23:59:59");
        //获取查询条件：电站UID数组、告警信息、告警级别、事件描述、告警状态
        String[] plantUidArray = alarmAnalysisConditionDTO.getPlantUid();
        String alarmMessage = alarmAnalysisConditionDTO.getAlarmMessage();
        String grade = alarmAnalysisConditionDTO.getGrade();
        String mean = alarmAnalysisConditionDTO.getMean();
        String status = alarmAnalysisConditionDTO.getStatus();
        //聚合条件传字符串数组：原因是传字符串数组才能确定聚合顺序
//        String[] conditions = alarmAnalysisConditionDTO.getConditions();
        List<AlarmAnalysis> alarmAnalyseList = new ArrayList<>();
        if (type==0){
            PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
             alarmAnalyseList = alarmAnalysisMapper.AlarmByConditionOnListQuery(startTime, endTime, plantUidArray, alarmMessage, grade, mean, status);
            PageInfo<AlarmAnalysis>  alarmAnalyseListInfo= new PageInfo<>(alarmAnalyseList);
             return AjaxResult.success("操作成功",alarmAnalyseListInfo);
        }else if(type==1) {
            alarmAnalyseList =  alarmAnalysisMapper.AlarmByConditionOnAggregation(startTime, endTime, plantUidArray);
//            Map<String, List<AlarmAnalysis>> listMap = alarmAnalyseList.stream().collect(Collectors.groupingBy(alarmAnalysis -> new AlarmAnalysis().getPlantUid()));
            List<List<AlarmAnalysis>> alarmAnalyseLists = CollStreamUtil.groupByKey(alarmAnalyseList, AlarmAnalysis::getPlantUid).values().stream().collect(Collectors.toList());
            PageInfo pageInfo = pageForList(alarmAnalyseLists, page, pageSize);
            return AjaxResult.success("操作成功",pageInfo);
        }else {
            return AjaxResult.error("未知异常,请联系服务端工程师！");
        }
    }

    @Override
    public PageInfo<AlarmAnalysis> AlarmAnalysisPolymerizationAll(String startTime, String endTime, String page, String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisPolymerizationAll(page, pageSize,endTime, startTime);
        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public HashMap<String, String[]> getEventMean() {
        //取出event_mean表中的所有数据
        List<HashMap<String, String>>  eventMean = alarmAnalysisMapper.selectEventMean();
        //定义一个字符串数组
        String[] strs = new String[eventMean.size()];
        //定义一个二维数组
        String[][] str2 = new String[eventMean.size()][eventMean.size()];
        //定义一个存放所有key的结果集
        Set<String> keySets = new HashSet<>();
        //定义一个存放处理后数据的结果集
        HashMap<String,String[]> eventMeanMap = new HashMap<>();
        //定义一个指针用来控制 二维数组每一行数据的存放
        int j =0;
        //遍历event_mean表中的所有数据
        for (HashMap<String, String> hashMap : eventMean) {
            //将key存放进结果集
            keySets= hashMap.keySet();
            //定义一个指针用来控制 二维数组行的下标
            int i = 0;
            //遍历key的结果集，拿到每一个key（每次存入一列数据）
            for (String key : keySets) {
                //取出每一个key对应的值：即最终数据中key对应的每一条结果
                String value = hashMap.get(key);
                //对最终数据中key对应的每一条结果存入字符串数组
                strs[i] = value;
                //将该字符串数组存入对应行的对应位置
                str2[i][j] = strs[i];
                //行指针往后移
                i++;
            }
            //列指针往后移（存入一列数据后，存入第二列）
            j++;
        }
        int length = 0;
        for (String key : keySets) {
            //调用封装的方法：取出二维数组中每一行的数据（即最终结果集中对应的key的数据）
            String[] array = getArray(str2, length);
            //将行数据按照对应的key进行存储进最终结果集
            eventMeanMap.put(key,array);
            //指针后移，取出下一行的数据
            length++;
        }
        //对grade级别的数据进行去重
        String[] gradesArr = eventMeanMap.get("grade");
        List<String > list = new LinkedList<String>();
        for (int i = 0; i < gradesArr.length; i++) {
            if (!list.contains(gradesArr[i])) {
                list.add(gradesArr[i]);
            }
        }
        //去重后转换为字符串数组的格式
        String[] newGradesArr = list.toArray(new String[list.size()]);
        //替换最终结果集中未去重之前的数组
        eventMeanMap.replace("grade",gradesArr,newGradesArr);
        return eventMeanMap;
    }
    
    //封装对List集合进行分页的方法
    public PageInfo pageForList(List<List<AlarmAnalysis>> alarmAnalyseLists,String page,String pageSize){

        //计算当前需要显示的数据下标起始值
        Integer pageNum = Integer.valueOf(page);
        Integer pageSize2 = Integer.valueOf(pageSize);
        int startIndex = (pageNum - 1) * pageSize2;
        int endIndex = Math.min(startIndex + pageSize2, alarmAnalyseLists.size());
        //从链表中截取需要显示的子链表，并加入到Page
        PageInfo pageInfo = new PageInfo(alarmAnalyseLists.subList(startIndex,endIndex));
        pageInfo.setTotal(alarmAnalyseLists.size());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize2);
        return pageInfo;
    }

    //封装取出二维数组中某一行的数据并存入新的集合的方法
    @NotNull
    @Contract(pure = true)
    private String[] getArray(@NotNull String[][] arr, int length){
        //定义一个字符串数组用来接收每一列的数据
        String[] array = new String[arr.length];
        //循环取出第length列中的数据
            for (int j = 0; j < arr.length; j++) {
                array[j] = arr[length][j];
        }
        return array;
    }



}
