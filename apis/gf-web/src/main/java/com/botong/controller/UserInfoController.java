package com.botong.controller;

import com.botong.services.service.BtoDeviceService;
import com.botong.services.service.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

@RestController
@RequestMapping("userInfo")
@Api(tags = "用户综合信息")
@CrossOrigin
public class UserInfoController {

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private BtoDeviceService btoDeviceService;

    @ApiOperation("获取电量衍生日月年信息")
    @GetMapping("getUserDerivativeInfo")
    public AjaxResult inverterElectricityInfo(String dataloggerSn){return AjaxResult.success("操作成功",userInfoService.getUserDerivativeInfo(dataloggerSn));}

    @ApiOperation("获取用户的综合信息")
    @GetMapping("getUserComplexInfo")
    public AjaxResult getUserComplexInfo(String plantId){return AjaxResult.success("操作成功",btoDeviceService.getUserComplexInfo(plantId));}
}
