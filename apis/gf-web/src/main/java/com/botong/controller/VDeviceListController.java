package com.botong.controller;

import com.botong.services.service.VDeviceListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

@RestController
@Api(tags="设备档案")
@RequestMapping("/VDeviceList")
@CrossOrigin
public class VDeviceListController {

    @Autowired
    private VDeviceListService vDeviceListService;

    @ApiOperation("查询全部数据")
    @GetMapping("/selectAll")
    public AjaxResult selectAll(Integer page, Integer pageSize){
        return  AjaxResult.success("查询成功！", vDeviceListService.selectAll(page,pageSize));
    }

    @ApiOperation("模糊查询")
    @GetMapping("/likeSelect")
    public AjaxResult likeSelect(Integer page, Integer pageSize, String device) {
        return AjaxResult.success("查询成功！",vDeviceListService.likeSelect(page,pageSize,device));
    }

   /* @ApiOperation("插入接口")
    @PostMapping("/insert")
    public AjaxResult insert(@RequestBody VDeviceList vDeviceList) {
        int i = vDeviceListService.insert(vDeviceList);
        if ( i > 0 ) {
            return AjaxResult.success("增加成功！");
        } else {
            return AjaxResult.error("增加失败！插入重复！");
        }
    }*/
}


