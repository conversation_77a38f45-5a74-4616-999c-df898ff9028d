package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.BtoStationList;
import entityDTO.AllStationList;
import entityDTO.BTOstationListDTO;
import entityDTO.StationLunBo;
import vo.StationListVo;

import java.util.List;

public interface HYABtoStationListService extends IService<BtoStationList> {


        List<BtoStationList> ceshi();

        Long StaionAllNum();

        Long StationStatus0();
        Long StationStatus1();
        List<StationLunBo> StationStatus2();


        Object Staionuptime();

        List<BtoStationList> sqlStaion();


        BtoStationList StationAllpeakPower();

        List<BTOstationListDTO>  StationAllenergy(String tableNme, String nowtime10, String nowtime20);



        List<BtoStationList> GFmonitoringCenter();

        BtoStationList getInstalledCapacity(String plantId);

        List<StationLunBo> Stationonline();

        List<StationListVo> getAllStationName();

        /**
         * 根据电站id获取装机容量
         */
        String getInstalledCapacityById(String plantId);

        /**
         * 根据电站id获取电站uid
         */
        String getIdByUId(String plantId);

        PageInfo<AllStationList> ListOfTotalPowerStations(String page, String pageSize);

        PageInfo<AllStationList> ListOfTotalPowerStationsstationName(String page, String pageSize, String stationName);

        Object ListOfTotalPowerStationsPhone(String plantUid);

        String getPhoneNumByUid(String userUid);
        }