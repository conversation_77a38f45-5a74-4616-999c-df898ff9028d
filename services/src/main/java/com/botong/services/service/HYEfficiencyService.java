package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.OperatorDetails;
import entityDTO.EfficiencyDTO;
import entityDTO.StationInfoConditionDTO;
import utils.AjaxResult;

import java.util.HashMap;
import java.util.List;


public interface HYEfficiencyService extends IService<EfficiencyDTO> {
    /**
     *     求出月发电量-发电效率-环比-同比数据
     */
    AjaxResult PowerGenerationMonth(String plantId, String month, Integer page, Integer pageSize);
    /**
     *     求出年发电量-发电效率-环比-同比数据
     */
    AjaxResult PowerGenerationYear(String plantId,String year,Integer page,Integer pageSize);

    /**
     * 电站统计——统计电站信息列表--带筛选
     * @param stationInfoConditionDTO
     * @return
     */
    AjaxResult StatisticsStationInfoByCondition(StationInfoConditionDTO stationInfoConditionDTO);

    /**
     * 设备统计——列表--带筛选
     * @param userName
     * @return
     */
    PageInfo<OperatorDetails> operatorDetails(String userName,String plantId,Integer page,Integer pageSize);
}
