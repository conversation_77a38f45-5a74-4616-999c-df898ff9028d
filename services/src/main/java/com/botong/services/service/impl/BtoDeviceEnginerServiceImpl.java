package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoDeviceEnginerMapper;
import com.botong.services.service.BtoDeviceEnginerService;
import com.mysql.jdbc.exceptions.MySQLTransactionRollbackException;
import entityDTO.BtoDeviceBindingDTO;
import entityDTO.BtoDeviceEnginerDTO;
import entityDTO.UpdateDeviceFormDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class BtoDeviceEnginerServiceImpl extends ServiceImpl<BtoDeviceEnginerMapper, BtoDeviceEnginerDTO> implements BtoDeviceEnginerService {
    @Autowired
    private BtoDeviceEnginerMapper btoDeviceEnginerMapper;

    @Override
    public Integer setDevice(BtoDeviceBindingDTO btoDeviceBindingDTO) {
        //生成UUID
        String deviceId = UUID.randomUUID().toString().toUpperCase();
        //获得系统当前时间(DateTime格式)
        Date date = new Date();
        Timestamp currentTime = new Timestamp(date.getTime());
        BtoDeviceEnginerDTO btoDeviceList = new BtoDeviceEnginerDTO();
        //逆变器所属公司ID
        btoDeviceList.setUserUid(btoDeviceBindingDTO.getUserUid());
        btoDeviceList.setDeviceId(deviceId);
        btoDeviceList.setPlantId(btoDeviceBindingDTO.getPlantId());
        btoDeviceList.setManuFacturer(btoDeviceBindingDTO.getManuFacturer());
        btoDeviceList.setDeviceName(btoDeviceBindingDTO.getDeviceSn());
        btoDeviceList.setDataloggerSn(btoDeviceBindingDTO.getDeviceSn());
        btoDeviceList.setDeviceSn(btoDeviceBindingDTO.getDeviceSn());
        btoDeviceList.setModel(btoDeviceBindingDTO.getModel());
        btoDeviceList.setType(btoDeviceBindingDTO.getType());
        btoDeviceList.setDType("Inverter");
        btoDeviceList.setTZone("+08:00");
        btoDeviceList.setLastUpdateTime(currentTime);
        btoDeviceList.setCreateTime(currentTime);
        btoDeviceList.setUpdateTime(currentTime);
        btoDeviceList.setComponentPower(btoDeviceBindingDTO.getComponentPower());
        btoDeviceList.setSpecial(btoDeviceBindingDTO.getSpecial());
        btoDeviceList.setDevicePc(btoDeviceBindingDTO.getDevicePc());
        btoDeviceList.setDeviceAddress(btoDeviceBindingDTO.getDeviceAddress());
        if (baseMapper.insert(btoDeviceList) == 1) {
            btoDeviceEnginerMapper.btostationBase(btoDeviceBindingDTO.getOrentation(), btoDeviceBindingDTO.getPlantId());
            btoDeviceEnginerMapper.btostationList(btoDeviceBindingDTO.getOrentation(), btoDeviceBindingDTO.getPlantId());
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public List<BtoDeviceEnginerDTO> getDeviceBySN(String sn) {
        List<BtoDeviceEnginerDTO> btoDeviceEnginer = btoDeviceEnginerMapper.selectBySN(sn);
        return btoDeviceEnginer;
    }

    @Override
    public List<HashMap<String, String>> getDeviceCode(String value, String type) {
        List<HashMap<String, String>> deviceCodeList = btoDeviceEnginerMapper.getDeviceCode(value, type);
        return deviceCodeList;
    }

    @Override
    public String updateDevice(UpdateDeviceFormDTO updateDeviceFormDTO) throws IllegalAccessException {
        String result = "更新成功";
        if ( checkObjFieldIsNull(updateDeviceFormDTO)) {
            result = "传入参数异常";
            return result;
        } else {
            try {
                //更新逆变器列表
                Integer deviceFlag = btoDeviceEnginerMapper.updateDeviceList(updateDeviceFormDTO);
                //更新光伏组件表
                Integer moduleFlag = btoDeviceEnginerMapper.updateDeviceModules(updateDeviceFormDTO);
                //删除version
                Integer versionFlag = btoDeviceEnginerMapper.deleteBySN(updateDeviceFormDTO);
//                System.out.println(1/0);
                if (deviceFlag + moduleFlag + versionFlag <= 0) {
                    result = "更新失败";
                }
            } catch (Exception e) {
                e.printStackTrace();
                TransactionAspectSupport.currentTransactionStatus()
                        .setRollbackOnly();
                result = "更新内部异常！";
            }
            return result;
        }
    }

    @Override
    public String getStationUIDByName(String name) {
        String plantId = btoDeviceEnginerMapper.selectStationIDByName(name);
        return plantId;
    }

    public  boolean checkObjFieldIsNull(Object obj) throws IllegalAccessException {
        boolean flag = false;
        if(Objects.isNull(obj)){
            flag =  true;
        }else{
            for(Field f : obj.getClass().getDeclaredFields()){
                f.setAccessible(true);
                log.debug(f.getName());
                if(f.get(obj) == null || "".equals(f.get(obj)) ){
                    flag = true;
                    return flag;
                }
            }
          }
        return flag;
    }
}
