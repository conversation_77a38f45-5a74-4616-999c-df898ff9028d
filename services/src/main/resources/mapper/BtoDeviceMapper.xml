<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.BtoDeviceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoDevice" id="btoDeviceMap">
        <result property="dataloggerSn" column="datalogger_sn"/>
        <result property="sn" column="sn"/>
        <result property="ipv1" column="ipv1"/>
        <result property="ipv2" column="ipv2"/>
        <result property="ipv3" column="ipv3"/>
        <result property="vpv1" column="vpv1"/>
        <result property="vpv2" column="vpv2"/>
        <result property="vpv3" column="vpv3"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="power" column="power"/>
        <result property="powerFactor" column="power_factor"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="monthEnergy" column="month_energy"/>
        <result property="yearEnergy" column="year_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="temperature" column="temperature"/>
        <result property="fac" column="fac"/>
        <result property="fac1" column="fac1"/>
        <result property="fac2" column="fac2"/>
        <result property="time" column="time"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <resultMap id="ElectricityStatisticsVoMap" type="vo.ElectricityStatisticsVo">
        <result property="dayElectricity" column="dayElectricity"/>
        <result property="monthElectricity" column="monthElectricity"/>
        <result property="yearElectricity" column="yearElectricity"/>
        <result property="allElectricity" column="allElectricity"/>
    </resultMap>

    <resultMap id="InverterVoMap" type="vo.InverterVo">
        <result property="inverterName" column="name1"/>
        <result property="inverterSn" column="sn"/>
    </resultMap>

    <resultMap id="getMonthElectricitySnMap" type="com.botong.services.vo.ChartVo">
        <result property="plantId" column="plant_id"/>
        <result property="date" column="date"/>
        <result property="energy" column="energy"/>
    </resultMap>

    <resultMap id="getYearElectricitySnMap" type="com.botong.services.vo.ChartVo">
        <result property="plantId" column="plant_id"/>
        <result property="date" column="t"/>
        <result property="energy" column="sum(energy)"/>
    </resultMap>

    <resultMap type="com.botong.services.vo.DayPowerVo" id="getDayPowerBySnMap">
        <result property="power" column="power"/>
        <result property="time" column="time"/>
    </resultMap>
    <resultMap type="com.botong.services.vo.ChartVo" id="getAllElectricitySnMap">
        <result property="plantId" column="sn"/>
        <result property="date" column="time"/>
        <result property="energy" column="total_energy"/>
    </resultMap>
    <insert id="registeredUser">
        INSERT INTO bto_user_list (c_user_id, c_user_uid, c_user_name, c_user_tel, del_flag, c_user_regtime, update_date, c_user_email, update_time, create_time, password, special,enginer_id
    )
        VALUES (1, #{uuid}, #{cUserName}, #{cUsertel}, 0, #{regtiTime}, #{regtiTime}, #{cUserEmail}, #{regtiTime}, #{regtiTime},'${password}',#{special},#{engnierId})
    </insert>

    <select id="inverterElectricityInfo" parameterType="string" resultMap="ElectricityStatisticsVoMap">
        SELECT
            sum( today_energy ) dayElectricity,
            sum( month_energy ) monthElectricity,
            sum( year_energy ) yearElectricity,
            sum( total_energy ) allElectricity
        FROM(
                SELECT
                    today_energy,
                    month_energy,
                    year_energy,
                    total_energy
                FROM
                    v_device_list aa
                        LEFT JOIN (
                        SELECT
                            sn,
                            today_energy,
                            month_energy,
                            year_energy,
                            total_energy
                        FROM
                            ${tableName}
                        WHERE
                            time LIKE #{time}
                        GROUP BY
                            SN,
                            time
                        ORDER BY
                            time DESC
                    ) bb ON aa.device_sn = bb.sn
                WHERE
                    sn IS NOT NULL
                GROUP BY
                    sn
            ) q;
    </select>
    <select id="getUserByInverterMoreOne" resultType="list">
        select name1 from v1_station_device group by name1 having count(name1) > 1
    </select>

    <select id="getUserByInverterEqualOne" resultType="java.lang.String">
        select name1 from v1_station_device group by name1 having count(name1) = 1
    </select>
    <select id="getCurrentPowerBySn" parameterType="string" resultType="float">
        select power from ${tableName} where datalogger_sn = #{sn} ORDER BY update_time desc limit 1
    </select>
    <select id="getDayMonthYearAllInfo" resultMap="btoDeviceMap">
        select today_energy,month_energy,year_energy,total_energy from ${tableName} where datalogger_sn = #{dataloggerSn}
            order by update_time desc limit 1
    </select>

    <select id="getDayPowerBySn" parameterType="string" resultMap="getDayPowerBySnMap">
        SELECT
            sn,
            power,
            time
        FROM
            ${tableName1}
        WHERE
            time LIKE #{time}
          AND sn = #{sn}
    </select>
    <select id="getMonthElectricitySn" parameterType="string" resultMap="getMonthElectricitySnMap">
        SELECT
            plant_id,
            date,
            energy
        FROM
            v_day
        WHERE
            date LIKE #{time}
          AND plant_id = #{plantId}
        ORDER BY
            date
    </select>
    <select id="getYearElectricitySn" parameterType="string" resultMap="getYearElectricitySnMap">
        SELECT
            plant_id,
            LEFT ( date, 7 ) t,
            sum(energy)
        FROM
            v_day
        WHERE
            plant_id = #{plantId}
        GROUP BY
            t
        ORDER BY
            date
    </select>
    <select id="getAllElectricitySn" parameterType="string" resultMap="getAllElectricitySnMap">
        SELECT
            sn,
            total_energy,
            time
        FROM
            ${tableName}
        WHERE
            time LIKE #{time}
          AND sn = #{sn}
        ORDER BY
            time DESC
            LIMIT 1
    </select>
    <select id="getElectricityBySn" parameterType="string" resultMap="btoDeviceMap">
        SELECT
            sn,
            today_energy,
            total_energy,
            time
        FROM
            ${tableName}
        WHERE
            time LIKE #{time}
          AND sn = #{sn}
        ORDER BY
            time DESC
            LIMIT 1
    </select>
    <select id="getInverterDetails" resultType="entity.BtoDevice">
        SELECT
            sn,
            time,
            ipv1,
            ipv2,
            ipv3,
            vpv1,
            vpv2,
            vpv3,
            iac1,
            iac2,
            iac3,
            vac1,
            vac2,
            vac3,
            fac,
            fac1,
            fac2,
            today_energy,
            total_energy,
            power
        FROM
           ${tableName}
        WHERE
            sn = #{sn}
          AND time LIKE #{time}
        ORDER BY
            time DESC
            LIMIT 1
    </select>


</mapper>