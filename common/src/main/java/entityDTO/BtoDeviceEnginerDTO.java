package entityDTO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
/**
 * 逆变器列表
 *
 * <AUTHOR>
 * @date 2022-09-21 10:09:49
 */
@TableName("bto_device_list")
@Data
public class BtoDeviceEnginerDTO {
        /**
         * 逆变器唯一id
         */
        private String deviceId;

        /**
         * 逆变器所属公司的uid
         */
        private String userUid;

        /**
         * 电站id
         */
        private String plantId;

        /**
         * 逆变器对应的SN
         */
        private String dataloggerSn;

        /**
         * 逆变器SN
         */
        private String deviceSn;

        /**
         * 逆变器厂家
         */
        private String manuFacturer;

        /**
         * 逆变器型号
         */
        private String model;

        /**
         * 逆变器类型（0：并网，1：储能）
         */
        private String type;

        /**
         * 数据最后接收时间
         */
        private Date lastUpdateTime;

        /**
         * 当前功率
         */
        private Float sysPower;

        /**
         * 数据上传时间
         */
        private Date updataDate;

        /**
         * 时区
         */
        private String tZone;

        /**
         * 逆变器类型
         */
        private String dType;

        /**
         * 总发电量
         */
        private Float eTotal;

        /**
         * 日发电量
         */
        private Float eToday;

        /**
         * 月发电量
         */
        private Float eMonth;

        /**
         * 年发电量
         */
        private Float eYear;

        /**
         * 逆变器状态（0：离线，1：正常运行，2：告警运行，3：离线）
         */
        private String deviceStatus;

        /**
         * 工作状态 5.2版本新增属性
         逆变器类型type为并网时[1:待机][2:正常][3:错误][4:警告]
         逆变器类型type为储能时[1:待机][2:并网][3:离网][4:旁路][5:故障][6:升级模式]

         */
        private String workStatus;

        /**
         * 逆变器故障
         */
        private String deviceEvent;

        /**
         * 逆变器PC码
         */
        private String devicePc;

        /**
         * 逆变器名称
         */
        private String deviceName;

        /**
         * 物联网卡号 5.2版本新增属性
         */
        private String iotNum;

        /**
         * CCID 5.2版本新增属性
         */
        private String ccId;

        /**
         * 模块SN 5.2版本新增属性
         */
        private String moduleSn;

        /**
         * 合作到期时间 5.2版本新增属性
         */
        private Date cooperationExpireDate;

        /**
         * 物联网卡使用状态1待激活 2正常使用 3已过期 4已注销 5沉默期 6停机 5.2版本新增属性
         */
        private String cardStatus;

        /**
         *
         */
        private Date updateTime;

        /**
         *
         */
        private Date createTime;

        /**
         * 该逆变器接入组件功率
         */
        private String componentPower;

        /**
         * 河源整线为`1`
         */
        private Integer special;
        /**
         * 逆变器地址
         */
        private String deviceAddress;

    }

