package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoDeviceTypeMapper;
import com.botong.services.service.BtoDeviceTypeService;
import entity.BtoDeviceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BtoDeviceTypeServiceImpl extends ServiceImpl<BtoDeviceTypeMapper, BtoDeviceType> implements BtoDeviceTypeService {

    @Autowired
    private BtoDeviceTypeMapper btoDeviceTypeMapper;

    @Override
    public List<BtoDeviceType> selectBtoDeviceType(){
        List<BtoDeviceType> btoIndices = btoDeviceTypeMapper.selectAll();
        return btoIndices;
    }

    @Override
    public List<BtoDeviceType> selectLikeBtoDeviceType(String model){
        String cc="%" + model + "%";
        List<BtoDeviceType> btoDeviceTypes = btoDeviceTypeMapper.selectLike(cc);
        return btoDeviceTypes;
    }
}
