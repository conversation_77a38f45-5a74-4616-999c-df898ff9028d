package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.StatisticalReport;

import java.util.List;

public interface StatisticalReportService extends IService<StatisticalReport> {
    PageInfo<StatisticalReport> PowerGenerationStatisticsDay(String time1, String time2, String sn,String page,String pageSize);

    PageInfo<StatisticalReport> PowerGenerationStatisticsMonth(String date, String plantId, String page, String pageSize);

    PageInfo<StatisticalReport> PowerGenerationStatisticsYear(String date, String plantId, String page, String pageSize);
}
