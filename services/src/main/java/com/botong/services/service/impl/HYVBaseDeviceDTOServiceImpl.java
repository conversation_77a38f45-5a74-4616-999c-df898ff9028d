package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYVBaseDeviceDTOMapper;
import com.botong.services.service.HYVBaseDeviceDTOService;
import com.botong.services.utils.PageHelp1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.AreaDeviceDTO;
import entityDTO.AreaStatisticsForm;
import entityDTO.VBaseDeviceDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import utils.ListPageUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class HYVBaseDeviceDTOServiceImpl extends ServiceImpl<HYVBaseDeviceDTOMapper, VBaseDeviceDTO> implements HYVBaseDeviceDTOService {
    @Autowired
    private HYVBaseDeviceDTOMapper hyvBaseDeviceDTOMapper;

//    @Override
//    public PageInfo<AreaDeviceDTO> selectDay(String time1, String town, String address1, Integer page, Integer pageSize) {
//        String[] split = time1.split("-");
//        StringBuffer buffer = new StringBuffer();
//        PageHelp1 pageHelp1 = new PageHelp1();
//        for (int i = 0; i < split.length; i++) {
//            buffer.append(split[i]);
//        }
//        String table = buffer.toString();
//        String time = time1;
//        List<AreaDeviceDTO> list = hyvBaseDeviceDTOMapper.selectAll(time, town, table);
//        ArrayList<List<AreaDeviceDTO>> deviceDTOList = new ArrayList<>();
//        Map<String, List<AreaDeviceDTO>> listMap = list.stream().collect(Collectors.groupingBy(AreaDeviceDTO::getSn));
//        Set<String> SNs = listMap.keySet();
//        for (String sn : SNs) {
//            listMap.get(sn);
//            deviceDTOList.add(listMap.get(sn));
//        }
//        PageInfo info = pageHelp1.pageHelper(deviceDTOList, page, pageSize);
//        return info;
//    }
//    @Override
//    public PageInfo<AreaDeviceDTO> selectMonth(String month, String town, Integer page, Integer pageSize) {
//        PageHelp1 pageHelp1 = new PageHelp1();
//        ArrayList<List<AreaDeviceDTO>> deviceDTOList = new ArrayList<>();
//        List<AreaDeviceDTO> list = hyvBaseDeviceDTOMapper.selectMonth(month, town);
//        Map<String, List<AreaDeviceDTO>> listMap = list.stream().collect(Collectors.groupingBy(AreaDeviceDTO::getSn));
//        Set<String> SNs = listMap.keySet();
//        for (String sn : SNs) {
//            listMap.get(sn);
//            deviceDTOList.add(listMap.get(sn));
//        }
//        PageInfo info = pageHelp1.pageHelper(deviceDTOList, page, pageSize);
//        return info;
//    }
//    @Override
//    public PageInfo<AreaDeviceDTO> selectYear(String year, String town, Integer page, Integer pageSize) {
//        PageHelp1 pageHelp1 = new PageHelp1();
//        List<AreaDeviceDTO> list = hyvBaseDeviceDTOMapper.selectYear(year, town);
//        ArrayList<List<AreaDeviceDTO>> deviceDTOList = new ArrayList<>();
//        Map<String, List<AreaDeviceDTO>> listMap = list.stream().collect(Collectors.groupingBy(AreaDeviceDTO::getSn));
//        Set<String> SNs = listMap.keySet();
//        for (String sn : SNs) {
//            deviceDTOList.add(listMap.get(sn));
//        }
//        PageInfo info = pageHelp1.pageHelper(deviceDTOList, page, pageSize);
//        return info;
//    }

    @Override
    public PageInfo<List<AreaDeviceDTO>> areaStatistics(AreaStatisticsForm areaStatisticsForm) {
//        PageHelper.startPage(areaStatisticsForm.getPage(),areaStatisticsForm.getPageSize());
        String time = areaStatisticsForm.getTime();
        String town = areaStatisticsForm.getTown();
        List<AreaDeviceDTO> areaStatisticsList = new ArrayList<>();
        switch (areaStatisticsForm.getTime().length()){
            case 10:
                String tableName = time.replace("-", "");
                areaStatisticsList = hyvBaseDeviceDTOMapper.areaStatisticsByDay(time, town, tableName);
                break;
            case 7:
                areaStatisticsList =  hyvBaseDeviceDTOMapper.areaStatisticsByMonth(time, town);
                break;
            case 4:
                areaStatisticsList = hyvBaseDeviceDTOMapper.areaStatisticsByYear(time, town);
            default:
        }
        List<List<AreaDeviceDTO>> deviceDTOList = new ArrayList<>();
        Map<String, List<AreaDeviceDTO>> listMap = areaStatisticsList.stream().collect(Collectors.groupingBy(AreaDeviceDTO::getSn));
        Set<String> SNs = listMap.keySet();
        for (String sn : SNs) {
            listMap.get(sn);
            deviceDTOList.add(listMap.get(sn));
        }
        List<List<AreaDeviceDTO>> lists = ListPageUtils.pageBySubAreaDeviceList(deviceDTOList, areaStatisticsForm.getPage(), areaStatisticsForm.getPageSize());
        PageInfo<List<AreaDeviceDTO>> areaStatisticsPageInfo = new PageInfo<>(lists);
        areaStatisticsPageInfo.setTotal(deviceDTOList.size());
        return areaStatisticsPageInfo;
    }
}
