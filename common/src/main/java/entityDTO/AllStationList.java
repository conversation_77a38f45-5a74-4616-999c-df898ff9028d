package entityDTO;

import lombok.Data;

import java.io.Serializable;

//总电站数量点进去列表
@Data
public class AllStationList implements Serializable {

    //新增配电箱状态
    private String distribution;


    private String address1;

    private String createdate;
    //电站id
    private String plantId;
    //运行状态
    private int status;
    //站点名称
    private String name;
    //装机容量
    private String peakPower;
    //逆变器数量
    private int deviceCount;
    //类型
    private int plantType;
    //
    private String time;
    //建站日期
    private String createDate;
    //实时功率
    private String realTimePower;
    //工作效率
    private String workEfficiency;
    //日发电量
    private String todayEnergy;
    //月发电量
    private String monthEnergy;
    //年发电量
    private String yearEnergy;
    //日等效小时数
    private String hoursperday;
    //总发电量
    private String totalEnergy;
    //地址
    private String city;
    //设备sn
    private String sn;

    private String plantUid;
    //电话
    private String ownerPhone;

    private String owner;

    //字符串类型的电站id
    private String plantIdstr;
    //电站名称
    private String stationName;

}
