package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.services.vo.MapVo;
import com.botong.services.vo.UserElectricityVo;
import entity.BtoStationBase;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 电站基本信息
 * 
 * <AUTHOR> @date 2022-08-15 15:35:23
 */
@Mapper
public interface BtoStationBaseMapper extends BaseMapper<BtoStationBase> {

    UserElectricityVo userElectricityInfo(@Param("plantId") String plantId, @Param("time") String time,@Param("tableName") String tableName);

    MapVo userMapInfo(@Param("plantId") String plantId);
}
