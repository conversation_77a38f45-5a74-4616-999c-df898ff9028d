<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.VEventMeanMapper">
    <resultMap type="entity.VEventMean" id="VEventMeanMap">
        <result property="plantId" column="plant_id"/>
        <result property="startTime" column="start_time"/>
        <result property="status" column="status"/>
    </resultMap>
    <update id="updateHostNoGrid">
            UPDATE bto_device_event
            SET end_time = now(),
                `status` = '1'
            WHERE
                start_time LIKE '${time}'
              AND end_time IS NULL
              AND `status` = '0'
              AND alarm_message LIKE '%No Grid Error%'
              AND sn IN ( SELECT DISTINCT ( sn )
                          FROM bto_device_${time3} WHERE time LIKE '${time2}%' AND power > 0 );

    </update>

    <select id="selectCountDay" resultType="int">
        SELECT
            count(*)
        FROM
            (
                SELECT
                    a.*
                FROM
                    v_event_r a
                        LEFT JOIN v1_station_device b ON a.sn = b.sn
                WHERE
                    start_time LIKE #{time}
                  AND STATUS = '0'
                GROUP BY
                    b.plant_id,
                    a.sn,
                    a.alarm_message
            ) q;
    </select>

    <select id="selectCountSun" resultType="int">
        SELECT
            count(
                    DISTINCT ( plant_id ))
        FROM
            v_event_mean a
                LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
             start_time LIKE #{time}
        AND status='0'
    </select>

    <select id="selectCountInHand" resultType="int">
        SELECT
            count(*)
        FROM
            (
                SELECT
                    a.*
                FROM
                    v_event_r a
                        LEFT JOIN v1_station_device b ON a.sn = b.sn
                WHERE
                    start_time LIKE '${time}'
                  AND STATUS in(1,2)
                GROUP BY
                    b.plant_id,
                    a.sn,
                    a.alarm_message
            ) q;
    </select>

    <select id="selectCountHand" resultType="int">
        SELECT
            count(*)
        FROM
            (
                SELECT
                    a.*
                FROM
                    v_event_r a
                        LEFT JOIN v1_station_device b ON a.sn = b.sn
                WHERE
                    start_time LIKE #{time}
                  AND STATUS = '0'
                GROUP BY
                    b.plant_id,
                    a.sn,
                    a.alarm_message
            ) q;
    </select>



</mapper>