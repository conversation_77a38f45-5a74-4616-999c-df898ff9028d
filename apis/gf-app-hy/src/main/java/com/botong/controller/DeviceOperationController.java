package com.botong.controller;

import com.botong.services.service.DeviceOperationService;
import entityDTO.ModifyStationForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

@RestController
@RequestMapping("DeviceOperationController")
@Api(tags = "电站操作" )
public class DeviceOperationController {
    @Autowired
    private DeviceOperationService deviceOperationService;

    @ApiOperation("查询收藏电站列表")
    @GetMapping("selectDeviceByCtrl")
    public AjaxResult selectDeviceByCtrl(Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",deviceOperationService.selectDeviceByCtrl(page, pageSize));
    }

    @ApiOperation("收藏电站")
    @GetMapping("collectDeviceOperation")
    public AjaxResult collectDeviceOperation(String plantId){
        return AjaxResult.success("操作成功",deviceOperationService.collectDeviceOperation(plantId));
    }

    @ApiOperation("取消收藏")
    @GetMapping("cancelCollectDeviceOperation")
    public AjaxResult cancelCollectDeviceOperation(String plantId){
        return AjaxResult.success("操作成功",deviceOperationService.cancelCollectDeviceOperation(plantId));
    }

    @ApiOperation("编辑电站，先查询电站信息")
    @GetMapping("selectDeviceInformation")
    public AjaxResult selectDeviceInformation(String plantId){
        return AjaxResult.success("操作成功",deviceOperationService.selectDeviceInformation(plantId));
    }

    @ApiOperation("编辑电站，修改电站信息")
    @PostMapping("/modifyDeviceOperation")
    public AjaxResult modifyDeviceOperation(@RequestBody ModifyStationForm modifyStationForm){
        return AjaxResult.success("操作成功",deviceOperationService.modifyDeviceOperation(modifyStationForm));
    }

    @ApiOperation("删除电站")
    @GetMapping("deleteDeviceOperation")
    public AjaxResult deleteDeviceOperation(String plantId,String password,String userUid){
        return AjaxResult.success("操作成功",deviceOperationService.deleteDeviceOperation(plantId,password,userUid));
    }
}
