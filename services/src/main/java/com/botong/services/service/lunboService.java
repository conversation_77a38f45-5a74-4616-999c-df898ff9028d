package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.Carousel;
import entityDTO.StationLunBo;
import entityDTO.siteCarouselPage;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public interface lunboService extends IService<StationLunBo> {
    PageInfo<Carousel> stationCarousel(Integer page,Integer pageSize);

    HashMap<String,Object> sevenAndSixEnergy();
}