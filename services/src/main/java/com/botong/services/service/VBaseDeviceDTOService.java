package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.VBaseDeviceDTO;

import java.util.List;

public interface VBaseDeviceDTOService extends IService<VBaseDeviceDTO> {
    PageInfo<VBaseDeviceDTO> selectDay(String time, String state, String city, String address ,Integer page,Integer pageSize);

    PageInfo<VBaseDeviceDTO> selectMonth(String time, String state, String city, String address ,Integer page,Integer pageSize);

    PageInfo<VBaseDeviceDTO> selectYear(String year, String state, String city, String address ,Integer page,Integer pageSize);
}
