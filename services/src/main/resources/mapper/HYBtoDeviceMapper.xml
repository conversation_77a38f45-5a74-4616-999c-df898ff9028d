<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYBtoDeviceMapper">

    <resultMap id="ElectricityStatisticsVoMap" type="vo.ElectricityStatisticsVo">
        <result property="dayElectricity" column="dayElectricity"/>
        <result property="monthElectricity" column="monthElectricity"/>
        <result property="yearElectricity" column="yearElectricity"/>
        <result property="allElectricity" column="allElectricity"/>
    </resultMap>
    <resultMap type="entity.BtoDevice" id="btoDeviceMap">
        <result property="dataloggerSn" column="datalogger_sn"/>
        <result property="sn" column="sn"/>
        <result property="ipv1" column="ipv1"/>
        <result property="ipv2" column="ipv2"/>
        <result property="ipv3" column="ipv3"/>
        <result property="vpv1" column="vpv1"/>
        <result property="vpv2" column="vpv2"/>
        <result property="vpv3" column="vpv3"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="power" column="power"/>
        <result property="powerFactor" column="power_factor"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="monthEnergy" column="month_energy"/>
        <result property="yearEnergy" column="year_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="temperature" column="temperature"/>
        <result property="fac" column="fac"/>
        <result property="fac1" column="fac1"/>
        <result property="fac2" column="fac2"/>
        <result property="time" column="time"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <update id="updatePassword">
        UPDATE bto_user_list
        SET password=#{password}
        WHERE c_user_uid=#{userUid};
    </update>

    <select id="inverterElectricityInfo" parameterType="string" resultMap="ElectricityStatisticsVoMap">
        SELECT
            sum( today_energy ) dayElectricity,
            sum( month_energy ) monthElectricity,
            sum( year_energy ) yearElectricity,
            sum( total_energy ) allElectricity
        FROM(
                SELECT
                    today_energy,
                    month_energy,
                    year_energy,
                    total_energy
                FROM
                    v_device_list aa
                        LEFT JOIN (
                        SELECT
                            sn,
                            today_energy,
                            month_energy,
                            year_energy,
                            total_energy
                        FROM
                            ${tableName}
                        WHERE
                            time LIKE #{time}
                        GROUP BY
                            SN,
                            time
                        ORDER BY
                            time DESC
                    ) bb ON aa.device_sn = bb.sn
                WHERE
                    aa.special = '1'
                  AND
                    sn IS NOT NULL
                GROUP BY
                    sn
            ) q;
    </select>
    <select id="getCurrentPowerBySn" parameterType="string" resultType="float">
        select power from ${tableName} where datalogger_sn = #{sn} ORDER BY update_time desc limit 1
    </select>
    <select id="getDayMonthYearAllInfo" resultMap="btoDeviceMap">
        select today_energy,month_energy,year_energy,total_energy from ${tableName} where datalogger_sn = #{dataloggerSn}
        order by update_time desc limit 1
    </select>
    <select id="powerStationDay" resultType="entityDTO.xuDto">
        SELECT
            sum( power ) power
        FROM
            `bto_device_total`
        WHERE
            special = '1'
          AND LEFT ( update_time, 13 )= '${time}';
    </select>
    <select id="returnPrimaryId" resultType="entityDTO.returnPrimaryId">
        SELECT
        plant_id plantId,
        plant_uid plantUid,
        sn sn
        FROM
        v1_station_device
        WHERE
        <if test="plantId!=null and plantId!=''">
            plant_id = '${plantId}'
        </if>
        <if test="plantUid!=null and plantUid!='' ">
            plant_uid = '${plantUid}'
        </if>
    </select>
</mapper>