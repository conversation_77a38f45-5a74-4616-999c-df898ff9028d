package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.BtoUserList;
import entityDTO.*;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public interface BtoStationEnginerService extends IService<BtoStationEnginerDTO> {
        /**
         * 注册电站 --插入电站信息到 bto_station_list 和bto_station_base表中
         * @param registerStationForm
         * @return
         */
        Integer registerStation(RegisterStationForm registerStationForm);


        /**
         * 通过电站名查询电站Uid
         * @param name
         * @return
         */
        String selectPlantUidByName(String name);

        /**
         * 查询所有电站
         * @return
         */
         List<BtoStationEnginerDTO> SelStationAll();

        /**
         *
         * @param stationName
         * @return
         */
        Integer CheckStationName(String stationName);

        /**
         * 通过电站Id查询整线值
         * @param plantUid
         * @return
         */
        String selectPlantIdBySpecial(String plantId);

        Integer registerStation2(RegisterStationForm registerStationForm);

        PageInfo<AlarmAnalysisengnier> RequirementsPowerStationAlarms(Integer page, Integer pagesize);
}
