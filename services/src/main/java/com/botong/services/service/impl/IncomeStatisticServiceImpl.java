package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.IncomeStatisticMapper;

import com.botong.services.service.IncomeStatisticService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import entityDTO.IncomeStatisticDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class IncomeStatisticServiceImpl extends ServiceImpl<IncomeStatisticMapper, IncomeStatisticDTO> implements IncomeStatisticService {

    @Autowired
    private IncomeStatisticMapper incomeStatisticMapper;

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsDay( String plant_id,String time1,String time2,Integer page,Integer pageSize ){
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String[] split = plant_id.split(",");
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        String emm="bto_device_"+time1;
        List<IncomeStatisticDTO> incomeStatisticLunbos=incomeStatisticMapper.IncomeStatisticsDay(Arrays.asList(split),emm,time2,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getToday_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;

    }

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsMonth( String plant_id, String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String[] split = plant_id.split(",");
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=incomeStatisticMapper.IncomeStatisticsMonth(Arrays.asList(split),time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getMonth_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsYear( String plant_id, String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String[] split = plant_id.split(",");
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=incomeStatisticMapper.IncomeStatisticsYear(Arrays.asList(split),time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getYear_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsAllDay(  String time1,String time2,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        String emm="bto_device_"+time1;
        List<IncomeStatisticDTO> incomeStatisticLunbos=incomeStatisticMapper.IncomeStatisticsAllDay(emm,time2,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getToday_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsAllMonth(  String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=incomeStatisticMapper.IncomeStatisticsAllMonth(time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getMonth_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }
    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsAllYear(  String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=incomeStatisticMapper.IncomeStatisticsAllYear(time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getYear_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }
}
