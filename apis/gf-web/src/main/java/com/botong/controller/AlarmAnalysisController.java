package com.botong.controller;

import com.botong.services.service.AlarmAnalysisService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.AlarmAnalysis;
import entityDTO.AlarmAnalysisConditionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import java.util.List;

@Api(tags = "实时报警")
@RestController
@RequestMapping("AlarmAnalysis")
public class AlarmAnalysisController {

    @Autowired
    private AlarmAnalysisService service;

//    @ApiOperation("实时报警——列表表格")
//    @GetMapping("AlarmAnalysisListForm")
//    public AjaxResult AlarmAnalysisListForm(Integer page,Integer pageSize){
//        return AjaxResult.success("操作成功",service.AlarmAnalysisListForm(page,pageSize));
//    }
//
    @ApiOperation("实时报警——列表表格plantUid")
    @GetMapping("AlarmAnalysisListFormplantUid")
    public AjaxResult AlarmAnalysisListFormplantUid(Integer page,Integer pageSize,String plantUid){
        return AjaxResult.success("操作成功",service.AlarmAnalysisListFormplantUid(page,pageSize,plantUid));
    }

    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery 实时报警——时间区间查询——精确时分秒 plantUid time 非全
    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery time plantUid全
//    @ApiOperation("实时报警——时间区间查询——精确时分秒——plantUid")
//    @GetMapping("AlarmAnalysisTimeIntervalQuery")
//    public AjaxResult AlarmAnalysisTimeIntervalQuery(String startTime,String endTime,int page,int pageSize,String plantUid){
//        return AjaxResult.success("操作成功",service.AlarmAnalysisTimeIntervalQuery(startTime,endTime,page,pageSize,plantUid));
//    }
//
//    @ApiOperation("实时报警——多条件查询")
//    @GetMapping("AlarmAnalysisTconditionQuery")
//    public AjaxResult AlarmAnalysisTconditionQuery(String startTime,int page,int pageSize,String name,String sn,String name1){
//        return AjaxResult.success("操作成功",service.AlarmAnalysisTconditionQuery(startTime,page,pageSize,name,name1,sn));
//    }

    @ApiOperation("实时报警——历史报警_传入告警级别grade")
    @GetMapping("AlarmAnalysisHistoryAlarm")
    public AjaxResult AlarmAnalysisHistoryAlarm(String startTime,String endTime,String page,String pageSize,int grade){
        return AjaxResult.success("操作成功",service.AlarmAnalysisHistoryAlarm(startTime,endTime,page,pageSize,grade));
    }

    @ApiOperation("实时报警——历史报警全部_人名，事件事件，告警等级3")
    @GetMapping("AlarmAnalysisHistoryAlarmQuantime3")
    public AjaxResult AlarmAnalysisHistoryAlarmAll3(Integer page,Integer pageSize,String name1,String startTime,String endTime,String grade){
        return AjaxResult.success("操作成功",service.AlarmAnalysisHistoryAlarmAll3(page,pageSize,name1,startTime,endTime,grade));
    }

    @ApiOperation("实时报警——历史报警全部_人名，事件事件，告警等级2")
    @GetMapping("AlarmAnalysisHistoryAlarmQuantime2")
    public AjaxResult AlarmAnalysisHistoryAlarmAll2(Integer page,Integer pageSize,String name1,String grade){
        return AjaxResult.success("操作成功",service.AlarmAnalysisHistoryAlarmAll2(page,pageSize,name1,grade));
    }

    @ApiOperation("实时报警——历史报警全部")
    @GetMapping("AlarmAnalysisHistoryAlarmQuann")
    public AjaxResult AlarmAnalysisHistoryAlarmQuann(String page,String pageSize){
        return AjaxResult.success("操作成功",service.AlarmAnalysisHistoryAlarmQuann(page,pageSize));
    }
    @ApiOperation("实时报警——历史报警全部PlantUid")
    @GetMapping("AlarmAnalysisHistoryAlarmAll")
    public AjaxResult AlarmAnalysisHistoryAlarmAll(String startTime,String endTime,String page,String pageSize,int grade,String plantUid){
        return AjaxResult.success("操作成功",service.AlarmAnalysisHistoryAlarmAll(startTime,endTime,page,pageSize,grade,plantUid));
    }



    @ApiOperation("实时报警——报警统计总_日月年统一接口，需要截取")
    @GetMapping("totalAlarmStatisticsTime")
    public AjaxResult totalAlarmStatistics(String Time){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTime(Time));
    }

    @ApiOperation("实时报警——报警统计总")
    @GetMapping("totalAlarmStatistics")
    public AjaxResult totalAlarmStatistics(){
        return AjaxResult.success("操作成功",service.totalAlarmStatistics());
    }

    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取")
    @GetMapping("totalAlarmStatisticsTIME")
    public AjaxResult totalAlarmStatisticsTIME(String Time,String plantUid){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIME(Time,plantUid));
    }


    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取")
    @GetMapping("totalAlarmStatisticsTIMETU")
    public AjaxResult totalAlarmStatisticsTIMETU(String Time){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMETU(Time));
    }
    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取")
    @GetMapping("totalAlarmStatisticsTIMEgeren")
    public AjaxResult totalAlarmStatisticsTIMEgeren(String Time,String plantUid){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEgeren(Time,plantUid));
    }


    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，遥信")
    @GetMapping("totalAlarmStatisticsTIMEyaoxin")
    public AjaxResult totalAlarmStatisticsTIMEyaoxin(String Time){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEgerenyaoxin(Time));
    }

    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，遥信")
    @GetMapping("totalAlarmStatisticsTIMEgerenyaoxin")
    public AjaxResult totalAlarmStatisticsTIMEgerenyaoxin(String Time,String plantUid){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEgerennyaoxin(Time,plantUid));
    }




    //时间统计
    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，时间统计")
    @GetMapping("totalAlarmStatisticsTIMEstatistics")
    public AjaxResult totalAlarmStatisticsTIMEstatistics(String Time){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEstatistics(Time));
    }

    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，时间统计")
    @GetMapping("totalAlarmStatisticsTIMEgerenstatistics")
    public AjaxResult totalAlarmStatisticsTIMEgerenstatistics(String Time,String plantUid){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEgerennstatistics(Time,plantUid));
    }

    //设备统计
    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，设备统计")
    @GetMapping("totalAlarmStatisticsTIMEEquipmentStatistics")
    public AjaxResult totalAlarmStatisticsTIMEEquipmentStatistics(String Time){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEEquipmentStatistics(Time));
    }

    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，设备统计")
    @GetMapping("totalAlarmStatisticsTIMEgerenEquipmentStatistics")
    public AjaxResult totalAlarmStatisticsTIMEgerenEquipmentStatistics(String Time,String plantUid){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEgerenEquipmentStatistics(Time,plantUid));
    }

    //等级统计
    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，等级统计")
    @GetMapping("totalAlarmStatisticsTIMELevelStatistics")
    public AjaxResult totalAlarmStatisticsTIMELevelStatistics(String Time){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMELevelStatistics(Time));
    }

    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，遥信")
    @GetMapping("totalAlarmStatisticsTIMEgerenLevelStatistics")
    public AjaxResult totalAlarmStatisticsTIMEgerenLevelStatistics(String Time,String plantUid){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEgerennLevelStatistics(Time,plantUid));
    }

    //列表
    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，列表")
    @GetMapping("totalAlarmStatisticsTIMEyaoxinlist")
    public AjaxResult totalAlarmStatisticsTIMEyaoxinlist(String Time){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEyaoxinlist(Time));
    }

    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，列表")
    @GetMapping("totalAlarmStatisticsTIMEgerenlist")
    public AjaxResult totalAlarmStatisticsTIMEgerenlist(String Time,String plantUid){
        return AjaxResult.success("操作成功",service.totalAlarmStatisticsTIMEgerenlist(Time,plantUid));
    }


    @ApiOperation("运维器事件数据")
    @GetMapping("OperatorEventData")
    public AjaxResult OperatorEventData(Integer page,Integer pageSize,String alarmtime,String city,String endtime){
        return AjaxResult.success("操作成功",service.OperatorEventData(page, pageSize, alarmtime, city,endtime));
    }

    @ApiOperation("运维器数据")
    @GetMapping("OperatorData")
    public AjaxResult OperatorData(Integer page,Integer pageSize,String StartTime,String EndTime,String name){
        return AjaxResult.success("操作成功",service.OperatorData(page,pageSize,StartTime,EndTime,name));
    }

    @ApiOperation("运维器数据_月")
    @GetMapping("OperatorDataMonth")
    public AjaxResult OperatorDataMonth(Integer page,Integer pageSize,String StartTime,String EndTime,String name){
        return AjaxResult.success("操作成功",service.OperatorDataMonth(page,pageSize,StartTime,EndTime,name));
    }

//    @ApiOperation("实时报警——时间区间查询——精确时分秒——time plantUid全--------改")
//    @GetMapping("AlarmAnalysisTimeIntervalAll")
//    public AjaxResult AlarmAnalysisTimeIntervalAll(String Time1,String Time2,String page,String pageSize){
//
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> list = service.AlarmAnalysisTimeIntervalAll(Time1, Time2);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(list);
//        return AjaxResult.success("操作成功",stationLunBoPageInfo);
//    }
//
//    @ApiOperation("实时报警——聚合plantUid_不传时间，传plantUid-----------------改")
//    @GetMapping("AlarmAnalysisPolymerization")
//    public AjaxResult AlarmAnalysisPolymerization(String page,String pageSize,String plantUid){
//        return AjaxResult.success("操作成功",service.AlarmAnalysisPolymerization(page,pageSize,plantUid));
//    }
//    @ApiOperation("实时报警——聚合全部-----------------------改")
//    @GetMapping("AlarmAnalysisPolymerizationAll")
//    public AjaxResult AlarmAnalysisPolymerizationAll(String startTime,String endTime,String page,String pageSize){
//        return AjaxResult.success("操作成功",service.AlarmAnalysisPolymerizationAll(startTime,endTime,page,pageSize));
//    }
//
//
//    @ApiOperation("实时报警——聚合全部——plantUid-------------------------改")
//    @GetMapping("AlarmAnalysisPolymerizationplantUid")
//    public AjaxResult AlarmAnalysisPolymerizationplantUid(String startTime,String endTime,String page,String pageSize,String plantUid){
//        return AjaxResult.success("操作成功",service.AlarmAnalysisPolymerizationplantUid(startTime,endTime,page,pageSize,plantUid));
//    }

    @ApiOperation("实时报警----聚合&列表--汇总--四合一")
    @PostMapping("AlarmAnalysisByCondition")
    public AjaxResult AlarmAnalysisByCondition(@RequestBody AlarmAnalysisConditionDTO alarmAnalysisConditionDTO) throws NoSuchFieldException, ClassNotFoundException {
        return service.AlarmAnalysisByCondition(alarmAnalysisConditionDTO);
    }

    @ApiOperation("查询事件-数据对照表")
    @GetMapping("getEventMean")
    public AjaxResult getEventMean(){
        return AjaxResult.success("查询成功",service.getEventMean());
    }
}
