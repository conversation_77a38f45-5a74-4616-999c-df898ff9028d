package com.botong.controller;

import com.botong.services.service.BtoUserService;
import entityDTO.Resource;
import entityDTO.Role;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import java.util.List;

@RestController
@RequestMapping("/user")
@Api(tags = "用户")
@CrossOrigin
public class UserController {
    @Autowired
    BtoUserService btoUserService;

    @GetMapping("/userTest")
    public AjaxResult userTest() {

        //return AjaxResult.success("ok",btoUserService.list());
        return AjaxResult.success("ok",btoUserService.test());
    }


    @ApiOperation("用户列表")
    @GetMapping("BTOuserList")
    public AjaxResult BTOuserList(Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",btoUserService.BTOuserList(page,pageSize));
    }

    @ApiOperation("用户列表_模糊查询")
    @GetMapping("BTOuserListLike")
    public AjaxResult BTOuserListLike(String userName,String page,String pageSize){
        return AjaxResult.success("操作成功",btoUserService.BTOuserListLike(userName,page,pageSize));
    }

    @ApiOperation("根据用户id，查询该用户角色身份id")
    @PostMapping("UserRoleQuery")
    public AjaxResult UserRoleQuery(String userUid){
        return AjaxResult.success("查询成功",btoUserService.UserRoleQuery(userUid));
    }

    @ApiOperation("根据用户id，获取权限id")
    @PostMapping("getPermissionId")
    public AjaxResult getPermissionId(String userUid){
        return AjaxResult.success("查询成功",btoUserService.getPermissionId(userUid));
    }

    @ApiOperation("根据角色id，获取权限id")
    @PostMapping("getroleResourceId")
    public AjaxResult getroleResourceId(int roleId){
        return AjaxResult.success("查询成功",btoUserService.getroleResourceId(roleId));
    }

    /**
     * 返回所有资源数据
     */
    @ApiOperation("查询全部权限")
    @GetMapping("getListResource")
    public List<Resource> getListResource() {
        // SQL语句非常简单：select * from resource
        return btoUserService.listResource();
    }

    /**
     * 返回所有角色数据
     */
    @ApiOperation("查询全部角色")
    @GetMapping("getListRole")
    public List<Role> getListRole() {
        // SQL语句非常简单：select * from role
        return btoUserService.listRole();
    }


    @ApiOperation("返回主要id")
    @GetMapping("returnPrimaryId")
    public AjaxResult returnPrimaryId(String plantId,String plantUid){
        return AjaxResult.success("查询成功",btoUserService.returnPrimaryId(plantId,plantUid));
    }

    @ApiOperation("修改密码")
    @PostMapping("updatePassword")
    public AjaxResult updatePassword(String userUid,String password) {
        if (btoUserService.updatePassword(userUid, password) == 1) {
            return AjaxResult.success("修改成功", btoUserService.updatePassword(userUid, password));
        } else {
            return AjaxResult.success("修改失败，用户可能不存在！", btoUserService.updatePassword(userUid, password));
        }
    }
}
