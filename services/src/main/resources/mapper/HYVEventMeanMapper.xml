<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.HYVEventMeanMapper">
    <resultMap type="entity.VEventMean" id="VEventMeanMap">
        <result property="plantId" column="plant_id"/>
        <result property="startTime" column="start_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <select id="selectCountDay" resultType="int">
        SELECT count(*)
        FROM v_device_event a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE b.special = '1'
          AND start_time LIKE #{time};
    </select>

    <select id="selectCountSun" resultType="int">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE special = '1'
          AND status = '2'
    </select>

    <select id="selectCountInHand" resultType="int">
        SELECT count(*)
        FROM v_device_event a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE b.special = '1'
          AND start_time LIKE #{time}

          AND STATUS != '1';
    </select>

    <select id="selectCountHand" resultType="int">
        SELECT count(*)
        FROM v_device_event a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE b.special = '1'
          AND start_time LIKE #{time}
          AND status = 0
    </select>
    <select id="selectProcessedNum" resultType="java.lang.Integer">
        SELECT count(1)
        FROM v_event_mean a
                 LEFT JOIN v_device_list b ON a.sn = b.device_sn
        WHERE b.special = '1'
          AND start_time LIKE '${time}'
          AND end_time &lt; DATE_SUB(now(), INTERVAL 30 MINUTE);
    </select>

    <select id="unhandledNum" resultType="java.lang.Integer">
        SELECT
	count( 1 )
FROM
	v_sn_hy a
	LEFT JOIN bto_device_event b ON a.device_sn = b.sn
WHERE
	a.special = '1'
	AND b.`status` = '0'
	AND left(b.start_time,10)=CURDATE()
    </select>
</mapper>