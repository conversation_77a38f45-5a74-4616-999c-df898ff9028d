package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel("逆变器列表-筛选条件")
@Data
public class DeviceListForm implements Serializable {
    @ApiModelProperty(value = "页码",notes = "页码")
   private Integer page;

    @ApiModelProperty(value = "页面大小",notes = "页面大小")
   private Integer pageSize;

    @ApiModelProperty(value = "状态的字符串数组",notes = "状态的字符串数组")
   private String[] statusArray;
}
