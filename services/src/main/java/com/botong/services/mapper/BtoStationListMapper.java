package com.botong.services.mapper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import entity.BtoStationList;
import entityDTO.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * 电站列表
 *
 * <AUTHOR> @date 2022-08-11 15:36:33
 */
@Mapper
public interface BtoStationListMapper extends BaseMapper<BtoStationList> {

    List<BtoStationList> sqlStaion();

    List<BTOstationListDTO> StationAllenergy(@Param("tableNme") String tableNme, @Param("nowtime10") String nowtime10, @Param("nowtime20") String nowtime20);

    Page<siteCarouselPage> selectPages(Page<siteCarouselPage> page, QueryWrapper<Object> wrapper);

    Integer StationStatus2(@Param("curTime") String curTime);

    Integer Stationonline(@Param("nearTime")String nearTime, @Param("tableName") String tableName);

    //    List<AllStationList> selectListt(@Param("page") String page,@Param("pageSize") String pageSize,@Param("time") String time,@Param("time2")String time2);
    List<AllStationList> selectListt(@Param("time") String time, @Param("time2") String time2);

    List<AllStationList> ListOfTotalPowerStationsstationName(@Param("time") String time, @Param("time2") String time2, @Param("stationName") String stationName);

    Object ListOfTotalPowerStationsPhone(@Param("time") String time, @Param("plantUid") String plantUid);

    //获取总电站数
    Integer getAllStationNum();

    //获取告警电站数
    Integer getWarnStationNum(@Param("time") String time);

    //获取告警记录数
    Integer getWarnRecords();

    //获取年月日总发电量
    ArrayList<HashMap<String, Float>> getEnergy(@Param("time") String time, @Param("tableName") String tableName);
    Double getDayElectricity();
    Double getMonthElectricity();
    Double getAllElectricity();

    //获取在线电站数
    Integer getOnlineStationNum();

    Object TotalInstalledCapacity();

    /**
     *    获取日/月/总 发电量
     */
    HashMap<String,Double> getStationEnergy(@Param("date") String date);

    /**
     * 获取告警运维器数量
     * @return
     */
    Integer getWarnOperatorNum();



    /**
     *  获取日/月/总 发电量
     */
    HashMap<String, Double> getElectricity();

    Double getAllPeakPower();
//    double getStationEnergyByMonth(@Param("date") String month);

    /**
     *  获取日/月/总 发电量
     */
//    double getStationEnergyByAll();
}
