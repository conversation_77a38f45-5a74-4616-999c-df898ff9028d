package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.BtoIndex;

import java.util.List;

public interface BtoIndexService extends IService<BtoIndex> {
    PageInfo<BtoIndex> SelectBtoIndex(Integer page, Integer pageSize);

    PageInfo<BtoIndex> LikeSelectBtoIndex(String IndexName, Integer page,Integer pageSize);

    int InsertBtoIndex(BtoIndex btoIndex);

    int UpdateBtoIndex(String indexId  ,String indexName);

    int DeleteBtoIndex(String IndexId);

}
