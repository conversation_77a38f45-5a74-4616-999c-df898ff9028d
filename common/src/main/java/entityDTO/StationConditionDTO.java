package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class StationConditionDTO implements Serializable {
    /**
     * 运行状态
     */
    private String status;

    /**
     * 电站类型
     */
    private Integer type;

    /**
     * 电站名称
     */
    private String stationName;

    /**
     * 装机容量(最小)
     */
    private String minPeakPower;
    /**
     * 装机容量(最大)
     */
    private String maxPeakPower;

    /**
     * 所在区域
     */
    private String town;

    /**
     * （页数-1）*条目数
     */
    private Integer a;
    /**
     *  条目数
     */
    private Integer b;


}
