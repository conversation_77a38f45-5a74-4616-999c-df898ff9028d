package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.BtoDeviceRealTimeDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface deviceRealtimeDataMapper extends BaseMapper<BtoDeviceRealTimeDTO> {
    List<BtoDeviceRealTimeDTO> selectdeviceRealtimeData(@Param("time") String time,@Param("sn") String sn,@Param("page") Integer page,@Param("pageSize") Integer pageSize);
}
