package com.botong.services;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

import java.util.UUID;


@MapperScan("com.botong.services.mapper") //mapper扫描
@EnableCaching
@SpringBootApplication
//@EnableConfigurationProperties(value = Properties.class)
public class ServicesApplication {
    public static void main(String[] args) {
        SpringApplication.run(ServicesApplication.class, args);
    }
}
