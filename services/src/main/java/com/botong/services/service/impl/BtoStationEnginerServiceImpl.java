package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoStationEnginerMapper;
import com.botong.services.service.BtoStationEnginerService;
import com.botong.services.service.userEnginerService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoUserList;
import entityDTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class BtoStationEnginerServiceImpl extends ServiceImpl<BtoStationEnginerMapper, BtoStationEnginerDTO> implements BtoStationEnginerService {
    @Autowired
    BtoStationEnginerMapper btoStationEnginerMapper;
    @Autowired
    private com.botong.services.service.userEnginerService userEnginerService;
    @Override
    public Integer registerStation(RegisterStationForm registerStationForm) {

        BtoStationEnginerDTO btoStationEnginerDTO = new BtoStationEnginerDTO();
        //通过用户名查询用户id
        String userUid = null;
        Integer userId = null;
        String enginerId=null;
        Integer special = null;
        String ownerPhone = null;
        String ownerEmail = null;

        List<userEnginerDTO> user = userEnginerService.selectUid(registerStationForm.getUsername());
        for (userEnginerDTO user1:user) {
            userUid = user1.getCUserUid();
            userId = user1.getCUserId();
            enginerId = user1.getEnginerId();
            special = user1.getSpecial();
            ownerEmail =user1.getCUserEmail();
            ownerPhone = user1.getCUserTel();
        }
        //生成UUID
        String uuid_plant = UUID.randomUUID().toString().toUpperCase();

        //生成（13-15位纯数字）的电站id
        int length = (int) Math.round(Math.
                random()*3);
        String plantId = String.valueOf(getNumber(length+13));

        //获得系统当前时间(DateTime格式)
        Date date = new Date();
        Timestamp currentTime = new Timestamp(date.getTime());

        //将工程师填写的电站注册表单信息存入实体类
        btoStationEnginerDTO.setUserUid(userUid);
        btoStationEnginerDTO.setPlantUid(uuid_plant);
        btoStationEnginerDTO.setUserId(userId);
        btoStationEnginerDTO.setName(registerStationForm.getPlantName());
        btoStationEnginerDTO.setPeakPower(registerStationForm.getPeakPower());
        btoStationEnginerDTO.setPlantId(plantId);
        btoStationEnginerDTO.setPlanType(registerStationForm.getPlanType());
        btoStationEnginerDTO.setElectrovalency(registerStationForm.getElectroValency());
        btoStationEnginerDTO.setAddress1(registerStationForm.getAddr());
        btoStationEnginerDTO.setLongitude(registerStationForm.getLongitude());
        btoStationEnginerDTO.setLatitude(registerStationForm.getLatitude());
        btoStationEnginerDTO.setOwner(registerStationForm.getUsername());
        btoStationEnginerDTO.setOwnerPhone(ownerPhone);
        btoStationEnginerDTO.setOwnerEmail(ownerEmail);
        btoStationEnginerDTO.setBusinessType(registerStationForm.getBusinessType());
        btoStationEnginerDTO.setCountry(registerStationForm.getCountry());
        btoStationEnginerDTO.setState(registerStationForm.getState());
        btoStationEnginerDTO.setCity(registerStationForm.getCity());
        btoStationEnginerDTO.setTown(registerStationForm.getTown());
        btoStationEnginerDTO.setEnginerId(registerStationForm.getEnginerId());
        btoStationEnginerDTO.setCreateTime(currentTime);
        btoStationEnginerDTO.setUpdateTime(currentTime);
        btoStationEnginerDTO.setCreateDate(currentTime);
        btoStationEnginerDTO.setSpecial(special);
        btoStationEnginerDTO.setLocale("en-US");

        if (baseMapper.insert(btoStationEnginerDTO)==1&& btoStationEnginerMapper.insertBase(btoStationEnginerDTO)==1){
            return 1;
        }else {
            return 0;
        }
    }

    @Override
    public String selectPlantUidByName(String name) {
        return btoStationEnginerMapper.selectPlantUidByName(name);
    }

    @Override
    public List<BtoStationEnginerDTO> SelStationAll() {
        return btoStationEnginerMapper.SelStationAll();
    }

    @Override
    public Integer CheckStationName(String stationName) {
        if (btoStationEnginerMapper.CheckStationName(stationName).isEmpty()){
            return 0;
        }else {
            return 1;
        }
    }

    @Override
    public String selectPlantIdBySpecial(String plantId) {
        return btoStationEnginerMapper.selectPlantIdBySpecial(plantId);
    }


    //工程师第二版，注册成功后，把河源用户表中的字段修改为已注册
    public Integer registerStation2(RegisterStationForm registerStationForm) {
        BtoStationEnginerDTO btoStationEnginerDTO = new BtoStationEnginerDTO();
        //通过用户名查询用户id
        String userUid = null;
        Integer userId = null;
        String enginerId=null;
        Integer special = null;
        String ownerPhone = null;
        String ownerEmail = null;

        List<userEnginerDTO> user = userEnginerService.selectUid(registerStationForm.getUsername());
        for (userEnginerDTO user1:user) {
            userUid = user1.getCUserUid();
            userId = user1.getCUserId();
            enginerId = user1.getEnginerId();
            special = user1.getSpecial();
            ownerEmail =user1.getCUserEmail();
            ownerPhone = user1.getCUserTel();
        }
        //生成UUID
        String uuid_plant = UUID.randomUUID().toString().toUpperCase();

        //生成（13-15位纯数字）的电站id
        int length = (int) Math.round(Math.
                random()*3);
        String plantId = String.valueOf(getNumber(length+13));

        //获得系统当前时间(DateTime格式)
        Date date = new Date();
        Timestamp currentTime = new Timestamp(date.getTime());

        //将工程师填写的电站注册表单信息存入实体类
        btoStationEnginerDTO.setUserUid(userUid);
        btoStationEnginerDTO.setPlantUid(uuid_plant);
        btoStationEnginerDTO.setUserId(userId);
        btoStationEnginerDTO.setName(registerStationForm.getPlantName());
        btoStationEnginerDTO.setPeakPower(registerStationForm.getPeakPower());
        btoStationEnginerDTO.setPlantId(plantId);
        btoStationEnginerDTO.setPlanType(registerStationForm.getPlanType());
        btoStationEnginerDTO.setElectrovalency(registerStationForm.getElectroValency());
        btoStationEnginerDTO.setAddress1(registerStationForm.getAddr());
        btoStationEnginerDTO.setLongitude(registerStationForm.getLongitude());
        btoStationEnginerDTO.setLatitude(registerStationForm.getLatitude());
        btoStationEnginerDTO.setOwner(registerStationForm.getUsername());
        btoStationEnginerDTO.setOwnerPhone(ownerPhone);
        btoStationEnginerDTO.setOwnerEmail(ownerEmail);
        btoStationEnginerDTO.setBusinessType(registerStationForm.getBusinessType());
        btoStationEnginerDTO.setCountry(registerStationForm.getCountry());
        btoStationEnginerDTO.setState(registerStationForm.getState());
        btoStationEnginerDTO.setCity(registerStationForm.getCity());
        btoStationEnginerDTO.setTown(registerStationForm.getTown());
        btoStationEnginerDTO.setEnginerId(registerStationForm.getEnginerId());
        btoStationEnginerDTO.setCreateTime(currentTime);
        btoStationEnginerDTO.setUpdateTime(currentTime);
        btoStationEnginerDTO.setCreateDate(currentTime);
        btoStationEnginerDTO.setSpecial(special);
        btoStationEnginerDTO.setLocale("en-US");

        if (baseMapper.insert(btoStationEnginerDTO)==1&& btoStationEnginerMapper.insertBase(btoStationEnginerDTO)==1){
            btoStationEnginerMapper.ModifyAlreadyregistered(registerStationForm.getHyUserUid());
            return 1;
        }else {
            return 0;
        }
    }

    @Override
    public PageInfo<AlarmAnalysisengnier> RequirementsPowerStationAlarms(Integer page,Integer pagesize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pagesize));
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        String time= simpleDateFormat.format(date);
        List<AlarmAnalysisengnier> alarmAnalyses= btoStationEnginerMapper.RequirementsPowerStationAlarms(time);
        PageInfo<AlarmAnalysisengnier> stationLunBoPageInfo = new PageInfo<>(alarmAnalyses);
        return stationLunBoPageInfo;
    }



    //注册电站随机生成id
    public static String getNumber(Integer length) {
        String uid = "";
        for (int i = 0; i < length; i++) {
            String randChar = String.valueOf(Math.round(1+Math.random() * 9));
            uid = uid.concat(randChar);
        }
        return uid;
    }
}

