package com.botong.controller;

import com.botong.services.service.HYABtoDeviceListService;
import com.botong.services.service.HYABtoStationBaseService;
import com.botong.services.service.HYAPPIndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("AdminIndex")
@Api(tags = "管理员APP主页页面")
public class HYAdminIndexController {
    @Autowired
    private HYAPPIndexService hyappIndexService;

    @ApiOperation("获取所有、正常、告警、在线电站数量、告警记录数")
    @GetMapping("getStationNum")
    public AjaxResult getStationNum(){
        return AjaxResult.success("请求成功", hyappIndexService.getStationNum());
    }

    @ApiOperation("获取日-月-年-总发电量")
    @PostMapping("getEnergy")
    public AjaxResult getEnergy(){
        HashMap<String, Double> energyMap = hyappIndexService.getEnergyAndPeakPower();
        return AjaxResult.success("请求成功", energyMap);
    }

    @ApiOperation("总装机容量")
    @GetMapping("TotalInstalledCapacity")
    public  AjaxResult  TotalInstalledCapacity(){
        return AjaxResult.success("查询成功",hyappIndexService.TotalInstalledCapacity());
    }
}
