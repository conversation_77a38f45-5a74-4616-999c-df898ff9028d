package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.StatisticalReportMapper;
import com.botong.services.service.StatisticalReportService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.StatisticalReport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class StatisticalReportServiceImpl extends ServiceImpl<StatisticalReportMapper, StatisticalReport> implements StatisticalReportService {

    @Autowired
    private StatisticalReportMapper statisticalReportMapper;

    @Override
    public PageInfo<StatisticalReport> PowerGenerationStatisticsDay(String time1, String time2, String sn,String page,String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<StatisticalReport> statisticalReport = new ArrayList<>();
        String shi= "bto_device_"+time1;
        List<StatisticalReport> stationLunBos = statisticalReportMapper.PowerGenerationStatisticsDay(shi,time2,sn,page,pageSize);
        for (StatisticalReport ce : statisticalReport) {
            System.out.println(ce.getTodayEnergy());
        }
        PageInfo<StatisticalReport> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<StatisticalReport> PowerGenerationStatisticsMonth(String date, String plantId, String page, String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<StatisticalReport> stationLunBos = statisticalReportMapper.PowerGenerationStatisticsMonth(date,plantId);
        PageInfo<StatisticalReport> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<StatisticalReport> PowerGenerationStatisticsYear(String date, String plantId, String page, String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<StatisticalReport> stationLunBos = statisticalReportMapper.PowerGenerationStatisticsYear(date,plantId);
        PageInfo<StatisticalReport> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }


}
