<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.BtoDeviceTypeMapper">
    <resultMap type="entity.BtoDeviceType" id="btoDeviceTypeMap">
        <result property="model" column="model"/>
    </resultMap>
    <select id="selectAll" resultMap="btoDeviceTypeMap">
        SELECT
            model
        FROM
            v_device_list
        GROUP BY
            model;
    </select>

    <select id="selectLike" resultMap="btoDeviceTypeMap">
        SELECT
            model
        FROM
            v_device_list
        WHERE
            model LIKE #{model}
        GROUP BY
            model;
    </select>
</mapper>