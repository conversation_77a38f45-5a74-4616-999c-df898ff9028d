package com.botong.services.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYBtoDeviceMapper;
import com.botong.services.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoDevice;
import entity.BtoDeviceList;
import entity.BtoStationBase;
import entityDTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import utils.AjaxResult;
import vo.DateElectricityVo;
import vo.ElectricityStatisticsVo;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class HYBtoDeviceServiceImpl extends ServiceImpl<HYBtoDeviceMapper, BtoDevice> implements HYBtoDeviceService{


    @Autowired
    private BtoDeviceService btoDeviceService;

    @Autowired
    private HYBtoDeviceListService hyBtoDeviceListService;

    @Autowired
    private HYBtoDeviceMapper hyBtoDeviceMapper;

    @Autowired
    private HYBtoDeviceService hyBtoDeviceService;

    @Autowired
    private HYBtoStationListService hyBtoStationListService;

    @Autowired
    private HYBtoStationBaseService hyBtoStationBaseService;

    @Override
    @Cacheable(cacheNames = "HY_WEB",key = " 'inverterElectricityInfo' ",cacheManager = "HY_Web_Cache")
    public List<ElectricityStatisticsVo> inverterElectricityInfo() {
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        tableName += format.format(date);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time = dateFormat.format(date);
        time += "%";
        return hyBtoDeviceMapper.inverterElectricityInfo(tableName, time);
    }

    @Override
    public float getCurrentPowerBySn(String sn) {
        QueryWrapper<BtoDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("datalogger_sn", sn);
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableName = "bto_device_" + dateFormat.format(date);
        return baseMapper.getCurrentPowerBySn(tableName, sn);
    }

    @Override
    public DateElectricityVo getDayMonthYearAllInfo(String dataloggerSn) {
        QueryWrapper<BtoDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("today_energy", "month_energy", "year_energy", "total_energy");
        queryWrapper.eq("datalogger_sn", dataloggerSn);
        queryWrapper.orderByDesc("update_time");

        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        tableName += dateFormat.format(date);

        BtoDevice btoDevice = baseMapper.getDayMonthYearAllInfo(tableName, dataloggerSn);

        DateElectricityVo dateElectricityVo = new DateElectricityVo();
        dateElectricityVo.setMonthEnergy(btoDevice.getMonthEnergy());
        dateElectricityVo.setYearEnergy(btoDevice.getYearEnergy());
        dateElectricityVo.setTodayEnergy(btoDevice.getTodayEnergy());
        dateElectricityVo.setTotalEnergy(btoDevice.getTotalEnergy());
        return dateElectricityVo;
    }


    @Override
    public HashMap<String, Object> getUserComplexInfo(String plantId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        List<BtoDeviceList> userInverterNum = hyBtoDeviceListService.getUserInverterNum(plantId);
        int normalStatus = 0;
        int alertNum = 0;
        float currentPower = 0;
        double allElectrical = 0;
        ArrayList<String> sns = new ArrayList<>();
        for (BtoDeviceList btoDeviceList : userInverterNum) {
            if ("1".equals(btoDeviceList.getDeviceStatus())) {
                normalStatus++;
            }
            if ("2".equals(btoDeviceList.getDeviceStatus())) {
                alertNum++;
            }
            sns.add(btoDeviceList.getDataloggerSn());
        }
//        if(userInverterNum.equals(0)){
//            System.out.println("错误信息===");
//            return hashMap;
//        }
        //逆变器正常率
        hashMap.put("normalRate", (normalStatus / userInverterNum.size()) * 100);
        //逆变器数量
        hashMap.put("inverterNum", userInverterNum.size());
        //告警数量
        hashMap.put("alertNum", alertNum);
        //装机容量
        hashMap.put("installedCapacity", hyBtoStationListService.getInstalledCapacityById(plantId));
        //当前功率、总发电量
        for (String sn : sns) {
            currentPower += getCurrentPowerBySn(sn);
            allElectrical += btoDeviceService.getDayMonthYearAllInfo(sn).getTotalEnergy();
        }
        hashMap.put("currentPower", currentPower);
        hashMap.put("allElectrical", allElectrical);
        hashMap.put("sns", sns);
        //经纬度
        BtoStationBase latitudeAndLongitude = hyBtoStationBaseService.getLatitudeAndLongitude(plantId);
        hashMap.put("longitude", latitudeAndLongitude.getLongitude());
        hashMap.put("latitude", latitudeAndLongitude.getLatitude());
        //电站uid
        hashMap.put("plantUid", hyBtoStationListService.getIdByUId(plantId));
        return hashMap;
    }





    @Override
    public xuDto powerStationDay() {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String time= simpleDateFormat.format(date).substring(0,13);
        return  hyBtoDeviceMapper.powerStationDay(time);
    }

    @Override
    public int updatePassword(String userUid, String password) {
        if (hyBtoDeviceMapper.updatePassword(userUid,password)==1){
            return 1;
        }else {
            return 0;
        }
    }


    @Override
    public Object returnPrimaryId(String plantId, String plantUid) {
        if (plantId!=null && plantUid!=null){
            plantUid=null;
        }
        return hyBtoDeviceMapper.returnPrimaryId(plantId,plantUid); //"69794294103196580"
    }

}
