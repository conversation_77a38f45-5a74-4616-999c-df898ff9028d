package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.EfficiencyDTO;
import utils.AjaxResult;

public interface EfficiencyService extends IService<EfficiencyDTO> {
    /**
     *     求出月发电量-发电效率-环比-同比数据
     */
    AjaxResult PowerGenerationMonth(String plantId, String month, Integer page, Integer pageSize);
    /**
     *     求出年发电量-发电效率-环比-同比数据
     */
    AjaxResult PowerGenerationYear(String plantId,String year,Integer page,Integer pageSize);

}