package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.VStationListMapper;
import com.botong.services.service.VStationListService;
import entity.VStationList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.Date;
import java.util.List;

@Service
public class VStationListServiceImpl extends ServiceImpl<VStationListMapper, VStationList> implements VStationListService {

    @Autowired
    private VStationListMapper vStationListMapper;

    @Override
    public List<VStationList> VStationListSelecEta(String plantId){
        String format = DateUtil.format(new Date(), "yyyy-MM");
        String month = format + "%";
        List<VStationList> list = vStationListMapper.VStationListSelecEta(plantId , month);
        return list;
    }


    @Override
    public List<VStationList> VStationListSelecEtaMonth(String plantId , String month1){
        String month = month1 + "%";
        List<VStationList> list = vStationListMapper.VStationListSelecEta(plantId , month);
        return list;
    }


    @Override
    public List<VStationList> VStationListSelecEtaSum(String plantId){
        String format = DateUtil.format(new Date(), "yyyy");
        String year = format +"%";
        List<VStationList> list = vStationListMapper.VStationListSelecAll(plantId,year);
        return list;
    }

    @Override
    public List<VStationList> VStationListSelecEtaSumYear(String plantId , String year1){

        String year = year1 +"%";
        List<VStationList> list = vStationListMapper.VStationListSelecAll(plantId,year);
        return list;
    }

}
