package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.VEventMean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface VEventMeanMapper extends BaseMapper<VEventMean> {

    int selectCountDay(@Param("time") String time);  //告警总数

    int selectCountSun(@Param("time") String time); //告警数电站数

    int selectCountInHand(@Param("time") String time);  //处理中

    int selectCountHand(@Param("time") String time);  //未处理

    int updateHostNoGrid(@Param("time") String time,@Param("time2") String time2,@Param("time3")String time3);

}
