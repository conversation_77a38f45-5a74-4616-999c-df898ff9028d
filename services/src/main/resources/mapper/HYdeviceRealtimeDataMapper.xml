<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.HYdeviceRealtimeDataMapper">

    <resultMap id="BtoDeviceRealtimeDate" type="entityDTO.BtoDeviceRealTimeDTO">
        <result property="sn" column="sn"/>
        <result property="time" column="time"/>
        <result property="ppv1" column="pv1"/>
        <result property="ppv2" column="pv2"/>
        <result property="ppv3" column="pv3"/>
        <result property="ppv4" column="pv4"/>
        <result property="ppv5" column="pv5"/>
        <result property="ppv6" column="pv6"/>
        <result property="ppv7" column="pv7"/>
        <result property="ppv8" column="pv8"/>
        <result property="ppv9" column="pv9"/>
        <result property="ipv1" column="ipv1"/>
        <result property="ipv2" column="ipv2"/>
        <result property="ipv3" column="ipv3"/>
        <result property="ipv4" column="ipv4"/>
        <result property="ipv5" column="ipv5"/>
        <result property="ipv6" column="ipv6"/>
        <result property="ipv7" column="ipv7"/>
        <result property="ipv8" column="ipv8"/>
        <result property="ipv9" column="ipv9"/>
        <result property="vpv1" column="vpv1"/>
        <result property="vpv2" column="vpv2"/>
        <result property="vpv3" column="vpv3"/>
        <result property="vpv4" column="vpv4"/>
        <result property="vpv5" column="vpv5"/>
        <result property="vpv6" column="vpv6"/>
        <result property="vpv7" column="vpv7"/>
        <result property="vpv8" column="vpv8"/>
        <result property="vpv9" column="vpv9"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="iac4" column="iac4"/>
        <result property="iac5" column="iac5"/>
        <result property="iac6" column="iac6"/>
        <result property="iac7" column="iac7"/>
        <result property="iac8" column="iac8"/>
        <result property="iac9" column="iac9"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="vac4" column="vac4"/>
        <result property="vac5" column="vac5"/>
        <result property="vac6" column="vac6"/>
        <result property="vac7" column="vac7"/>
        <result property="vac8" column="vac8"/>
        <result property="vac9" column="vac9"/>
        <result property="facc1" column="fac"/>
        <result property="facc2" column="fac1"/>
        <result property="facc3" column="fac2"/>
        <result property="facc4" column="fac3"/>
        <result property="facc5" column="fac4"/>
        <result property="facc6" column="fac5"/>
        <result property="facc7" column="fac6"/>
        <result property="facc8" column="fac7"/>
        <result property="facc9" column="fac8"/>
        <result property="power" column="power"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="totalEnergy" column="totalEnergy"/>
        <result property="updateTime" column="updateTime"/>
    </resultMap>

    <select id="selectdeviceRealtimeData" resultMap="BtoDeviceRealtimeDate">
        SELECT
            a.sn,
            time,
            ipv1,
            ipv2,
            ipv3,
            ipv4,
            ipv5,
            ipv6,
            ipv7,
            ipv8,
            ipv9,
            vpv1,
            vpv2,
            vpv3,
            vpv4,
            vpv5,
            vpv6,
            vpv7,
            vpv8,
            vpv9,
            iac1,
            iac2,
            iac3,
            iac4,
            iac5,
            iac6,
            iac7,
            iac8,
            iac9,
            vac1,
            vac2,
            vac3,
            vac4,
            vac5,
            vac6,
            vac7,
            vac8,
            vac9,
            fac,
            fac1,
            fac2,
            fac3,
            fac4,
            fac5,
            fac6,
            fac7,
            fac8,
            power,
            concat( pv1_dc1, ' - ', pv1_dc2, ' - ', pv1_dc3, ' - ', pv1_dc4 ) pv1,
            concat( pv2_dc1, ' - ', pv2_dc2, ' - ', pv2_dc3, ' - ', pv2_dc4 ) pv2,
            concat( pv3_dc1, ' - ', pv3_dc2, ' - ', pv3_dc3, ' - ', pv3_dc4 ) pv3,
            concat( pv4_dc1, ' - ', pv4_dc2, ' - ', pv4_dc3, ' - ', pv4_dc4 ) pv4,
            concat( pv5_dc1, ' - ', pv5_dc2, ' - ', pv5_dc3, ' - ', pv5_dc4 ) pv5,
            concat( pv6_dc1, ' - ', pv6_dc2, ' - ', pv6_dc3, ' - ', pv6_dc4 ) pv6,
            concat( pv7_dc1, ' - ', pv7_dc2, ' - ', pv7_dc3, ' - ', pv7_dc4 ) pv7,
            concat( pv8_dc1, ' - ', pv8_dc2, ' - ', pv8_dc3, ' - ', pv8_dc4 ) pv8,
            concat( pv9_dc1, ' - ', pv9_dc2, ' - ', pv9_dc3, ' - ', pv9_dc4 ) pv9,
            today_energy todayEnergy,
            total_energy totalEnergy,
            a.update_time updateTime
        FROM
            bto_device_${time} a
                LEFT JOIN bto_device_modules b ON a.sn = b.sn
        WHERE
            a.sn = #{sn}
        ORDER BY
            time DESC
    </select>
</mapper>