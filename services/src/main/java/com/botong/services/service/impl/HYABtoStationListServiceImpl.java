package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYABtoStationListMapper;
import com.botong.services.service.HYABtoStationListService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoStationList;
import entityDTO.AllStationList;
import entityDTO.BTOstationListDTO;
import entityDTO.StationLunBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.StationListVo;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class HYABtoStationListServiceImpl extends ServiceImpl<HYABtoStationListMapper, BtoStationList> implements HYABtoStationListService {

    @Autowired
    HYABtoStationListMapper hyaBtoStationListMapper;


    @Override
    public List<BtoStationList> ceshi() {
        return baseMapper.selectList(null);
    }

    @Override
    public Long StaionAllNum() {
        return baseMapper.selectCount(null);
    }

    @Override
    public Long StationStatus0() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "0");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Long StationStatus1() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "1");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<StationLunBo> StationStatus2() {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        String tableName= simpleDateFormat.format(date)+"%";
        return hyaBtoStationListMapper.StationStatus2(tableName);
    }

    //在线电站
    @Override
    public Object Staionuptime() {
        Long StationStatus1 = StationStatus1();
        Long online = StaionAllNum() - StationStatus0();
        double Staionuptime = online / StationStatus1;
        return Staionuptime;
    }

    @Override
    public List<BtoStationList> sqlStaion() {
        return hyaBtoStationListMapper.sqlStaion();
    }

    @Override
    public BtoStationList StationAllpeakPower() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("SUM(peak_power) as peakPower ");
        return baseMapper.selectOne(queryWrapper);
        //return btoStationListMapper.StationAllpeakPower();
    }

    @Override
    public List<BTOstationListDTO> StationAllenergy(String tableNme, String nowtime10, String nowtime20) {
        return hyaBtoStationListMapper.StationAllenergy(tableNme, nowtime10, nowtime20);
    }


    @Override
    public List<BtoStationList> GFmonitoringCenter() {
        return baseMapper.selectList(null);
    }

    @Override
    public BtoStationList getInstalledCapacity(String plantId) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<StationListVo> getAllStationName() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("plant_id", "name");
        ArrayList<StationListVo> stationListVos = new ArrayList<>();
        List<BtoStationList> btoStationLists = baseMapper.selectList(queryWrapper);
        for (BtoStationList btoStationList : btoStationLists) {
            StationListVo stationListVo = new StationListVo();
            stationListVo.setName(btoStationList.getName());
            stationListVo.setPlantId(btoStationList.getPlantId());
            stationListVos.add(stationListVo);
        }
        return stationListVos;
    }

    @Override
    public String getInstalledCapacityById(String plantId) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("peak_power");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper).getPeakPower();
    }

    @Override
    public List<StationLunBo> Stationonline() {
        Date date=new Date();
        SimpleDateFormat time=new SimpleDateFormat("yyyyMMdd");
        String tableNme2=time.format(date);
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        String tableName= simpleDateFormat.format(date)+"%";
        return hyaBtoStationListMapper.Stationonline(tableName,tableNme2);
    }

    @Override
    public String getIdByUId(String plantId) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("plant_uid");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper).getPlantUid();
    }

    @Override
    public PageInfo<AllStationList> ListOfTotalPowerStations(String page, String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String time=simpleDateFormat.format(date);
        String time2=simpleDateFormat2.format(date)+"%";
        List<AllStationList> stationLunBos = hyaBtoStationListMapper.selectListt(page,pageSize,time,time2);
        PageInfo<AllStationList> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return  stationLunBoPageInfo;
    }

    @Override
    public PageInfo<AllStationList> ListOfTotalPowerStationsstationName(String page, String pageSize, String stationName) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String time=simpleDateFormat.format(date);
        String time2=simpleDateFormat2.format(date)+"%";
        List<AllStationList> stationLunBos = hyaBtoStationListMapper.ListOfTotalPowerStationsstationName(time,time2,stationName);
        PageInfo<AllStationList> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return  stationLunBoPageInfo;
    }

    @Override
    public Object ListOfTotalPowerStationsPhone(String plantUid) {
        return hyaBtoStationListMapper.ListOfTotalPowerStationsPhone(plantUid);
    }

    @Override
    public String getPhoneNumByUid(String userUid) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("owner_phone");
        queryWrapper.eq("plant_id", userUid);
        return baseMapper.selectOne(queryWrapper).getOwnerPhone();
    }
}

