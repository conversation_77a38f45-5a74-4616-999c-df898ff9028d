package com.botong.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class CustomMetaObjectHandler implements MetaObjectHandler {

    //使用mp实现添加的时候，这个方法执行
    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("createDate", new Date(), metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }

    //使用mp实现修改的时候，使用这个方法执行。
    @Override
    public void updateFill(MetaObject metaObject) {
        //修改的时候只需要改变修改时间。
        this.setFieldValByName("updateTime", new Date(), metaObject);

    }

}