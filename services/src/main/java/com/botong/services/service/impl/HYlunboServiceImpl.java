package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYlunboMapper;
import com.botong.services.service.HYlunboService;
import com.github.pagehelper.PageInfo;
import entity.Carousel;
import entityDTO.StationLunBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import utils.ListPageUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class HYlunboServiceImpl extends ServiceImpl<HYlunboMapper, StationLunBo> implements HYlunboService {

    @Autowired
    HYlunboMapper hylunboMapper;

    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'stationCarousel'+'page_'+#p0+'pageSize_'+#p1 ", cacheManager = "HY_Web_Cache")
    public PageInfo<Carousel> stationCarousel(Integer page, Integer pageSize) {
        List<Carousel> stationCarousel = hylunboMapper.stationCarousel();
        int total = stationCarousel.size();
        stationCarousel = new ArrayList<>(ListPageUtils.pageBySubCarouselList(stationCarousel, page, pageSize));
        PageInfo<Carousel> carouselPageInfo = new PageInfo<>(stationCarousel);
        carouselPageInfo.setTotal(total);
        return carouselPageInfo;
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB3M", key = " 'sevenAndSixEnergy' ", cacheManager = "HY_Web_Cache_3M")
    public HashMap<String, Object> sevenAndSixEnergy() {
        HashMap<String, Object> sevenAndSixEnergyMap = new HashMap<>();
        List<HashMap<String, String>> sevenDayEnergy = hylunboMapper.powerSevenEnergy();
        List<HashMap<String, String>> sixMonthEnergy = hylunboMapper.powerSIXEnergy();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        final SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
        //正序
        sevenDayEnergy.sort(Comparator.comparing((Map<String, String> date) -> (dateFormat.format( date.get("date")))));
//        sixMonthEnergy.sort();
        Collections.sort(sixMonthEnergy, new Comparator<HashMap<String, String>>() {
            @Override
            public int compare(HashMap<String, String> o1, HashMap<String, String> o2) {
                int flag = 1;
                try {
                    Date month1 = monthFormat.parse(o1.get("month"));
                    Date month2 = monthFormat.parse(o2.get("month"));
                    if(month1.getTime() < month2.getTime()){
                        flag =  -1;//调整顺序,-1为不需要调整顺序;
                    }
                    if(month1.equals(month2)){
                        flag =  0;
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                return flag;
            }
        });
        sevenAndSixEnergyMap.put("sevenDayEnergy", sevenDayEnergy);
        sevenAndSixEnergyMap.put("sixMonthEnergy", sixMonthEnergy);
        return sevenAndSixEnergyMap;
    }

//    @Override
//    @Cacheable(cacheNames = "HY_WEB",key = " 'powerSevenEnergy' ",cacheManager = "HY_Web_Cache")
//    public List<HashMap<String,String>> powerSevenEnergy() {
//        Date date=new Date();
//        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM");
//        String tiem=simpleDateFormat.format(date)+"%";
//        return hylunboMapper.powerSevenEnergy(tiem);
//    }
//    @Override
//    @Cacheable(cacheNames = "HY_WEB",key = " 'powerSIXEnergy' ",cacheManager = "HY_Web_Cache")
//    public List<HashMap<String,String>> powerSIXEnergy() {
//        return hylunboMapper.powerSIXEnergy();
//    }

}
