package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoDeviceMapDTOMapper;
import com.botong.services.mapper.HYBtoDeviceMapDTOMapper;
import com.botong.services.service.BtoDeviceMapDTOService;
import com.botong.services.service.HYBtoDeviceMapDTOService;
import entityDTO.BtoDeviceMapDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class HYBtoDeviceMapDTOServiceImpl extends ServiceImpl<HYBtoDeviceMapDTOMapper, BtoDeviceMapDTO> implements HYBtoDeviceMapDTOService {

    @Autowired
    private HYBtoDeviceMapDTOMapper hybtoDeviceMapDTOMapper;

    @Override
    public List<BtoDeviceMapDTO> selectBtoDeviceMap(String plantId) {
        List<BtoDeviceMapDTO> btoDeviceList = hybtoDeviceMapDTOMapper.selectDevice(plantId);
        return btoDeviceList;
    }

    @Override
    public List<BtoDeviceMapDTO> selectBtoDeviceMapDay(String plantId, String sn, String date) {
        String tableName ="";
        String[] split = date.split("-");
        for (String s : split) {
            tableName+=s;
        }
        date =date.concat("%");
        List<BtoDeviceMapDTO> list = hybtoDeviceMapDTOMapper.selectBtoDeviceMap2(date, tableName, plantId, sn);
        return list;
    }


    @Override
    public List<BtoDeviceMapDTO> selectBtoDeviceMapDay1(String plantId, String sn, String day) {
        String[] split = day.split("-");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            sb.append(split[i]);
        }
        String time = sb.toString();     //yyyyMMdd
        String table = time;
        List<BtoDeviceMapDTO> list = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            if (i < 10) {
                String b = day + " 0" + i + ":%";
                BtoDeviceMapDTO mapDTO = hybtoDeviceMapDTOMapper.selectBtoDeviceMap(table, b, plantId, sn);
                if (mapDTO == null) {
                    continue;
                } else {
                    list.add(mapDTO);
                }
            } else {
                String b = day + " " + i + ":%";
                BtoDeviceMapDTO mapDTO = hybtoDeviceMapDTOMapper.selectBtoDeviceMap(table, b, plantId, sn);
                if (mapDTO == null) {
                    continue;
                } else {
                    list.add(mapDTO);
                }
            }
        }
        return list;
    }

}




