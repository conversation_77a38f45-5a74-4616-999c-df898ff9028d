package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.VBaseDeviceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface VBaseDeviceDTOMapper extends BaseMapper<VBaseDeviceDTO> {
    List<VBaseDeviceDTO> selectAll(@Param("time") String time,@Param("state") String state,@Param("city") String city,@Param("address") String address,@Param("table") String table,@Param("sn") String sn);

    String[] selectAllSn(@Param("table") String table);

    String[] selectSn(@Param("state") String state,@Param("city") String city,@Param("address") String address,@Param("table") String table);

    String[] selectMonthSn(@Param("state") String state,@Param("city") String city,@Param("address") String address);

    List<VBaseDeviceDTO> selectMonth(@Param("month") String month,@Param("state") String state,@Param("city") String city,@Param("address") String address,@Param("sn") String sn);

    List<VBaseDeviceDTO> selectYear(@Param("year") String year,@Param("state") String state,@Param("city") String city,@Param("address") String address,@Param("sn") String sn);

}
