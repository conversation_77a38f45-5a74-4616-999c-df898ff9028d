package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.HyStationInfo;
import entity.OperatorDetails;
import entityDTO.EfficiencyDTO;
import entityDTO.StationInfoConditionDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface HYEfficiencyMapper extends BaseMapper<EfficiencyDTO> {

    List<EfficiencyDTO> PowerGenerationMonth(@Param("plantId") String plantId, @Param("month") String month );

    List<EfficiencyDTO> PowerGenerationYear(@Param("plantId") String plantId, @Param("year") String year );

    List<HyStationInfo> getStationInfoByCondition(StationInfoConditionDTO stationInfoConditionDTO);

    List<OperatorDetails> operatorDetails(@Param("userName") String userName,@Param("plantId") String plantId);

}