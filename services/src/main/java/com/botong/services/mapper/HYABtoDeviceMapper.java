package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.services.vo.ChartVo;
import com.botong.services.vo.DayPowerVo;
import com.botong.services.vo.YearChartVo;
import entity.BtoDevice;
import entityDTO.StationLunBo;
import entityDTO.WisdomDeviceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.*;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface HYABtoDeviceMapper extends BaseMapper<BtoDevice> {


    List<ElectricityStatisticsVo> inverterElectricityInfo(@Param("tableName") String tableName, @Param("time") String time);

    List<String> getUserByInverterMoreOne();

    List<String> getUserByInverterEqualOne();

    float getCurrentPowerBySn(@Param("tableName") String tableName, @Param("sn") String sn);

    BtoDevice getDayMonthYearAllInfo(@Param("tableName") String tableName, @Param("dataloggerSn") String dataloggerSn);

    List<DayPowerVo> getDayPowerBySn(@Param("sn") String sn, @Param("tableName1") String tableName1, @Param("time") String time);

    List<ChartVo> getMonthElectricitySn(@Param("plantId") String plantId, @Param("time")
    String time);

    List<YearChartVo> getYearElectricitySn(@Param("plantId") String plantId, @Param("time") String time);

    List<ChartVo> getAllElectricitySn(@Param("sn") String sn, @Param("time") String time, @Param("tableName") String tableName);
    List<ChartVo> getAllElectricity(@Param("plantId") String plantId, @Param("tableName") String tableName);

    List<BtoDevice> getElectricityBySn(@Param("sn") String sn, @Param("tableName") String tableName, @Param("time") String time);

    BtoDevice getInverterDetails(@Param("sn") String sn, @Param("tableName") String tableName, @Param("time") String time);

    DeviceListVo selectDeviceList(@Param("plantId")String plantId);

    List<DayPowerVo> getNewDayPowerBySn(@Param("sn") String sn, @Param("tableName") String tableName, @Param("time") String time);

    List<MonthElectricityVo> getNewMonthElectricitySn(@Param("stationId")String stationId, @Param("time")String time);

    StationDetailsHomeVo getStationDetailsHome(@Param("stationId")String stationId,@Param("tableName")String tableName);

    BtoDevice getDeviceDetails(@Param("sn") String sn,@Param("tableName") String tableName);

    VersionInfoVo getDeviceVersionInfo(String sn);

    /**
     * 查询该电站的逆变器告警信息
     * @param stationId
     * @return
     */
    List<DeviceAlertVo> getDeviceAlertInfo(@Param("stationId") String stationId);

    /**
     * 查询该电站的运维器告警信息
     * @param plantUid
     * @return
     */
    List<OperatorInfoVO> getWisdomALarmInfo(@Param("plantUid") String plantUid);


    List<String> selectDeviceByStation(String stationId);

    BtoDevice getElectricityBySnToOne(@Param("sn") String sn,@Param("tableName") String tableName, @Param("time")String time);

     Double getEnergy(@Param("time") String time, @Param("plantId") String plantId,@Param("timeType") String timeType);

     Double getTotalEnergy(@Param("plantId") String plantId);

    /**
     * 逆变器图表信息-日
     * @return
     */
    List<DayPowerVo> getDeviceChart(@Param("tableName") String tableName,@Param("SNs") String[] SNs,@Param("time") String time);

    List<String> getDateTimeList(@Param("tableName") String tableName,@Param("plantId") String plantId,@Param("dateTime") String dateTime);

    /**
     * 查询当天单个逆变器所有时间段  功率之和
     * @param plantId
     * @return
     */
    List<String> selectTotalPower(@Param("tableName") String tableName,@Param("plantId") String plantId);

    /**
     * 根据逆变器SN查询逆变器当前状态
     * @param deviceSn
     * @return
     */
    String selectStatusByDeviceSN(@Param("deviceSn") String deviceSn);
}