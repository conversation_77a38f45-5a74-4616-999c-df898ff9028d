package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.services.vo.ChartVo;
import com.botong.services.vo.DayPowerVo;
import entity.BtoDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.ElectricityStatisticsVo;
import vo.InverterVo;

import java.util.List;

/**
 * 逆变器数据
 *
 * <AUTHOR> @date 2022-08-16 10:10:10
 */
@Mapper
public interface BtoDeviceMapper extends BaseMapper<BtoDevice> {

    List<ElectricityStatisticsVo> inverterElectricityInfo(@Param("tableName") String tableName, @Param("time") String time);

    List<String> getUserByInverterMoreOne();

    List<String> getUserByInverterEqualOne();

    float getCurrentPowerBySn(@Param("tableName") String tableName, @Param("sn") String sn);

    BtoDevice getDayMonthYearAllInfo(@Param("tableName") String tableName, @Param("dataloggerSn") String dataloggerSn);

    List<DayPowerVo> getDayPowerBySn(@Param("sn") String sn, @Param("tableName1") String tableName1, @Param("time") String time);

    List<ChartVo> getMonthElectricitySn(@Param("plantId") String plantId, @Param("time")
    String time);

    List<ChartVo> getYearElectricitySn(@Param("plantId") String plantId, @Param("time") String time);

    List<ChartVo> getAllElectricitySn(@Param("sn") String sn, @Param("time") String time, @Param("tableName") String tableName);

    List<BtoDevice> getElectricityBySn(@Param("sn") String sn, @Param("tableName") String tableName, @Param("time") String time);

    BtoDevice getInverterDetails(@Param("sn") String sn, @Param("tableName") String tableName, @Param("time") String time);

    int registeredUser(@Param("uuid") String uuid, @Param("cUserName") String cUserName,@Param("cUsertel")  String cUsertel, @Param("cUserEmail") String cUserEmail,@Param("regtiTime")  String regtiTime,@Param("engnierId") String engnierId,@Param("special")Integer special,@Param("password")String password);
}
