<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYAuserListLoginMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.userListLogin" id="userListLoginMap">
        <result property="delFlag" column="delFlag"/>
        <result property="updateDate" column="updateDate"/>
        <result property="updateTime" column="updateTime"/>
        <result property="createTime" column="createTime"/>
        <result property="cUserId" column="cUserId"/>
        <result property="cUserUid" column="cUserUid"/>
        <result property="cUserName" column="cUserName"/>
        <result property="cUserTel" column="cUserTel"/>
        <result property="delFlag" column="delFlag"/>
        <result property="cUserRegtime" column="cUserRegtime"/>
        <result property="cUserEmail" column="cUserEmail"/>
        <result property="createTime" column="createTime"/>
        <result property="password" column="password"/>
        <result property="name" column="name"/>
        <result property="peakPower" column="peakPower"/>
        <result property="stationNum" column="stationNum"/>
        <result property="todayenergy" column="todayenergy"/>
        <result property="monthenergy" column="monthenergy"/>
        <result property="yearenergy" column="yearenergy"/>
        <result property="totalenergy" column="totalenergy"/>
        <result property="alertNum" column="alertNum"/>
        <result property="alarmCode" column="alarmCode"/>
        <result property="alarmMessage" column="alarmMessage"/>
        <result property="endTime" column="endTime"/>
        <result property="grade" column="grade"/>
        <result property="mean" column="mean"/>
        <result property="startTime" column="startTime"/>
        <result property="plantId" column="plantId"/>
        <result property="plantIdNum" column="plantIdNum"/>
        <result property="plantUid" column="plantUid"/>
        <result property="address1" column="address1"/>
        <result property="createDate" column="createDate"/>
        <result property="sumpower" column="sumpower"/>
        <result property="sn" column="sn"/>
        <result property="jishu" column="jishu"/>
        <result property="status" column="status"/>
        <result property="wisdomdevicesn" column="wisdomdevicesn"/>
        <result property="t" column="t"/>
        <result property="pvele" column="pvele"/>
        <result property="useele" column="useele"/>
        <result property="selfele" column="selfele"/>
        <result property="sellele" column="sellele"/>
        <result property="buyele" column="buyele"/>
        <result property="time" column="time"/>
    </resultMap>
    <insert id="registeredUser">
        insert into bto_user_list (c_user_uid,c_user_name,c_user_tel,del_flag,c_user_regtime,update_date,c_user_email)
        VALUES (#{uuid},#{cUserName},#{cUsertel},#{delFlag},#{nb},#{updateTime},#{cUserEmail})
    </insert>

    <update id="updatePassword">
        UPDATE bto_user_list
        SET password=#{password}
        WHERE c_user_uid=#{userUid};
    </update>
    <select id="login" resultMap="userListLoginMap">
        SELECT * FROM bto_user_list where c_user_name=#{username} and password=#{password}
    </select>
    <select id="frontPage" resultMap="userListLoginMap">
        SELECT
            name name,
            sum(peak_power) peakPower,
            count( a.plant_id ) stationNum,
            user_uid cUserUid,
            sum(today_energy) todayenergy,
            sum(month_energy) monthenergy,
            sum(year_energy) yearenergy,
            sum( total_energy ) totalenergy
        FROM
            v_station_base a
                LEFT JOIN (
                SELECT
                    plant_id,
                    count(DISTINCT(sn)) 逆变器数,
                    sn,
                    power,
                    today_energy,
                    month_energy,
                    year_energy,
                    total_energy,
                    time
                FROM
                    v_device_list aa
                    LEFT	JOIN (
                    SELECT sn,power,today_energy,month_energy,year_energy,total_energy,time
                    FROM ${time}
                    WHERE
                    time LIKE #{time2}
                    GROUP BY SN,time
                    ORDER BY time desc

                    ) bb
                ON aa.device_sn = bb.sn
                GROUP BY
                    sn
            ) b ON a.plant_id = b.plant_id
        where user_uid=#{userUid}
    </select>
    <select id="TotalNumberOfAlarms" resultType="java.lang.Integer">
        SELECT
            count(*) alertNum
        FROM
            v_event_r
        WHERE
            sn IN ( SELECT sn FROM v1_station_device WHERE user_uid = #{userUid} )
          AND start_time LIKE #{time2}
    </select>
    <select id="TotalNumberOfAlarmsDetails" resultType="entityDTO.userListLogin">
        SELECT
            sn sn,
            alarm_code alarmCode,
            alarm_message alarmMessage,
            start_time startTime,
            end_time endTime,
            status status,
            update_time updateTime,
            create_time createTime,
            grade grade,
            mean mean
        FROM
            v_event_r
        WHERE
            sn IN ( SELECT sn FROM v1_station_device WHERE user_uid = #{userUid} )
          AND start_time LIKE  #{time2}
    </select>
    <select id="AlarmNumberClassification" resultType="entityDTO.userListLogin">
        SELECT
            status,
            CASE

                WHEN status = 0 THEN
                    count( sn )
                WHEN status = 1 THEN
                    count( sn )
                END jishu
        FROM
            v_event_r
        WHERE
            sn IN ( SELECT sn FROM v1_station_device WHERE user_uid = #{userUid} )
          AND start_time LIKE #{time2}

        GROUP BY
            status
    </select>
    <select id="OfflineNumberClassification" resultType="entityDTO.userListLogin">
        SELECT
            count(DISTINCT(plant_id )) plantIdNum
        FROM
            v1_station_device a
                LEFT JOIN bto_device_${time} b ON a.sn = b.sn
        WHERE
            user_uid = #{userUid}
          AND b.sn IS NULL
    </select>
    <select id="frontPageplantid" resultType="entityDTO.userListLogin">
        SELECT
            plant_id plantId,
            user_uid plantUid,
            peak_power peakPower
        FROM
            v1_station_device
        WHERE
            user_uid = #{userUid}
        GROUP BY
            plant_id
    </select>
    <select id="PowerStationList" resultType="entityDTO.userListLogin">
        SELECT
            `name` name,
            address1 address1,
            create_date createDate,
            sum(distinct(power)) sumpower,
            sum(distinct(today_energy)) todayenergy,
            sum( distinct(total_energy) ) totalenergy,
            a.plant_id plantId
        FROM
            v1_station_device a
                LEFT JOIN (
                SELECT
                    plant_id,
                    power,
                    today_energy,
                    total_energy,
                    time
                FROM
                    v_device_list aa
                    LEFT JOIN (
                    SELECT sn,power,today_energy,total_energy,time
                    FROM bto_device_${time}
                    WHERE
                    time LIKE '${time2}'
                    GROUP BY SN,time
                    ORDER BY time desc
                    ) bb
                ON aa.device_sn = bb.sn
                WHERE sn is not null
                GROUP BY sn
            ) b ON a.plant_id = b.plant_id  where a.special='1'
        group by a.plant_id
            LIMIT #{a},#{b}
    </select>
    <select id="ifSmartOM" resultType="entityDTO.userListLogin">
        select plant_id plantId,plant_uid plantUid from v_wisdom_sn where
        <foreach collection="plantId" item="plantId" separator="or">
            plant_id= ${plantId}
        </foreach>
    </select>
    <select id="ifSmartOMYueShang" resultType="entityDTO.userListLogin">
        SELECT
            a.plant_uid plantUid,
            a.wisdom_device_sn wisdom_device_sn,
            sum( pv_ele ) pvele,
            sum( use_ele ) useele,
            sum( self_ele ) selfele,
            sum( sell_ele ) sellele,
            sum( buy_ele ) buyele,
            LEFT ( time, 7 ) t
        FROM
            v_sn_hy a
            LEFT JOIN v_operation b ON a.plant_uid = b.plant_uid
        WHERE
            a.plant_id = #{plantId}
          AND time LIKE CONCAT (#{time},'%')
    </select>
    <select id="ifSmartOMYueXia" resultType="entityDTO.userListLogin">
        SELECT
            b.plant_uid plantUid,
            b.wisdom_device_sn wisdomdevicesn,
            pv_ele pvele,
            use_ele useele,
            self_ele selfele,
            sell_ele sellele,
            buy_ele buyele,
            time time
        FROM
            v_sn_hy a
            LEFT JOIN v_operation b ON a.plant_uid = b.plant_uid
        WHERE
            a.plant_id = #{plantId}
          AND time like CONCAT (#{time},'%')
        ORDER BY
            time
    </select>
    <select id="ifSmartOMNianShang" resultType="entityDTO.userListLogin">
        SELECT
            a.plant_id plantUid,
            a.wisdom_device_sn wisdomdevicesn,
            sum( pv_ele ) pvele,
            sum( use_ele ) useele,
            sum( self_ele ) selfele,
            sum( sell_ele ) sellele,
            sum( buy_ele ) buyele,
            LEFT ( time, 4 ) t
        FROM
            v_sn_hy a
            LEFT JOIN v_operation b ON a.plant_uid = b.plant_uid
        WHERE
            a.plant_id = #{plantId}
          AND
            time LIKE CONCAT ('%',#{time},'%')
    </select>
    <select id="ifSmartOMNianXia" resultType="entityDTO.userListLogin">
        SELECT
            a.plant_uid plantUid,
            a.wisdom_device_sn wisdomdevicesn,
            sum( pv_ele ) pvele,
            sum( use_ele ) useele,
            sum( self_ele ) selfele,
            sum( sell_ele ) sellele,
            sum( buy_ele ) buyele,
            LEFT ( time, 7 ) t
        FROM
            v_sn_hy a
            LEFT JOIN v_operation b ON a.plant_uid = b.plant_uid
        WHERE
            a.plant_id = #{plantId}
          AND
            time LIKE CONCAT ('%',#{time},'%')
        GROUP BY date
        ORDER BY
            time;
    </select>
    <select id="ifSmartOMALL" resultType="entityDTO.userListLogin">
        SELECT
            plant_uid plantUid,
            wisdom_device_sn wisdomdevicesn,
            sum( pv_ele ) pvele,
            sum( use_ele ) useele,
            sum( self_ele ) selfele,
            sum( sell_ele ) sellele,
            sum( buy_ele ) buyele,
            LEFT ( time, 4 ) t
        FROM
            v_operation
        WHERE
            plant_uid = #{plantUid}
    </select>


    <select id="PowerStationListName" resultType="entityDTO.userListLogin">
        SELECT
            `name` name,
            address1 address1,
            create_date createDate,
            sum(distinct(power)) sumpower,
            sum(distinct(today_energy)) todayenergy,
            sum( distinct(total_energy) ) totalenergy,
            a.plant_id plantId
        FROM
            v1_station_device a
                LEFT JOIN (
                SELECT
                    plant_id,
                    power,
                    today_energy,
                    total_energy,
                    time
                FROM
                    v_device_list aa
                    LEFT JOIN (
                    SELECT sn,power,today_energy,total_energy,time
                    FROM bto_device_${time}
                    WHERE
                    time LIKE '${time2}'
                    GROUP BY SN,time
                    ORDER BY time desc
                    ) bb
                ON aa.device_sn = bb.sn
                WHERE sn is not null
                GROUP BY sn

            ) b ON a.plant_id = b.plant_id  where a.special='1' and a.name like '${name1}'
        group by a.plant_id
            LIMIT #{a},#{b}
    </select>
    <select id="ifSmartOMALLShang" resultType="entityDTO.userListLogin">
        SELECT
            a.plant_id plantUid,
            a.wisdom_device_sn wisdomdevicesn,
            sum( pv_ele ) pvele,
            sum( use_ele ) useele,
            sum( self_ele ) selfele,
            sum( sell_ele ) sellele,
            sum( buy_ele ) buyele
        FROM
            v_sn_hy a
                LEFT JOIN v_operation b ON a.plant_uid = b.plant_uid
        WHERE
            a.plant_id =  #{plantId}
    </select>
    <select id="ifSmartOMALLXia" resultType="entityDTO.userListLogin">
        SELECT
            a.plant_uid plantUid,
            a.wisdom_device_sn wisdomdevicesn,
            sum( pv_ele ) pvele,
            sum( use_ele ) useele,
            sum( self_ele ) selfele,
            sum( sell_ele ) sellele,
            sum( buy_ele ) buyele,
            LEFT ( time, 4 ) t
        FROM
            v_sn_hy a
            LEFT JOIN v_operation b ON a.plant_uid = b.plant_uid
        WHERE
            a.plant_id =  #{plantId}
        GROUP BY t ORDER BY time
    </select>
    <select id="accordingToTiemenergy" resultType="entityDTO.userListLogin">
        SELECT
            `name` name,
            a.plant_id plantId,
            address1 address1,
            create_date creatDate,
            sum(distinct(power)) power,
            sum(distinct(today_energy)) todayenergy,
            sum( distinct(total_energy) ) totalenergy
        FROM
            `v1_station_device` a
                LEFT JOIN (
                SELECT
                    plant_id,
                    power,
                    today_energy,
                    total_energy,
                    time
                FROM
                    v_device_list aa
                    LEFT JOIN (
                    SELECT sn,power,today_energy,total_energy,time
                    FROM bto_device_${time}
                    WHERE
                    time LIKE '${time2}%'
                    GROUP BY SN,time
                    ORDER BY time desc
                    ) bb
                ON aa.device_sn = bb.sn
                WHERE sn is not null
                GROUP BY sn
            ) b ON a.plant_id = b.plant_id  where a.special='1'
        group by a.plant_id
            LIMIT #{a},#{b}
    </select>

</mapper>