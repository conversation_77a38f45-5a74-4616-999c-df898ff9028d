package com.botong.services.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.StationInfoMapper;
import com.botong.services.mapper.userListLoginMapper;
import com.botong.services.service.userlogionService;
import com.github.pagehelper.PageInfo;
import entityDTO.*;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.ChartConditionVO;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

@Service
public class userlogionServiceImpl extends ServiceImpl<userListLoginMapper, userListLogin> implements userlogionService {
    @Autowired
    private userListLoginMapper userListLoginMapper;


    @Override
    public int userlogin(String username, String password) {
        if (userListLoginMapper.login(username, password) == null) {
            return 0;
        } else {
            return 1;
        }
    }

    @Override
    public List<userListLogin> frontPage(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem = "bto_device_" + dateFormat.format(date);
        String tiem2 = dateFormat2.format(date) + "%";
        return userListLoginMapper.frontPage(userUid, tiem, tiem2);
    }

    @Override
    public int TotalNumberOfAlarms(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem2 = dateFormat2.format(date) + "%";
        return userListLoginMapper.TotalNumberOfAlarms(userUid, tiem2);
    }

    @Override
    public List<userListLogin> TotalNumberOfAlarmsDetails(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem2 = dateFormat2.format(date) + "%";
        return userListLoginMapper.TotalNumberOfAlarmsDetails(userUid, tiem2);
    }

    @Override
    public List<userListLogin> AlarmNumberClassification(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem2 = dateFormat2.format(date) + "%";
        return userListLoginMapper.AlarmNumberClassification(userUid, tiem2);
    }

    @Override
    public List<userListLogin> OfflineNumberClassification(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String tiem = dateFormat.format(date);
        return userListLoginMapper.OfflineNumberClassification(userUid, tiem);
    }

    @Override
    public List<userListLogin> frontPageplantid(String userUid) {
        return userListLoginMapper.frontPageplantid(userUid);
    }

    @Override
    public List<userListLogin> PowerStationList(String plantId) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem = dateFormat.format(date);
        String time2 = dateFormat2.format(date) + "%";
        String[] split = plantId.split(",");
        return userListLoginMapper.PowerStationList(tiem, time2, Arrays.asList(split));
    }

    @Override
    public List<userListLogin> ifSmartOM(String plantId) {
        String[] split = plantId.split(",");
        return userListLoginMapper.ifSmartOM(Arrays.asList(split));
    }

    @Override
    public List<userListLogin> ifSmartOMYueShang(String plantUid, String time) {
        return userListLoginMapper.ifSmartOMYueShang(plantUid, time);
    }

    @Override
    public List<userListLogin> ifSmartOMYueXia(String plantUid, String time) {
        return userListLoginMapper.ifSmartOMYueXia(plantUid, time);
    }

    @Override
    public List<userListLogin> ifSmartOMNianShang(String plantUid, String time) {
        return userListLoginMapper.ifSmartOMNianShang(plantUid, time);
    }

    @Override
    public List<userListLogin> ifSmartOMNianXia(String plantUid, String time) {
        return userListLoginMapper.ifSmartOMNianXia(plantUid, time);
    }

    @Override
    public List<userListLogin> ifSmartOMALL(String plantUid) {
        return userListLoginMapper.ifSmartOMALL(plantUid);
    }

    @Override
    public int updatePassword(String userUid, String password) {
        if (userListLoginMapper.updatePassword(userUid, password) == 1) {
            return 1;
        } else {
            return 0;
        }
    }

    public int registeredUser(String cUserName, String cUsertel, String cUserEmail, String updateTime, String delFlag) {
        String uuid = UUID.randomUUID().toString().toUpperCase();
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String nb = dateFormat2.format(date);
        if (userListLoginMapper.registeredUser(uuid, cUserName, cUsertel, nb, cUserEmail, updateTime, delFlag) == 1) {
            return 1;
        } else {
            return 0;
        }
    }

    ///etc/nginx/conf.d/gf/APP/

    @Override
    public Object versionView() {
        File file = new File("/etc/nginx/conf.d/gf/APP/gfUserApp.apk");
        System.getProperty("file.encoding");
        String test = null;
        System.out.println(file.exists());
        System.out.println(file.isFile());
        if (file.exists() && file.isFile()) {
            try {
                ApkFile apkFile = new ApkFile(file);
                ApkMeta apkMeta = apkFile.getApkMeta();
                System.out.println("版本号1：" + test);
                test = apkMeta.getVersionName();
                System.out.println("版本号2：" + test);
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("版本号3：" + test);
            }
        }
        System.out.println("版本号4：" + test);
        return test;
    }
}
