package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entityDTO.BtoDeviceTypeDTO;
import entityDTO.btoProjectSign;
import entityDTO.userEnginerDTO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface userEnginerService extends IService<userEnginerDTO> {
    int registeredUser(String cUserName, String cUsertel, String cUserEmail,String engnierId,Integer special);

    List<userEnginerDTO> selectUid(String cUserName);

    List<btoProjectSign> QueryAreaUsers();

    int bindSmartOperator(String stationName, String smartOperatorName,Integer num);

    int updatePassword(String userUid, String password);

    List<userEnginerDTO> SelectcompanyId();


    List<userEnginerDTO> SelUserAll();

    Integer CheckUsername(String userName);

    Object versionView();

    List<BtoDeviceTypeDTO> InverterModel();

    Object readFileName();

    Integer QueryIMEIcode(String imei);

    List<BtoDeviceTypeDTO> getUseroPhone(String cUserPhone);
}
