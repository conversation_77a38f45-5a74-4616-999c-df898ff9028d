<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.AlarmAnalysisMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.AlarmAnalysis" id="AlarmAnalysisMap">
        <result property="sn" column="sn"/>
        <result property="startTime" column="startTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="alarmMessage" column="alarmMessage"/>
        <result property="mean" column="mean"/>
        <result property="STATUS" column="STATUS"/>
        <result property="plantId" column="plantId"/>
        <result property="plantUid" column="plantUid"/>
        <result property="name1" column="name1"/>
        <result property="NAME" column="NAME"/>
        <result property="tel" column="tel"/>
        <result property="grade" column="grade"/>
        <result property="NUM" column="NUM"/>
        <result property="q" column="q"/>
        <result property="yaoxin" column="yaoxin"/>
        <result property="nb" column="nb"/>
        <result property="name" column="name"/>
        <result property="city" column="city"/>
        <result property="planId" column="planId"/>
        <result property="wisdomdevicesn" column="wisdomdevicesn"/>
        <result property="pvele" column="pvele"/>
        <result property="useele" column="useele"/>
        <result property="selfele" column="selfele"/>
        <result property="sellele" column="sellele"/>
        <result property="buyele" column="buyele"/>
        <result property="time" column="time"/>
        <result property="mingzi" column="mingzi"/>
        <result property="createdate" column="createdate"/>
        <result property="plantId" column="plantId"/>
    </resultMap>

    <resultMap type="entityDTO.OperatorEvents" id="OperatorEventsMap">
        <result property="name" column="name"/>
        <result property="ownerPhone" column="ownerPhone"/>
        <result property="city" column="city"/>
        <result property="plantUid" column="plantUid"/>
        <result property="wisdomDevicesn" column="wisdomDevicesn"/>
        <result property="alarmname" column="alarmname"/>
        <result property="alarmtime" column="alarmtime"/>
        <result property="apointname" column="apointname"/>
        <result property="bpointname" column="nambpointnamee"/>
        <result property="cpointname" column="cpointname"/>
        <result property="apointvoltage" column="apointvoltage"/>
        <result property="bpointvoltage" column="bpointvoltage"/>
        <result property="cpointvoltage" column="cpointvoltage"/>
        <result property="dpointvoltage" column="dpointvoltage"/>
        <result property="plantUid" column="plantUid"/>
    </resultMap>
    <select id="selectListt" resultMap="AlarmAnalysisMap">
        SELECT a.sn          sn,
               start_time    startTime,
               update_time   updateTime,
               alarm_message alarmMessage,
               mean          mean,
               STATUS        STATUS,
               plant_id      plantId,
               plant_uid     plantUid,
               name1         name1,
               NAME          NAME,
               tel           tel,
               grade         grade,
               CASE
                   WHEN STATUS = 0 THEN '未处理'
                   WHEN STATUS = 1 THEN '已处理'
                   WHEN STATUS = 2 THEN '失效'
                   END       state #状态
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '%${time}%'
    </select>
    <select id="AlarmAnalysisTimeIntervalQuery" resultMap="AlarmAnalysisMap">
        SELECT
        a.sn sn,
        start_time startTime,
        update_time updateTime,
        alarm_message alarmMessage,
        mean mean,
        STATUS STATUS,
        plant_id plantId,
        plant_uid plantUid,
        name1 name1,
        NAME NAME,
        tel tel,
        CASE
        WHEN STATUS = 0 THEN'未处理'
        WHEN STATUS = 1 THEN'已处理'
        WHEN STATUS = 2 THEN'失效'
        END state
        FROM
        v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE 1=1
        <if test="startTime!= null and endTime != null and startTime!='' and endTime!='' ">
            AND start_time BETWEEN '${startTime}' AND '${endTime}'
        </if>
        <if test="plantUid != null and plantUid.size>0">
            AND
            <foreach collection="plantUid" item="plantUid" separator="or">
                plant_uid='${plantUid}'
            </foreach>
        </if>
    </select>
    <select id="AlarmAnalysisTconditionQuery" resultMap="AlarmAnalysisMap">
        SELECT a.sn          sn,
               start_time    startTime,
               update_time   updateTime,
               alarm_message alarmMessage,
               mean          mean,
               STATUS        STATUS,
               plant_id      plantId,
               plant_uid     plantUid,
               name1         name1,
               NAME          NAME,
               tel           tel,
               CASE
                   WHEN STATUS = 0 THEN '未处理'
                   WHEN STATUS = 1 THEN '已处理'
                   WHEN STATUS = 2 THEN '失效'
                   END       state #状态
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '%${startTime}%'
           OR name LIKE '#{name}'
           OR a.sn LIKE '#{sn}'
           OR name1 LIKE '#{name1}'
    </select>
    <select id="AlarmAnalysisPolymerization" resultMap="AlarmAnalysisMap">
        SELECT
        plantUid,
        name1,
        sn,
        mean,
        q,
        startTime,
        grade,
        NUM
        FROM(
        SELECT
        plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        substr('${startTime}',1,10) q,
        start_time startTime,
        grade grade,
        count(*) NUM
        FROM
        v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        GROUP BY grade,q,mean,a.sn,name1
        ) w WHERE
        <foreach collection="plantUid" item="plantUid" separator="or">
            plantUid='${plantUid}'
        </foreach>

    </select>
    <select id="AlarmAnalysisPolymerizationAll" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid                     plantUid,
               name1                         name1,
               a.sn                          sn,
               mean                          mean,
               substr('${startTime}', 1, 10) q,
               start_time                    startTime,
               grade                         grade,
               count(*)                      NUM
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time BETWEEN '${endTime}' AND '${startTime}'
        GROUP BY grade, q, mean, a.sn, name1
    </select>
    <select id="AlarmAnalysisHistoryAlarm" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        start_time BETWEEN '${endTime}' AND '${startTime}'
        <if test="grade!= null and grade != '' ">
            and grade=${grade}
        </if>
    </select>
    <select id="AlarmAnalysisListFormplantUid" resultType="entityDTO.AlarmAnalysis">
        SELECT
        a.sn sn,
        start_time startTime,
        update_time updateTime,
        alarm_message alarmMessage,
        mean mean,
        STATUS STATUS,
        plant_id plantId,
        plant_uid plantUid,
        name1 name1,
        NAME NAME,
        tel tel,
        CASE
        WHEN STATUS = 0 THEN'未处理'
        WHEN STATUS = 1 THEN'已处理'
        WHEN STATUS = 2 THEN'失效'
        END state #状态
        FROM
        v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        <if test="plantUid !=null and plantUid.size>0">
            <foreach collection="plantUid" item="plantUid" separator="or">
                plant_uid='${plantUid}'
            </foreach>
        </if>
    </select>
    <select id="AlarmAnalysisHistoryAlarmAll" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        <if test="endTime != null and endTime != '' ">
            start_time BETWEEN '${endTime}' AND '${startTime}'
        </if>
        <if test="plantUid != null and plantUid != '' ">
            AND plantUid ='#{plantUid}'
        </if>
        <if test="grade != null and grade != '' ">
            AND grade=${grade}
        </if>
    </select>
    <select id="AlarmAnalysisTimeIntervalAll" resultMap="AlarmAnalysisMap">
        SELECT a.sn          sn,
               start_time    startTime,
               update_time   updateTime,
               alarm_message alarmMessage,
               mean          mean,
               STATUS        STATUS,
               plant_id      plantId,
               plant_uid     plantUid,
               name1         name1,
               NAME          NAME,
               tel           tel,
               CASE
                   WHEN STATUS = 0 THEN '未处理'
                   WHEN STATUS = 1 THEN '已处理'
                   WHEN STATUS = 2 THEN '失效'
                   END       state #状态
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time BETWEEN '${time1}' AND '${time2}'
    </select>
    <select id="AlarmAnalysisHistoryAlarmAll3" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        name name,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        <if test="(name1 != null and name1 != '') or (grade != null and grade != '') or (startTime != null and startTime != '')">
            where
        </if>
        <if test="startTime != null and startTime != ''">
            start_time between '${startTime}' and '${endTime}'
        </if>
        <if test="grade != null and grade != ''">
            <if test="startTime != null and startTime !=''">and</if>
            grade like #{grade}
        </if>
        <if test="name1 != null and name1 != ''">
            <if test="(startTime != null and startTime !='') or (grade != null and grade !='')">and</if>
            name1 like CONCAT ('%',#{name1},'%')
        </if>

    </select>
    <select id="AlarmAnalysisHistoryAlarmQuann" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid  plantUid,
               name1      name1,
               a.sn       sn,
               mean       mean,
               start_time startTime,
               grade      grade
        FROM (
                 select sn, alarm_code, alarm_message, start_time, `status`, mean, grade
                 from v_event_r
                 union
                 select sn, alarm_code, alarm_message, start_time, `status`, mean, grade
                 from v_event_h) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
    </select>
    <select id="AlarmAnalysisPolymerizationplantUid" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        substr('${startTime}', 1, 10) q,
        start_time startTime,
        grade grade,
        count(*) NUM
        FROM v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time BETWEEN '${startTime}' AND '${endTime}'
        <if test="plantUid!=null and plantUid.size>0 ">
            and
            <foreach collection="plantUid" item="plantUid" separator="or">
                plant_uid = '${plantUid}'
            </foreach>
        </if>
        GROUP BY grade, q, mean, a.sn, name1
    </select>
    <select id="totalAlarmStatistics" resultType="java.lang.Object">
        SELECT count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
    </select>
    <select id="totalAlarmStatisticsTime" resultType="java.lang.Object">
        SELECT count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
    </select>
    <select id="totalAlarmStatisticsTIME" resultType="entityDTO.AlarmAnalysis">
        SELECT count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plantUid}'
    </select>
    <select id="totalAlarmStatisticsTIMETU" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid,
               name1,
               #a.sn,
               mean,
               substr(start_time, 1, 10) q,
               #start_time,
               #grade,
               count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'

    </select>
    <select id="totalAlarmStatisticsTIMEgeren" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid,
               name1,
               #a.sn,
               mean,
               substr(start_time, 1, 10) q,
               #start_time,
               #grade,
               count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'

          AND plant_uid = '${plantUid}'

        GROUP BY q
    </select>
    <select id="totalAlarmStatisticsTIMEgerenyaoxin" resultType="entityDTO.AlarmAnalysis">
        SELECT count(distinct (mean)) yaoxin
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
    </select>
    <select id="totalAlarmStatisticsTIMEgerennyaoxin" resultType="entityDTO.AlarmAnalysis">
        SELECT count(distinct (mean)) yaoxin
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plantUid}'
    </select>
    <select id="totalAlarmStatisticsTIMEstatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(*)                 nb,
               substr(start_time, 1, 7) q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${tiem}%'
        GROUP BY q
    </select>
    <select id="totalAlarmStatisticsTIMEgerennstatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(*)                 nb,
               substr(start_time, 1, 7) q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${tiem}%'
          AND plant_uid = '${plantUid}'
        GROUP BY q
    </select>
    <select id="totalAlarmStatisticsTIMEEquipmentStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(a.sn) q,
               a.sn
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
        GROUP BY a.sn;

    </select>
    <select id="totalAlarmStatisticsTIMEgerenEquipmentStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(a.sn) q,
               a.sn
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'

          AND plant_uid = '${plantUid}'
        GROUP BY a.sn;
    </select>
    <select id="totalAlarmStatisticsTIMELevelStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(grade),
               grade
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
        GROUP BY grade;
    </select>
    <select id="totalAlarmStatisticsTIMEgerennLevelStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(grade),
               grade
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plantUid}'
        GROUP BY grade;
    </select>
    <select id="totalAlarmStatisticsTIMEgerenlist" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid plantUid,
               name1     name1,
               a.sn      sn,
               count(*)  q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plnatUid}'
        GROUP BY a.sn;
    </select>
    <select id="totalAlarmStatisticsTIMEyaoxinlistt" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid plantUid,
               name1     name1,
               a.sn      sn,
               count(*)  q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
        GROUP BY a.sn;
    </select>
    <select id="OperatorEventData" resultMap="OperatorEventsMap">
        SELECT
        b.name name,
        b.owner_phone ownerPhone,
        city,
        a.wisdom_device_sn wisdomdevicesn,
        a.alarm_name alarmname,
        a.alarm_time alarmtime,
        a.a_point_name apointname,
        a.b_point_name bpointname,
        a.c_point_name cpointname,
        a.a_point_voltage apointvoltage,
        a.b_point_voltage bpointvoltage,
        a.c_point_voltage cpointvoltage,
        a.d_point_voltage dpointvoltage,
        a.plant_uid plantUid
        FROM
        bto_operation_fault a
        LEFT JOIN v_station_list b ON a.wisdom_device_sn = b.wisdom_device_sn
        <if test="alarmtime !=null and alarmtime != ''">
            WHERE alarm_time between #{alarmtime} and #{endtime}
        </if>
        <if test="city !=null and city != ''">
            and
            city LIKE '${city}'
        </if>
    </select>
    <select id="AlarmAnalysisHistoryAlarmAll2" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        name name,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        where
        <if test="name1 !=null and name1 != ''">
            name1 like CONCAT ('%',#{name1},'%')
        </if>
        <if test="grade !=null and grade != ''">

            <if test="name1 !=null and name1 != ''">
                and
            </if>
            grade like #{grade}
        </if>
        <if test="startTime !=null and startTime != ''">
            start_time between '${startTime}' and '${endTime}'
        </if>


    </select>
    <select id="OperatorData" resultType="entityDTO.AlarmAnalysis">
        SELECT
        a.name stationName,
        a.create_date createdate,
        a.city city,
        a.plant_id plantId,
        a.plant_uid plantUid,
        a.wisdom_device_sn wisdomdevicesn,
        b.pv_ele pvele,
        b.use_ele useele,
        b.self_ele selfele,
        b.sell_ele sellele,
        b.buy_ele buyele,
        b.time
        FROM
        v_wisdom_sn a
        LEFT JOIN v_operation b ON a.wisdom_device_sn = b.wisdom_device_sn
        WHERE
        <if test="name != null and name != ''">
            a.name like '%${name}%' and
        </if>
        time between '${startTime}' and '${endTime}'
        GROUP BY a.plant_id,time
        ORDER BY
        b.time
    </select>
    <select id="OperatorDataMonth" resultType="entityDTO.AlarmAnalysis">
        SELECT
        stationName,
        createdate,
        city,
        plantId,
        plantUid,
        wisdomdevicesn,
        sum( pv_ele ) pvele,
        sum( use_ele ) useele,
        sum( self_ele ) selfele,
        sum( sell_ele ) sellele,
        sum( buy_ele ) buyele,
        LEFT ( time, 7 ) date
        FROM(
        SELECT
        a.`name` stationName,
        a.create_date createdate,
        a.city city,
        a.plant_id plantId,
        a.plant_uid plantUid,
        a.wisdom_device_sn wisdomdevicesn,
        b.pv_ele ,
        b.use_ele ,
        b.self_ele ,
        b.sell_ele ,
        b.buy_ele ,
        b.time
        FROM
        v_wisdom_sn a
        LEFT JOIN v_operation b ON a.wisdom_device_sn = b.wisdom_device_sn
        WHERE
        <if test="name != null and name != ''">
            a.name like CONCAT ('%',#{name},'%') and
        </if>
        time between '${startTime}' and '${endTime}'
        GROUP BY
        a.plant_id,b.time
        ORDER BY
        time)qq group by plantId
    </select>
    <select id="AlarmByConditionOnListQuery" resultType="entityDTO.AlarmAnalysis">
        SELECT a.sn sn,
        start_time startTime,
        update_time updateTime,
        alarm_message alarmMessage,
        mean mean,
        STATUS STATUS,
        plant_id plantId,
        plant_uid plantUid,
        name1 name1,
        NAME NAME,
        tel tel,
        grade grade
        FROM v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time BETWEEN #{startTime} AND #{endTime}
        <if test="alarmMessage!=null and alarmMessage!='' ">
            and alarm_message = #{alarmMessage}
        </if>
        <if test="mean!=null and mean!='' ">
            and mean = #{mean}
        </if>
        <if test="grade!=null and grade!='' ">
            and grade = #{grade}
        </if>
        <if test="status!=null and status!='' ">
            and status = #{status}
        </if>
        <if test="plantUidArray.length>0 and plantUidArray!=null">
            and plant_uid in
            <foreach collection="plantUidArray" item="plantUid" open="(" separator="," close=")">
                #{plantUid}
            </foreach>
        </if>
    </select>
    <select id="AlarmByConditionOnAggregation" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade,
        status,
        alarm_message alarmMessage
        FROM v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        start_time BETWEEN #{startTime} AND #{endTime}
        <if test="plantUidArray.length>0 and plantUidArray!=null">
            and plant_uid in
            <foreach collection="plantUidArray" item="plantUid" open="(" separator="," close=")">
                #{plantUid}
            </foreach>
        </if>
    </select>
    <select id="selectEventMean" resultType="java.util.HashMap">
        select alarm_code    alarmCode,
               alarm_message alarmMessage,
               mean,
               grade
        from bto_device_event_mean
    </select>
    <!--        <if test="conditions!=null and conditions.length>0 ">-->
    <!--            <foreach collection="conditions" item="condition">-->
    <!--                ,#{condition}-->
    <!--            </foreach>-->
    <!--        </if>-->

</mapper>