package com.botong.controller;

import com.botong.services.service.BtoDeviceTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RequestMapping("/btoDeviceType")
@RestController
@Api(tags = "设备类型")
@CrossOrigin
public class BtoDeviceTypeController {

    @Autowired
    private BtoDeviceTypeService btoDeviceTypeService;

    @ApiOperation("查询所有设备类型")
    @GetMapping("/selectAll")
    public AjaxResult selectAll(){
        return AjaxResult.success("操作成功", btoDeviceTypeService.selectBtoDeviceType());
    }

    @ApiOperation("模糊查询设备类型")
    @GetMapping("/selectLike")
    public AjaxResult selectLike(String model){
        return AjaxResult.success("操作成功",btoDeviceTypeService.selectLikeBtoDeviceType(model));
    }
}
