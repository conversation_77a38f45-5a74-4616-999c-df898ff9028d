package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoUserList;
import entityDTO.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.OperatorInfoVO;
import vo.StationListVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/10/14 20:05
 */
@Mapper
public interface HYARespondentsMapper extends BaseMapper<HYARespondentsDTO> {
    List<HYARespondentsDTO> SelEctRespondentsList();


    Integer AddUserList(@Param("uuid") String uuid,@Param("username") String username,@Param("cuserRegtime") String cuserRegtime);

    List<HYABtoUserListDTO> SelUserList(@Param("plantId") String plantId);


    Integer AddUserPondents(HYABtoUserListDTO hyaBtoUserListDTO);

    Integer AddUsersonPondents(HYABtoUserListDTO hyaBtoUserListDTO2);

    List<HYABtoUserListDTO> selheyuanapp(String userUid);

    List<stationListName> loginSelsubAccount(@Param("userName") String userName);

    List<HYARespondentsDTO> visitorList(@Param("plantId") String plantId);

    Object checkSubAccount(@Param("userName") String userName,@Param("plantId")String plantId);

    List<BtoUserList> checkSubAccountNum(@Param("userName") String userName);

    Integer removePowerStation(@Param("plantId") String plantId,@Param("userUid")String userUid);


    List<BtoUserList> selUser(@Param("userName") String userName);

    List<HYABtoUserListDTO> SelEctRespondentsList2(@Param("username") String username);

    HYABtoUserListDTO seltest(@Param("username") String username);

    List<String> selectSubStationList(@Param("userUid")String userUid);

    List<String> hYSubAccDayMonthAll(@Param("userUid")String userUid,@Param("day2") String day2,@Param("day") String day);

    String StationNum(@Param("userUid")String userUid);

    String powerStationOnline(@Param("userUid")String userUid);

    String AlarmPowerStation(@Param("userUid")String userUid,@Param("curTime") String curTime);

    Integer AlarmInverter(@Param("userUid")String userUid,@Param("curTime") String curTime);

    String TotalInstalledCapacity(@Param("userUid") String userUid);

    List<stationListName> SubaccountPowerPlantAlarm(@Param("userUid") String userUid,@Param("day2") String day2);

    List<stationListName> stationListAccsucess(@Param("userUid") String userUid,@Param("day") String day);

    List<HYABtoUserListDTO> selPassword(@Param("userUid") String userUid,@Param("password") String password);

    List<BtoUserList> selUser2(@Param("plantId") String plantId,@Param("userUid") String userUid);

    List<stationListName> hYSubAccDayMonthAll2(@Param("userUid")String userUid, @Param("day") String day,@Param("day2")String day2);

    List<StationListVo> selectSubStationList2(@Param("userUid")String userUid, @Param("time")String time, @Param("sortWith")Integer sortWith);

    List<NewHYAstationList> filterSubStationList2(@Param("userUid")String userUid,@Param("stationConditionDTO") StationConditionDTO stationConditionDTO, @Param("time") String time);

    List<BtoUserList> selUser3(@Param("plantId") String plantId,@Param("userUid") String userUid);

    List<BtoUserList> seluserName(@Param("username") String username);

    List<BtoUserList> checkSubAccountNum2(@Param("userName") String userName);

    List<BtoUserList> checkSubAccountNum3(@Param("accountName") String accountName);

    List<BtoUserList> checkSubAccountNum4(@Param("accountName") String accountName);

    List<BtoUserList> checkSubAccountNum5(@Param("getid") String getid,@Param("accountName") String accountName);

    int selectCountAccountByUserId(String userUid);

    int selectRepeatHyAdmin();

    Integer selectRepeat(@Param("username") String username);

    int selectplantIdRepeat(@Param("plantId") String plantId,@Param("username") String username);

    //日月年总发电量
    Double getDayElectricity(@Param("userUid") String  userUid);

    Double getMonthElectricity(@Param("userUid") String  userUid);

    Double getAllElectricity(@Param("userUid") String  userUid);

    ArrayList<OperatorInfoVO> OperatorAlarmInformationList(@Param("userUid") String userUid);

    Integer getWarnOperatorNum(@Param("userUid") String userUid);
}
