package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.services.vo.InverterVo;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entityDTO.BtoDeviceListDTO;
import entityDTO.TUBIAO;
import entityDTO.sheBeiJianCe;
import entityDTO.stationNum;
import vo.EfficiencyVo;

import java.util.List;

public interface HYABtoDeviceListService extends IService<BtoDeviceList> {


    Long DeviceAllNum();

    Long DeviceStatus0();

    Long DeviceStatus1();

    List<stationNum> DeviceStatus2();

    double Deviceuptime();

    Long DeviceStatus3();

    List<BtoDeviceListDTO> AllStationEnergy();

    List<BtoDeviceList> DeviceInformation(String plantUid);

    List<BtoDeviceList> DeviceInformationSN(String plantUid);

    List<TUBIAO> DeviceInformationTU(String plantUid);

    List<BtoDeviceList> DeviceMonitorUlist();

    List<sheBeiJianCe> DeviceActivePower(String snName, String tableNme, String tableNme2);

    List<sheBeiJianCe> Devicecurrent(String snName,String tableNme,String tableNme2);

    List<sheBeiJianCe> DeviceVoltage(String snName,String tableNme,String tableNme2);

    List<sheBeiJianCe> DeviceFrequency(String snName,String tableNme,String tableNme2);

    List<sheBeiJianCe> Devicetemperature(String snName,String tableNme,String tableNme2);

    List<EfficiencyVo> workEfficiencyRanking(String pageNum);

    List<BtoDeviceList> getUserInverterNum(String plantId);

    List<sheBeiJianCe> collectionMaxMin(String snName, String tableNme ,String tabletime2);

    List<sheBeiJianCe> collectioAvg(String snName, String tableNme ,String tabletime2);

    Object EquipmentMonitoring(String page, String pageSize);

    Object EquipmentMonitoringlike(String devicesn, String deviceId,String plantUid);

    List<stationNum> Deviceonline();

    Object EquipmentMonitoringplantUid(String page, String pageSize, String plantUid);

    List<String> getDeviceIdsByPlantId(String plantId);

    List<InverterVo> getInverterByPlantId(String plantId);

    Object getInverterDetails(String sn);

    Object GetBasicInverterInformation(String sn);

    PageInfo<BtoDeviceList> InverterOfflineList(String page, String pageSize, String time1, String time2);
}
