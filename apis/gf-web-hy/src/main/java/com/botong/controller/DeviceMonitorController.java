package com.botong.controller;

import com.botong.services.service.DeviceMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@CrossOrigin
@RestController
@RequestMapping("DeviceMonitor")
@Api(tags = "设备检测")
public class DeviceMonitorController {
    @Autowired
    private DeviceMonitorService deviceMonitorService;
    @ApiOperation("采集因子-最大值最小值")
    @GetMapping("collectionMaxMin")
    public AjaxResult collectionMaxMin(String snName, String tableNme, String tabletime2) {
        return AjaxResult.success("操作成功", deviceMonitorService.collectionMaxMin(snName,tableNme,tabletime2));
    }

    @ApiOperation("采集因子-平均值")
    @GetMapping("collectioAvg")
    public AjaxResult collectioAvg(String snName,String tableNme,String tabletime2) {
        return AjaxResult.success("操作成功", deviceMonitorService.collectioAvg(snName,tableNme,tabletime2));
    }

    @ApiOperation("实时监控——设备监测-电流")
    @GetMapping("Devicecurrent")
    public AjaxResult Devicecurrent(String snName,String tableNme,String tableNme2){
        return AjaxResult.success("操作成功",deviceMonitorService.Devicecurrent(snName,tableNme,tableNme2));
    }
}
