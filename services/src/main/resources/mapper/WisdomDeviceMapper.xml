<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.WisdomDeviceMapper">

    <resultMap id="wisdomDeviceInformationDTO" type="entityDTO.WisdomDeviceDTO">
        <result property="imei" column="imei"/>
        <result property="apv" column="apv"/>
        <result property="bpv" column="bpv"/>
        <result property="cpv" column="cpv"/>
        <result property="dpv" column="dpv"/>
        <result property="updateTime" column="update_time"/>
        <result property="alarmName" column="alarm_name"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="wisdomType" column="wisdom_type"/>
    </resultMap>

    <select id="WisdomDeviceInformation" resultType="java.util.HashMap">
        SELECT wisdom_device_sn imei,
               concat(LEFT(update_time, 15), '0:00') updateTime,
               apv,
               bpv,
               cpv,
               dpv,
               wisdom_type wisdomType
        FROM v_operation
        WHERE plant_id = '${plantId}'
        order by updateTime desc
        limit 1
    </select>
    <select id="wisdomDeviceAlarmInformation" resultMap="wisdomDeviceInformationDTO">
        SELECT wisdom_device_sn imei,
               alarm_name,
               alarm_time,
               a_point_voltage  apv,
               b_point_voltage  bpv,
               c_point_voltage  cpv,
               d_point_voltage  dpv
        FROM bto_operation_fault
        WHERE wisdom_device_sn = #{imei}
    </select>
    <select id="selectRssi" resultType="java.util.HashMap">
        SELECT b.Rssi                                  Rssi,
               CONCAT(LEFT(b.update_time, 15), '0:00') RssiTime
        FROM bto_device_list a
                 LEFT JOIN bto_device_total b ON a.device_sn = b.device_sn
        WHERE a.plant_id = #{plantId}
        GROUP BY a.plant_id
    </select>
</mapper>