package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.BtoDeviceMapDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Mapper
public interface HYBtoDeviceMapDTOMapper extends BaseMapper<BtoDeviceMapDTO> {

    List<BtoDeviceMapDTO> selectBtoDeviceMap2(@Param("date") String date,@Param("tableName") String tableName , @Param("plantId") String plantId ,@Param("sn") String sn);

    BtoDeviceMapDTO selectBtoDeviceMap(@Param("table") String table,@Param("b") String b , @Param("plantId") String plantId ,@Param("sn") String sn);

    List<String> selectDistinct(@Param("b") String b , @Param("plantId") String plantId,@Param("table") String table);

    /**
     * 发电统计——查询日月年数据
     * @param tableName
     * @param time
     * @param plantIdArray
     * @return
     */
    List<HashMap<String,Object>> selectPowerStatisticByDay(@Param("tableName") String tableName, @Param("time") String time , @Param("plantIdArray")  String[] plantIdArray);

    List<HashMap<String,Object>> selectPowerStatisticByMonth(@Param("plantIdArray") String[] plantIdArray,@Param("time") String time);

    List<HashMap<String,Object>> selectPowerStatisticByYear(@Param("plantIdArray") String[] plantIdArray,@Param("time") String time);

    List<BtoDeviceMapDTO> selectDevice(@Param("plantId") String plantId);
}
