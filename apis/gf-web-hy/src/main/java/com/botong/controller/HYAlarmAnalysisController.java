package com.botong.controller;

import com.botong.services.service.HYAlarmAnalysisService;
import com.github.pagehelper.PageInfo;
import entity.HyStationAlarmInfo;
import entityDTO.AlarmAnalysis;
import entityDTO.AlarmAnalysisConditionDTO;
import entityDTO.AlarmHandlerDTO;
import entityDTO.AlarmHistoryConditionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import java.util.List;
@CrossOrigin
@Api(tags = "实时报警")
@RestController
@RequestMapping("AlarmAnalysis")
public class HYAlarmAnalysisController {

    @Autowired
    private HYAlarmAnalysisService hyAlarmAnalysisService;

//    @ApiOperation("实时报警——列表表格")
//    @GetMapping("AlarmAnalysisListForm")
//    public AjaxResult AlarmAnalysisListForm(Integer page, Integer pageSize){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisListForm(page,pageSize));
//    }
//
//    @ApiOperation("实时报警——列表表格plantUid")
//    @GetMapping("AlarmAnalysisListFormplantUid")
//    public AjaxResult AlarmAnalysisListFormplantUid(Integer page,Integer pageSize,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisListFormplantUid(page,pageSize,plantUid));
//    }

//
//    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery 实时报警——时间区间查询——精确时分秒 plantUid time 非全
//    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery time plantUid全
//    @ApiOperation("实时报警——时间区间查询——精确时分秒——plantUid")
//    @GetMapping("AlarmAnalysisTimeIntervalQuery")
//    public AjaxResult AlarmAnalysisTimeIntervalQuery(String Time1,String Time2,int page,int pageSize,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisTimeIntervalQuery(Time1,Time2,page,pageSize,plantUid));
//    }
//
//    @ApiOperation("实时报警——时间区间查询——精确时分秒——time plantUid全")
//    @GetMapping("AlarmAnalysisTimeIntervalAll")
//    public AjaxResult AlarmAnalysisTimeIntervalAll(String Time1,String Time2,String page,String pageSize){
//
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> list = hyAlarmAnalysisService.AlarmAnalysisTimeIntervalAll(Time1, Time2);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(list);
//        return AjaxResult.success("操作成功",stationLunBoPageInfo);
//    }
//
//
//    @ApiOperation("实时报警——多条件查询")
//    @GetMapping("AlarmAnalysisTconditionQuery")
//    public AjaxResult AlarmAnalysisTconditionQuery(String startTime,int page,int pageSize,String name,String sn,String name1){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisTconditionQuery(startTime,page,pageSize,name,name1,sn));
//    }
//
//    @ApiOperation("实时报警——聚合plantUid_不传时间，传plantUid")
//    @GetMapping("AlarmAnalysisPolymerization")
//    public AjaxResult AlarmAnalysisPolymerization(String page,String pageSize,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisPolymerization(page,pageSize,plantUid));
//    }
//    @ApiOperation("实时报警——聚合全部")
//    @GetMapping("AlarmAnalysisPolymerizationAll")
//    public AjaxResult AlarmAnalysisPolymerizationAll(String startTime,String endTime,String page,String pageSize){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisPolymerizationAll(startTime,endTime,page,pageSize));
//    }
//
//
//    @ApiOperation("实时报警——聚合全部——plantUid")
//    @GetMapping("AlarmAnalysisPolymerizationplantUid")
//    public AjaxResult AlarmAnalysisPolymerizationplantUid(String startTime,String endTime,String page,String pageSize,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisPolymerizationplantUid(startTime,endTime,page,pageSize,plantUid));
//    }
//    @ApiOperation("实时报警——历史报警_传入告警级别grade")
//    @GetMapping("AlarmAnalysisHistoryAlarm")
//    public AjaxResult AlarmAnalysisHistoryAlarm(String startTime,String endTime,String page,String pageSize,int grade){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisHistoryAlarm(startTime,endTime,page,pageSize,grade));
//    }
//
//    @ApiOperation("实时报警——历史报警全部_人名，事件事件，告警等级3")
//    @GetMapping("AlarmAnalysisHistoryAlarmQuantime3")
//    public AjaxResult AlarmAnalysisHistoryAlarmAll3(Integer page,Integer pageSize,String name1,String startTime,String endTime,String grade){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisHistoryAlarmAll3(page,pageSize,name1,startTime,endTime,grade));
//    }
//
//    @ApiOperation("实时报警——历史报警全部_人名，事件事件，告警等级2")
//    @GetMapping("AlarmAnalysisHistoryAlarmQuantime2")
//    public AjaxResult AlarmAnalysisHistoryAlarmAll2(Integer page,Integer pageSize,String name1,String grade){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisHistoryAlarmAll2(page,pageSize,name1,grade));
//    }
//
//    @ApiOperation("实时报警——历史报警全部")
//    @GetMapping("AlarmAnalysisHistoryAlarmQuann")
//    public AjaxResult AlarmAnalysisHistoryAlarmQuann(String page,String pageSize){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisHistoryAlarmQuann(page,pageSize));
//    }
//    @ApiOperation("实时报警——历史报警全部PlantUid")
//    @GetMapping("AlarmAnalysisHistoryAlarmAll")
//    public AjaxResult AlarmAnalysisHistoryAlarmAll(String startTime,String endTime,String page,String pageSize,int grade,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.AlarmAnalysisHistoryAlarmAll(startTime,endTime,page,pageSize,grade,plantUid));
//    }
//
//
//
//    @ApiOperation("实时报警——报警统计总_日月年统一接口，需要截取")
//    @GetMapping("totalAlarmStatisticsTime")
//    public AjaxResult totalAlarmStatistics(String Time){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTime(Time));
//    }
//
//    @ApiOperation("实时报警——报警统计总")
//    @GetMapping("totalAlarmStatistics")
//    public AjaxResult totalAlarmStatistics(){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatistics());
//    }
//
//    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取")
//    @GetMapping("totalAlarmStatisticsTIME")
//    public AjaxResult totalAlarmStatisticsTIME(String Time,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIME(Time,plantUid));
//    }
//
//
//    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取")
//    @GetMapping("totalAlarmStatisticsTIMETU")
//    public AjaxResult totalAlarmStatisticsTIMETU(String Time){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMETU(Time));
//    }
//    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取")
//    @GetMapping("totalAlarmStatisticsTIMEgeren")
//    public AjaxResult totalAlarmStatisticsTIMEgeren(String Time,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEgeren(Time,plantUid));
//    }
//
//
//    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，遥信")
//    @GetMapping("totalAlarmStatisticsTIMEyaoxin")
//    public AjaxResult totalAlarmStatisticsTIMEyaoxin(String Time){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEgerenyaoxin(Time));
//    }
//
//    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，遥信")
//    @GetMapping("totalAlarmStatisticsTIMEgerenyaoxin")
//    public AjaxResult totalAlarmStatisticsTIMEgerenyaoxin(String Time,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEgerennyaoxin(Time,plantUid));
//    }
//    //时间统计
//    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，时间统计")
//    @GetMapping("totalAlarmStatisticsTIMEstatistics")
//    public AjaxResult totalAlarmStatisticsTIMEstatistics(String Time){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEstatistics(Time));
//    }
//
//    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，时间统计")
//    @GetMapping("totalAlarmStatisticsTIMEgerenstatistics")
//    public AjaxResult totalAlarmStatisticsTIMEgerenstatistics(String Time,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEgerennstatistics(Time,plantUid));
//    }
//
//    //设备统计
//    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，设备统计")
//    @GetMapping("totalAlarmStatisticsTIMEEquipmentStatistics")
//    public AjaxResult totalAlarmStatisticsTIMEEquipmentStatistics(String Time){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEEquipmentStatistics(Time));
//    }
//
//    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，设备统计")
//    @GetMapping("totalAlarmStatisticsTIMEgerenEquipmentStatistics")
//    public AjaxResult totalAlarmStatisticsTIMEgerenEquipmentStatistics(String Time,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEgerenEquipmentStatistics(Time,plantUid));
//    }
//
//    //等级统计
//    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，等级统计")
//    @GetMapping("totalAlarmStatisticsTIMELevelStatistics")
//    public AjaxResult totalAlarmStatisticsTIMELevelStatistics(String Time){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMELevelStatistics(Time));
//    }
//
//    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，遥信")
//    @GetMapping("totalAlarmStatisticsTIMEgerenLevelStatistics")
//    public AjaxResult totalAlarmStatisticsTIMEgerenLevelStatistics(String Time,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEgerennLevelStatistics(Time,plantUid));
//    }
//
//    //列表
//    @ApiOperation("实时报警——报警统计总日月年统一接口，需要截取，列表")
//    @GetMapping("totalAlarmStatisticsTIMEyaoxinlist")
//    public AjaxResult totalAlarmStatisticsTIMEyaoxinlist(String Time){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEyaoxinlist(Time));
//    }
//
//    @ApiOperation("实时报警——报警统计个人日月年统一接口，需要截取，列表")
//    @GetMapping("totalAlarmStatisticsTIMEgerenlist")
//    public AjaxResult totalAlarmStatisticsTIMEgerenlist(String Time,String plantUid){
//        return AjaxResult.success("操作成功",hyAlarmAnalysisService.totalAlarmStatisticsTIMEgerenlist(Time,plantUid));
//    }

    @ApiOperation("电站列表--告警信息")
    @GetMapping("stationAlarmInfoByPlantUid")
    public AjaxResult stationAlarmInfoByPlantUid(Integer page,Integer pageSize,String plantUid){
        PageInfo<HyStationAlarmInfo> stationAlarmInfo = hyAlarmAnalysisService.stationAlarmInfoByPlantUid(page, pageSize, plantUid);
        return AjaxResult.success(stationAlarmInfo);
    }

    @ApiOperation("实时报警----聚合&列表--&自检提示列表--汇总--五合一")
    @PostMapping("AlarmAnalysisByCondition")
    public AjaxResult AlarmAnalysisByCondition(@RequestBody AlarmAnalysisConditionDTO alarmAnalysisConditionDTO) throws NoSuchFieldException, ClassNotFoundException {
        return hyAlarmAnalysisService.AlarmAnalysisByCondition(alarmAnalysisConditionDTO);
    }

    @ApiOperation("历史报警----聚合&列表--汇总--四合一")
    @PostMapping("AlarmHistoryByCondition")
    public AjaxResult AlarmHistoryByCondition(@RequestBody AlarmHistoryConditionDTO alarmHistoryConditionDTO){
        return hyAlarmAnalysisService.AlarmHistoryByCondition(alarmHistoryConditionDTO);
    }

    @ApiOperation("实时报警----告警处理--操作")
    @PostMapping("alarmHandler")
    public AjaxResult alarmHandler(@RequestBody AlarmHandlerDTO alarmHandlerDTO) {
        hyAlarmAnalysisService.alarmHandler(alarmHandlerDTO);
        return AjaxResult.success("操作成功!");
    }

    @ApiOperation("运维器事件数据--河源Web")
    @GetMapping("OperatorEventData")
    public AjaxResult OperatorEventData(Integer page,Integer pageSize,String alarmTime,String address,String endTime){
        return AjaxResult.success("操作成功",hyAlarmAnalysisService.OperatorEventData(page, pageSize, alarmTime, address,endTime));
    }


    @ApiOperation("运维器数据--河源Web")
    @GetMapping("OperatorData")
    public AjaxResult OperatorData(Integer page,Integer pageSize,String startTime,String endTime,String stationName){
        return AjaxResult.success("操作成功",hyAlarmAnalysisService.OperatorData(page,pageSize,startTime,endTime,stationName));
    }

    @ApiOperation("运维器数据-日总数-河源Web")
    @GetMapping("OperatorDataMonth")
    public AjaxResult OperatorDataMonth(Integer page,Integer pageSize,String startTime,String endTime,String stationName){
        return AjaxResult.success("操作成功",hyAlarmAnalysisService.OperatorDataMonth(page,pageSize,startTime,endTime,stationName));
    }
    @ApiOperation("根据电站id查询当日告警数--河源Web")
    @GetMapping("getCurDayAlarmNum")
    public AjaxResult getCurDayAlarmNum(String plantId){
        return AjaxResult.success("操作成功",hyAlarmAnalysisService.getCurDayAlarmNum(plantId));
    }

}
