package utils;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class DateUtil {

    /**
     * 获取当前日期 返回yyyyMMdd
     * 如当前日期：2022年10月22日
     * 返回22021022
     * 用于拼接日表后缀
     */
    public static String getyyyyMMdd() {
        return LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 获取当前日期 返回yyyy-MM-dd
     * 如当前日期：2022年10月22日
     * 返回2202-10-22
     * 用于一些筛选
     */
    public static String getyyy_MM_dd() {
        return LocalDate.now().format(DateTimeFormatter.ISO_DATE);
    }



}
