package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoDeviceListMapper;
import com.botong.services.service.BtoDeviceListService;
import com.botong.services.service.BtoDeviceService;
import com.botong.services.vo.InverterVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entityDTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import vo.EfficiencyVo;

import javax.annotation.Resource;
import java.text.Collator;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;


/**
 * 逆变器列表 impl
 *
 * <AUTHOR> @date 2022-08-11 15:37:05
 */
@Service
public class BtoDeviceListServiceImpl extends ServiceImpl<BtoDeviceListMapper, BtoDeviceList> implements BtoDeviceListService {
    @Resource
    private BtoDeviceListMapper btoDeviceListMapper;

    @Resource
    private BtoDeviceService btoDeviceService;

    @Autowired
    private BtoDeviceListService btoDeviceListService;


    @Override
    public Long DeviceAllNum() {
        return baseMapper.selectCount(null);
    }

    @Override
    public Long DeviceStatus0() {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_status", "0");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Long DeviceStatus1() {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_status", "1");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<stationNum> DeviceStatus2() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String tableName = simpleDateFormat.format(date) + "%";
        return btoDeviceListMapper.devicealert(tableName);
    }

    @Override
    public double Deviceuptime() {
        Long DeviceStatus1 = DeviceStatus1();
        Long online = DeviceAllNum() - DeviceStatus0();
        double Staionuptime = online / DeviceStatus1;
        return Staionuptime;
    }

    @Override
    public Long DeviceStatus3() {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_status", "3");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<BtoDeviceListDTO> AllStationEnergy() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String DateTime = simpleDateFormat.format(date);
        String Time2 = simpleDateFormat2.format(date) + "%";
        List<BtoDeviceListDTO> list = btoDeviceListMapper.AllStationEnergy(DateTime, Time2);
        for (int i = 0; i < list.size() - 1; i++) {
            list.get(i).setTodayEnergy(list.get(i + 1).getTodayEnergy() - list.get(i).getTodayEnergy());
        }
        return btoDeviceListMapper.AllStationEnergy(DateTime, Time2);
    }

    @Override
    public List<BtoDeviceList> DeviceInformation(String plantUid) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");

        //表名
        String tableNme = simpleDateFormat.format(date);
        String tableNme1 = simpleDateFormat1.format(date)+"%";
        List<BtoDeviceList> list = btoDeviceListMapper.DeviceInformationSN(plantUid);
        List<BtoDeviceList> list1 = new ArrayList<>();
        for (BtoDeviceList btoDeviceList :list){
           String snName = btoDeviceList.getSnName();
            btoDeviceList = btoDeviceListMapper.DeviceInformation(snName, tableNme, tableNme1);
            if(btoDeviceList==null){
                BtoDeviceList btoDeviceList1=new BtoDeviceList();
                btoDeviceList1.setSnName(snName);
                list1.add(btoDeviceList1);
            }else {
                list1.add(btoDeviceList);
            }
        }
        return list1;
    }

    @Override
    public List<BtoDeviceList> DeviceInformationSN(String plantUid) {
        return btoDeviceListMapper.DeviceInformationSN(plantUid);
    }

    @Override
    public List<TUBIAO> DeviceInformationTU(String plantUid) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateTime = new SimpleDateFormat("yyyy-MM-dd");
        //表名
        String tableName = simpleDateFormat.format(date);
        String time = dateTime.format(date) + "%";
        return btoDeviceListMapper.DeviceInformationTU(plantUid, tableName, time);
    }

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'DeviceMonitorUlist' ", cacheManager = "GF_Web_Cache")
    public List<HashMap<String,String>> DeviceMonitorUlist() {
        return btoDeviceListMapper.DeviceMonitorUlist();
    }

    @Override
    public List<sheBeiJianCe> DeviceActivePower(String snName, String tableName, String tableName2) {
        return btoDeviceListMapper.DeviceActivePower(snName, tableName, tableName2);
    }

    @Override
    public List<sheBeiJianCe> Devicecurrent(String snName, String tableName, String tableName2) {
        return btoDeviceListMapper.Devicecurrent(snName, tableName, tableName2);
    }

    @Override
    public List<sheBeiJianCe> DeviceVoltage(String snName, String tableName, String tableName2) {
        return btoDeviceListMapper.DeviceVoltage(snName, tableName, tableName2);
    }

    @Override
    public List<sheBeiJianCe> DeviceFrequency(String snName, String tableName, String tableName2) {
        return btoDeviceListMapper.DeviceFrequency(snName, tableName, tableName2);
    }

    @Override
    public List<sheBeiJianCe> Devicetemperature(String snName, String tableName, String tableName2) {
        return btoDeviceListMapper.Devicetemperature(snName, tableName, tableName2);
    }

    @Override
    @Cacheable(cacheNames = "workEfficiencyRanking" ,key = " 'workEfficiencyRanking' " )//或者这样写key = "#id"
    public HashMap<String, Object> workEfficiencyRanking() {
        HashMap<String, Object> hashMap = new HashMap<>();
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        tableName += format.format(date);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time = dateFormat.format(date).concat("%");
        List<EfficiencyVo> efficiencyVos = baseMapper.workEfficiencyRanking(tableName, time);
//        for (int i = 0; i < efficiencyVos.size(); i++) {
//            String subStationName = efficiencyVos.get(i).getStationName().substring(4);
//            efficiencyVos.get(i).setStationName(subStationName);
//        }
        Collections.sort(efficiencyVos, new Comparator<EfficiencyVo>() {
            @Override
            public int compare(EfficiencyVo vo1, EfficiencyVo vo2) {
                //获取中文环境
                Comparator<Object> com = Collator.getInstance(java.util.Locale.CHINA);
                return com.compare(vo1.getStationName(), vo2.getStationName());
            }
        });
        hashMap.put("total", efficiencyVos.size()+1);
        hashMap.put("list", efficiencyVos);
        return hashMap;
    }


    @Override
    public List<sheBeiJianCe> collectionMaxMin(String snName, String tablName, String tabletime2) {
        return btoDeviceListMapper.collectionMaxMin(snName, tablName, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> collectioAvg(String snName, String tableNme, String tabletime2) {
        return btoDeviceListMapper.collectioAvg(snName, tableNme, tabletime2);
    }

    @Override
    public Object EquipmentMonitoring(String page, String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableNme = simpleDateFormat.format(date);
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat dateFormattime10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormattime20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //截取当前时间前10分钟
        String Nowtime10 = (dateFormattime10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (dateFormattime20.format(now20)).substring(0, 15) + "%";
        List<sheBeiJianCe> stationLunBos = btoDeviceListMapper.selectListt(page, pageSize, tableNme, Nowtime10, Nowtime20);
        PageInfo<sheBeiJianCe> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public Object EquipmentMonitoringlike(String devicesn, String deviceId, String plantUid) {
        Date date = new Date();
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String tableNme = time.format(date);
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat dateFormattime10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormattime20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //截取当前时间前10分钟
        String Nowtime10 = (dateFormattime10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (dateFormattime20.format(now20)).substring(0, 15) + "%";
        List<sheBeiJianCe> ceList = btoDeviceListMapper.EquipmentMonitoringlike(devicesn, deviceId, tableNme, Nowtime20, Nowtime10, plantUid);
        return ceList;
    }

    @Override
    public List<BtoDeviceList> getUserInverterNum(String plantId) {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("datalogger_sn", "device_status");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @Cacheable(cacheNames = "Index" ,key = " 'id1' " )//或者这样写key = "#id"
    public List<stationNum> Deviceonline() {
        Date date = new Date();
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String tableNme2 = time.format(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH");
        String tableName = simpleDateFormat.format(date) + "%";
        List<stationNum> KONG = btoDeviceListMapper.Deviceonline(tableName, tableNme2);
        if (btoDeviceListMapper.Deviceonline(tableName, tableNme2) == null) {
            return KONG;
        }
        return btoDeviceListMapper.Deviceonline(tableName, tableNme2);
    }

    @Override
    public Object EquipmentMonitoringplantUid(String page, String pageSize, String plantUid) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableNme = simpleDateFormat.format(date);
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat dateFormattime10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormattime20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //截取当前时间前10分钟
        String Nowtime10 = (dateFormattime10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (dateFormattime20.format(now20)).substring(0, 15) + "%";
        List<sheBeiJianCe> stationLunBos = btoDeviceListMapper.EquipmentMonitoringplantUid(page, pageSize, tableNme, Nowtime10, Nowtime20, plantUid);
        PageInfo<sheBeiJianCe> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public List<String> getDeviceIdsByPlantId(String plantId) {
        ArrayList<String> deviceIds = new ArrayList<>();
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("device_sn");
        queryWrapper.eq("plant_id", plantId);
        for (BtoDeviceList btoDeviceList : baseMapper.selectList(queryWrapper)) {
            deviceIds.add(btoDeviceList.getDeviceSn());
        }
        return deviceIds;
    }

    @Override
    public List<InverterVo> getInverterByPlantId(String plantId) {
        List<InverterVo> inverterByPlantId = baseMapper.getInverterByPlantId(plantId);
        for (InverterVo inverterVo : inverterByPlantId) {
            //根据sn获取发电量
            inverterVo.setBtoDevices(btoDeviceService.getElectricityBySn(inverterVo.getSn()));
        }
        return inverterByPlantId;
    }

    @Override
    public Object getInverterDetails(String sn) {

        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        tableName += dateFormat.format(date);
        String time = format.format(date);

        return btoDeviceService.getInverterDetails(sn, tableName, time += "%");
    }

    @Override
    public Object GetBasicInverterInformation(String sn) {
        return btoDeviceListMapper.GetBasicInverterInformation(sn);
    }

    @Override
    public PageInfo<BtoDeviceList> InverterOfflineList(String page, String pageSize, String time1, String time2) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String time3=time2+"%";
        List<BtoDeviceList> list= btoDeviceListMapper.InverterOfflineList(page,pageSize,time1,time3);
        PageInfo<BtoDeviceList> stationLunBoPageInfo = new PageInfo<>(list);
        return stationLunBoPageInfo;
    }











    @Override
    public HashMap<String, Object> mergecollectionMaxMin(String snName, String tableName, String tableName2) {
        HashMap<String, Object> iacMaxmap = new HashMap<>();
        HashMap<String, Object> iacMinmap = new HashMap<>();
        HashMap<String, Object> vacMaxmap = new HashMap<>();
        HashMap<String, Object> vacMinmap = new HashMap<>();
        HashMap<String, Object> ActivePowerMaxmap = new HashMap<>();
        HashMap<String, Object> ActivePowerMinmap = new HashMap<>();
        HashMap<String, Object> FaxMaxmap = new HashMap<>();
        HashMap<String, Object> FaxMinmap = new HashMap<>();
        HashMap<String, Object> TemperatureMinmap = new HashMap<>();
        HashMap<String, Object> TemperatureMaxmap = new HashMap<>();
        HashMap<String, Object> avg = new HashMap<>();

        HashMap<String, Object> map = new HashMap<>();

        List<sheBeiJianCe> list1= btoDeviceListService.Devicecurrent(snName,tableName,tableName2);//电流
        List<sheBeiJianCe> list2= btoDeviceListService.DeviceVoltage(snName,tableName,tableName2);//电压
        List<sheBeiJianCe> list3= btoDeviceListService.DeviceFrequency(snName,tableName,tableName2);//频率
        List<sheBeiJianCe> list4= btoDeviceListService.Devicetemperature(snName,tableName,tableName2);//温度
        List<sheBeiJianCe> list5= btoDeviceListService.DeviceActivePower(snName,tableName,tableName2);//有功功率
        List<sheBeiJianCe> list6=btoDeviceListService.collectioAvg(snName,tableName,tableName2);//平均值


        //电流最大值
        sheBeiJianCe iacMaxA = list1.stream().max(Comparator.comparing(sheBeiJianCe::getIac1)).get();
        sheBeiJianCe iacMaxB = list1.stream().max(Comparator.comparing(sheBeiJianCe::getIac2)).get();
        sheBeiJianCe iacMaxC = list1.stream().max(Comparator.comparing(sheBeiJianCe::getIac3)).get();

        //电流最小值
        sheBeiJianCe iacMinA = list1.stream().min(Comparator.comparing(sheBeiJianCe::getIac1)).get();
        sheBeiJianCe iacMinB = list1.stream().min(Comparator.comparing(sheBeiJianCe::getIac2)).get();
        sheBeiJianCe iacMinC = list1.stream().min(Comparator.comparing(sheBeiJianCe::getIac3)).get();


        //电压最大值
        sheBeiJianCe vacMaxA = list2.stream().max(Comparator.comparing(sheBeiJianCe::getVac1)).get();
        sheBeiJianCe vacMaxB = list2.stream().max(Comparator.comparing(sheBeiJianCe::getVac2)).get();
        sheBeiJianCe vacMaxC = list2.stream().max(Comparator.comparing(sheBeiJianCe::getVac3)).get();
        //电压最小值
        sheBeiJianCe vacMinA = list2.stream().min(Comparator.comparing(sheBeiJianCe::getVac1)).get();
        sheBeiJianCe vacMinB = list2.stream().min(Comparator.comparing(sheBeiJianCe::getVac2)).get();
        sheBeiJianCe vacMinC = list2.stream().min(Comparator.comparing(sheBeiJianCe::getVac3)).get();

        //功率最大值
        sheBeiJianCe PowerMax = list5.stream().max(Comparator.comparing(sheBeiJianCe::getActivePower)).get();
        //功率最小值
        sheBeiJianCe Powermin = list5.stream().min(Comparator.comparing(sheBeiJianCe::getActivePower)).get();

        //频率最大值
        sheBeiJianCe FacMax = list3.stream().max(Comparator.comparing(sheBeiJianCe::getFac1)).get();
        //频率最小值
        sheBeiJianCe Facmin = list3.stream().min(Comparator.comparing(sheBeiJianCe::getFac1)).get();

        //温度最大值
        sheBeiJianCe TemperatureMax = list4.stream().max(Comparator.comparing(sheBeiJianCe::getTemperature)).get();
        //温度最小值
        sheBeiJianCe Temperaturemin = list4.stream().min(Comparator.comparing(sheBeiJianCe::getTemperature)).get();

        iacMaxmap.put("iacMaxA",iacMaxA.getIac1());
        iacMaxmap.put("iacMaxATime",iacMaxA.getTime());
        iacMaxmap.put("iacMaxB",iacMaxB.getIac2());
        iacMaxmap.put("iacMaxBTime",iacMaxB.getTime());
        iacMaxmap.put("iacMaxC",iacMaxC.getIac3());
        iacMaxmap.put("iacMaxCTime",iacMaxC.getTime());


        iacMinmap.put("iacMinA",iacMinA.getIac1());
        iacMinmap.put("iacMinATime",iacMinA.getTime());
        iacMinmap.put("iacMinB",iacMinB.getIac2());
        iacMinmap.put("iacMinBTime",iacMinB.getTime());
        iacMinmap.put("iacMinC",iacMinC.getIac3());
        iacMinmap.put("iacMinCTime",iacMinC.getTime());

        vacMaxmap.put("vacMaxA",vacMaxA.getVac1());
        vacMaxmap.put("vacMaxATime",vacMaxA.getTime());
        vacMaxmap.put("vacMaxB",vacMaxB.getVac2());
        vacMaxmap.put("vacMaxBTime",vacMaxA.getTime());
        vacMaxmap.put("vacMaxC",vacMaxC.getVac3());
        vacMaxmap.put("vacMaxCTime",vacMaxC.getTime());

        vacMinmap.put("vacMinA",vacMinA.getVac1());
        vacMinmap.put("vacMinATime",vacMinA.getVac1());
        vacMinmap.put("vacMinB",vacMinB.getTime());
        vacMinmap.put("vacMinBTime",vacMinB.getVac2());
        vacMinmap.put("vacMinC",vacMinC.getVac3());
        vacMinmap.put("vacMinCTime",vacMinC.getTime());

        ActivePowerMaxmap.put("PowerMax",PowerMax.getActivePower());
        ActivePowerMaxmap.put("PowerMaxTime",PowerMax.getTime());
        ActivePowerMinmap.put("Powermin",Powermin.getActivePower());
        ActivePowerMinmap.put("PowerminTime",Powermin.getTime());

        FaxMaxmap.put("FacMax",FacMax.getFac1());
        FaxMaxmap.put("FacMaxTime",FacMax.getTime());
        FaxMinmap.put("Facmin",Facmin.getFac1());
        FaxMinmap.put("FacMinTime",Facmin.getTime());

        TemperatureMaxmap.put("TemperatureMax",TemperatureMax.getTemperature());
        TemperatureMaxmap.put("TemperatureMaxTime",TemperatureMax.getTime());
        TemperatureMinmap.put("Temperaturemin",Temperaturemin.getTemperature());
        TemperatureMinmap.put("TemperatureminTime",Temperaturemin.getTime());


        map.put("iacMaxmap",iacMaxmap);
        map.put("iacMinmap",iacMinmap);
        map.put("vacMaxmap",vacMaxmap);
        map.put("vacMinmap",vacMinmap);
        map.put("ActivePowerMaxmap",ActivePowerMaxmap);
        map.put("ActivePowerMinmap",ActivePowerMinmap);
        map.put("FaxMaxmap",FaxMaxmap);
        map.put("FaxMinmap",FaxMinmap);
        map.put("TemperatureMinmap",TemperatureMinmap);
        map.put("TemperatureMaxmap",TemperatureMaxmap);

        //平均值
        for(int i = 0;i < list6.size();i++){
            sheBeiJianCe a = list6.get(i);//直接拿这个a去点get或者set就行了
            avg.put("iac1",a.getIac1());
            avg.put("iac2",a.getIac2());
            avg.put("iac3",a.getIac3());
            avg.put("vac1",a.getVac1());
            avg.put("vac2",a.getVac2());
            avg.put("vac3",a.getVac3());
            avg.put("ActivePower",a.getActivePower());
            avg.put("Temperature",a.getTemperature());
            avg.put("fac",a.getFac1());
        }
        map.put("avg",avg);

        return map;
    }













    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'DeviceNum' ", cacheManager = "GF_Web_Cache")
    public HashMap<String, Integer> DeviceNum() {
        HashMap<String, Integer> deviceNumMap = new HashMap<>();
        //获取所有逆变器数量
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<BtoDeviceList>();
        Long deviceAllNumL = baseMapper.selectCount(queryWrapper);
        Integer deviceAllNum = deviceAllNumL.intValue();
        //获取在线逆变器数量
        Date date = new Date();
        Date near_date = new Date(date.getTime() - 60 * 60 * 1000);
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String tableName = time.format(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String curTime = simpleDateFormat.format(date).substring(0,10) + "%";
        String nearTime = simpleDateFormat.format(near_date).substring(0, 13);
        Integer onlineDeviceNum = btoDeviceListMapper.deviceOnline(nearTime, tableName);
        //获取告警逆变器数量
        Integer alertDeviceNum = btoDeviceListMapper.deviceAlert(curTime);
        //获取正常逆变器数量
        Integer normalDeviceNum = onlineDeviceNum - alertDeviceNum;
        //获取离线逆变器数量
        Integer offlineDeviceNum = deviceAllNum - onlineDeviceNum;
        deviceNumMap.put("deviceAllNum", deviceAllNum);
        deviceNumMap.put("onlineDeviceNum", onlineDeviceNum);
        deviceNumMap.put("alertDeviceNum", alertDeviceNum);
        deviceNumMap.put("normalDeviceNum", normalDeviceNum);
        deviceNumMap.put("offlineDeviceNum", offlineDeviceNum);
        return deviceNumMap;
    }

}
