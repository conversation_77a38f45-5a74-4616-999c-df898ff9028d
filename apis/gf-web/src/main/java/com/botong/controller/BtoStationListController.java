package com.botong.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import com.botong.services.service.BtoStationListService;
import com.botong.services.service.IndexService;
import entityDTO.PeakPowerAndEnergy;
import entityDTO.StationDeviceNum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import utils.AjaxResult;


/**
 * 电站列表
 *
 * <AUTHOR> @date 2022-08-11 15:36:33
 */
@CrossOrigin
@RestController
@RequestMapping("Index")
@Api(tags = "首页-站点概览、综合大屏、站点轮播&电站列表")
public class BtoStationListController {

    @Autowired
    private BtoStationListService btoStationListService;

    @Autowired
    private IndexService indexService;

    @ApiOperation(value = "总装机容量&日-月-总-近六月-近七日发电量",response = PeakPowerAndEnergy.class)
    @GetMapping("peakPowerAndStationEnergy")
    public AjaxResult peakPowerAndStationEnergy(){
        return AjaxResult.success("操作成功",indexService.peakPowerAndStationEnergy());
    }

    @ApiOperation(value = "电站&逆变器数量(总、正常、在线、离线、告警)、告警记录数",response = StationDeviceNum.class)
    @GetMapping("stationAndDeviceNum")
    public AjaxResult stationAndDeviceNum(){
        return AjaxResult.success("操作成功",indexService.stationAndDeviceNum());
    }

    @ApiOperation(value = "电站实时发电列表&站点轮播")
    @GetMapping("/realTimePowerCarousel")
    public AjaxResult realTimePowerCarousel(Integer page, Integer pageSize){
        return AjaxResult.success("操作成功",indexService.realTimePowerCarousel(page,pageSize));
    }

    @ApiOperation("总装机容量")
    @GetMapping("StationAllpeakPower")
    public AjaxResult StationAllpeakPower(){
        return AjaxResult.success("操作成功",btoStationListService.StationAllpeakPower());
    }

    @ApiOperation("日-月-总--发电量")
    @GetMapping("getStationEnergy")
    public AjaxResult getStationEnergy(){
        return AjaxResult.success("操作成功",btoStationListService.getStationEnergy());
    }

    @ApiOperation("光伏监控中心")
    @GetMapping("GFmonitoringCenter")
    public AjaxResult GFmonitoringCenter(){
        return AjaxResult.success("操作成功",btoStationListService.GFmonitoringCenter());
    }


    @ApiOperation("获取所有的电站名称名称")
    @GetMapping("getAllStationName")
    public AjaxResult getAllStationName(){
        return AjaxResult.success("操作成功",btoStationListService.getAllStationName());
    }


    @ApiOperation("总电站数量列表")
    @GetMapping("ListOfTotalPowerStations")
    public AjaxResult ListOfTotalPowerStations(String page,String pageSize,String status,String stationName){
        return AjaxResult.success("操作成功",btoStationListService.ListOfTotalPowerStations(status,stationName,page,pageSize));
    }


    @ApiOperation("总电站数量列表_电话")
    @GetMapping("ListOfTotalPowerStationsPhone")
    public AjaxResult ListOfTotalPowerStationsPhone(String plantUid){
        return AjaxResult.success("操作成功",btoStationListService.ListOfTotalPowerStationsPhone(plantUid));
    }


    @ApiOperation("总电站数量列表_模糊查询")
    @GetMapping("ListOfTotalPowerStationsstationName")
    public AjaxResult ListOfTotalPowerStationsstationName(String page,String pageSize,String stationName){
        return AjaxResult.success("操作成功",btoStationListService.ListOfTotalPowerStationsstationName(page,pageSize,stationName));
    }

    @ApiOperation("电站数量及状态列表")
    @GetMapping("/stationNum")
    public AjaxResult StationNum(){
        return AjaxResult.success("操作成功",btoStationListService.StationNum());
    }



































































































}
