package com.botong.services.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.EfficiencyMapper;
import com.botong.services.service.EfficiencyService;
import com.botong.services.utils.PageHelp1;
import com.github.pagehelper.PageInfo;
import entityDTO.EfficiencyDTO;
import entityDTO.EfficiencyPageDTO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import utils.AjaxResult;
import utils.ListPageUtils;

import java.util.ArrayList;
import java.util.List;
@Service
public class EfficiencyServiceImpl extends ServiceImpl<EfficiencyMapper, EfficiencyDTO> implements EfficiencyService {

    @Autowired
    private EfficiencyMapper efficiencyMapper;

    @Override
    public AjaxResult PowerGenerationMonth(String plantId, String month, Integer page, Integer pageSize) {
        String[] curSplit = month.split("-");
        int year = Integer.parseInt(curSplit[0]);
        String curMonthStr = curSplit[1];
        Integer curMonthNum = Integer.parseInt(curMonthStr);
        String curMonthSql = year + (curMonthNum > 10 ? "-" + curMonthStr : "-" + curMonthStr).concat("%");
        // last
        Integer lastMonthNum = curMonthNum - 1 == 0 ? 12 : curMonthNum - 1;
        String lastMonthStr = lastMonthNum > 10 ? String.valueOf(lastMonthNum) : "0" + lastMonthNum;
        String lastMonthSql = lastMonthNum == 12 ? String.valueOf(year - 1) : year + "-" + lastMonthStr + "%";
        //当前月所有数据
        List<EfficiencyDTO> efficiencyCurMonth = efficiencyMapper.PowerGenerationMonth(plantId, curMonthSql);
        // 上一个月所有数据
        List<EfficiencyDTO> efficiencyLastMonth = efficiencyMapper.PowerGenerationMonth(plantId, lastMonthSql);

        if (CollUtil.isNotEmpty(efficiencyCurMonth) && CollUtil.isNotEmpty(efficiencyLastMonth)){
            //调用求同比的方法
            Double[] yoy = getYoy(efficiencyLastMonth, efficiencyCurMonth, 8, 10);
            for (int i = 0;i<efficiencyCurMonth.size();i++) {
                efficiencyCurMonth.get(i).setYoy(yoy[i]);
                System.out.println(efficiencyCurMonth);
                efficiencyCurMonth.set(i,efficiencyCurMonth.get(i));
            }
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurMonth,"月");
            for (int i = 0;i<efficiencyCurMonth.size();i++) {
                efficiencyCurMonth.get(i).setMom(mom[i]);
                efficiencyCurMonth.set(i,efficiencyCurMonth.get(i));
            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurMonth, page, pageSize);
            return AjaxResult.success("请求成功!",subList);
        }else if( CollUtil.isNotEmpty(efficiencyCurMonth) && CollUtil.isEmpty(efficiencyLastMonth)){
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurMonth,"月");
            for (int i = 0;i<efficiencyCurMonth.size();i++) {
                efficiencyCurMonth.get(i).setMom(mom[i]);
                efficiencyCurMonth.set(i,efficiencyCurMonth.get(i));
            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurMonth, page, pageSize);
            return  AjaxResult.success("无同比数据:上月份无数据",subList);
        }else if(CollUtil.isNotEmpty(efficiencyLastMonth) && CollUtil.isEmpty(efficiencyCurMonth)){
            return AjaxResult.error("本月份无数据!");
        }else {
            return AjaxResult.error("本月份和上月份均无数据");
        }
    }

    @Override
    public AjaxResult PowerGenerationYear(String plantId, String year, Integer page, Integer pageSize) {
        String lastYear = String.valueOf(Integer.parseInt(year)-1).concat("%");
        String curYear = year.concat("%");
        List<EfficiencyDTO> efficiencyCurYear = efficiencyMapper.PowerGenerationYear(plantId, curYear);
        List<EfficiencyDTO> efficiencyLastYear = efficiencyMapper.PowerGenerationYear(plantId, lastYear);
        if (CollUtil.isNotEmpty(efficiencyCurYear) && CollUtil.isNotEmpty(efficiencyLastYear)){
            //调用求同比的方法
            Double[] yoy = getYoy(efficiencyLastYear, efficiencyCurYear, 0, 3);
            for (int i = 0;i<efficiencyCurYear.size();i++) {
                efficiencyCurYear.get(i).setYoy(yoy[i]);
                System.out.println(efficiencyCurYear);
                efficiencyCurYear.set(i,efficiencyCurYear.get(i));
            }
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurYear,"年");
            for (int i = 0;i<efficiencyCurYear.size();i++) {
                efficiencyCurYear.get(i).setMom(mom[i]);
                efficiencyCurYear.set(i,efficiencyCurYear.get(i));
            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurYear, page, pageSize);
            return AjaxResult.success("请求成功!",subList);
        }else if( CollUtil.isNotEmpty(efficiencyCurYear) && CollUtil.isEmpty(efficiencyLastYear)){
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurYear,"年");
            for (int i = 0;i<efficiencyCurYear.size();i++) {
                efficiencyCurYear.get(i).setMom(mom[i]);
                efficiencyCurYear.set(i,efficiencyCurYear.get(i));

            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurYear, page, pageSize);
            return  AjaxResult.success("无同比数据:上年份无数据",subList);
        }else if(CollUtil.isNotEmpty(efficiencyLastYear) && CollUtil.isEmpty(efficiencyCurYear)){
            return AjaxResult.error("本年份无数据!");
        }else {
            return AjaxResult.error("本年份和上年份均无数据");
        }
    }

    /**
     * 封装求同比数据的方法
     */
    @NotNull
    private static Double[] getYoy(List<EfficiencyDTO> lastEfficiencyDTOs, List<EfficiencyDTO> curEfficiencyDTOs, int startIndex, int endIndex) {
        //求出本月/年有效数据的集合大小
        int Isize = curEfficiencyDTOs.size();
        int Jsize = lastEfficiencyDTOs.size();
        //定义一个结果集存放同比数据并赋初值为0.0
        Double[] yoy = new Double[Isize];
        for (int i = 0;i<Isize;i++){
            yoy[i] = 0.0;
        }
        int i = 0;
        int j = 0;
        while (i < Isize && j <Jsize) {
            //当前月/年的每一天/月的日期(Integer)
            Integer curDay = Integer.parseInt(curEfficiencyDTOs.get(i).getDate().substring(startIndex, endIndex));
            //上一月/年的每一天/月的日期(Integer)
            Integer lastDay = Integer.parseInt(lastEfficiencyDTOs.get(j).getDate().substring(startIndex, endIndex));
            //判断当前月日期是否小于或等于上月日期
            if (curDay <= lastDay) {
                //判断两个月/年的天是否是同一天/月
                if (curDay.equals(lastDay)) {
                    //直接求出这一天/月的同比数据并存入yoy结果集
                    Double curEnergy = curEfficiencyDTOs.get(i).getEnergy();
                    Double lastEnergy = lastEfficiencyDTOs.get(j).getEnergy();
                    double yoyDay = (curEnergy - lastEnergy) / lastEnergy;
                    if(!NumberUtil.isValid(lastEnergy)){
                        yoy[i] = 0.0;
                    }else {
                        yoy[i] = yoyDay;
                    }
//                    yoy[i] = yoyDay;
                    //i和j的指针往后移一位
                    i++;
                    if (j<Jsize-1){
                        j++;
                    }
                }
                i++;
            } else {
                //j的指针往后移一位，i不动
                if (j >= Jsize) {
                    break;
                }
                j++;
            }
        }
        return yoy;
    }
    /**
     * 封装求环比
     */
    private static  Double[] getMom( List<EfficiencyDTO> curEfficiencyDTOs,String dateStr){
        //求本月/年数据条数
        int size = curEfficiencyDTOs.size();
        //定义一个结果集存放同比数据并赋初值为0.0
        Double[] mom = new Double[size];
        for (int i = 0;i<size;i++){
            mom[i] = 0.0;
        }
        long between = 0L;
        //求当前月/年的每一天/月
        for (int i = 1; i < curEfficiencyDTOs.size(); i++) {
            DateTime lastDate = DateUtil.parse(curEfficiencyDTOs.get(i-1).getDate());
            DateTime curDate = DateUtil.parse(curEfficiencyDTOs.get(i).getDate());
            if ("年".equals(dateStr)){
                int lastMonth = lastDate.month();
                int curMonth = curDate.month();
                between = curMonth - lastMonth;
            }else {
                between = DateUtil.between(lastDate, curDate, DateUnit.DAY);
            }
            if (between==1){
                Double lastEnergy = curEfficiencyDTOs.get(i-1).getEnergy();
                Double curEnergy = curEfficiencyDTOs.get(i).getEnergy();
                double curMom = (curEnergy - lastEnergy) / lastEnergy;
                if (!NumberUtil.isValid(lastEnergy)){
                    mom[i] = 0.0;
                }else {
                    mom[i] = curMom;
                }
            }
        }
        return mom;
    }

/*    @Override
    public AjaxResult PowerGenerationMonth(String plantId , String month0, Integer page, Integer pageSize){
        String month = month0 + "%";
        String[] split = month0.split("-");
        String monthDq = split[1];  //当前月
        List<EfficiencyDTO> efficiencyDTO_DQS = efficiencyMapper.PowerGenerationMonth(plantId, month); //当前月环比的数据
        ArrayList<EfficiencyDTO> list = new ArrayList<>();
        int size = efficiencyDTO_DQS.size();
        for (int i = 0; i < size; i++) {    //求当前月的每一天
            int month1 = Integer.parseInt(monthDq);
            int MonthQn = month1 - 1;             //查询上一个月
            if (MonthQn < 10) {
                String[] split1 = month0.split("-");
                String qyue = split[0]  +"-0"+ MonthQn +"%";
                List<EfficiencyDTO> efficiencyDTO_SYGS = efficiencyMapper.PowerGenerationMonth(plantId, qyue);  // 上一个月的环比数据
                EfficiencyDTO efficiencyDTO_DQ = efficiencyDTO_DQS.get(i);  //以当前为准
                EfficiencyDTO efficiencyDTO_SYG = efficiencyDTO_SYGS.get(i);
                double yoy = (efficiencyDTO_DQ.getEnergy() - efficiencyDTO_SYG.getEnergy()) / efficiencyDTO_SYG.getEnergy();
                efficiencyDTO_DQ.setYoy(yoy);
                list.add(efficiencyDTO_DQ);
            } else {
                String[] split1 = month0.split("-");
                String qyue = split[0]  +"-"+ MonthQn +"%";
                List<EfficiencyDTO> efficiencyDTO_SYGS = efficiencyMapper.PowerGenerationMonth(plantId, qyue);  // 上一个月的环比数据
                EfficiencyDTO efficiencyDTO_DQ = efficiencyDTO_DQS.get(i);  //以当前为准
                EfficiencyDTO efficiencyDTO_SYG = efficiencyDTO_SYGS.get(i);
                double yoy = (efficiencyDTO_DQ.getEnergy() - efficiencyDTO_SYG.getEnergy()) / efficiencyDTO_SYG.getEnergy();
                efficiencyDTO_DQ.setYoy(yoy);
                list.add(efficiencyDTO_DQ);
            }
        }
        PageHelp1 pageHelp1 = new PageHelp1();
        PageInfo info = pageHelp1.pageHelper(list, page, pageSize);
        return info;
    }
    @Override
    public PageInfo<EfficiencyDTO> PowerGenerationYear(String plantId,String year1,Integer page,Integer pageSize) {
            String year = year1 + "%";
            List<EfficiencyDTO> efficiencyDTOS = efficiencyMapper.PowerGenerationYear(plantId, year);
            ArrayList<EfficiencyDTO> list = new ArrayList<>();
            for (int i = 1; i < efficiencyDTOS.size(); i++) {

                double yoy = (efficiencyDTOS.get(i).getEnergy() - efficiencyDTOS.get(i - 1).getEnergy()) / efficiencyDTOS.get(i - 1).getEnergy();
                efficiencyDTOS.get(i).setYoy(yoy);
                list.add(efficiencyDTOS.get(i));
            }
            PageHelp1 pageHelp1 = new PageHelp1();
            PageInfo info = pageHelp1.pageHelper(list, page, pageSize);
            return info;
    }*/

}
