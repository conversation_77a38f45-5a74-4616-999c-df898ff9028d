package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/4 11:03
 */
@Data
@ApiModel("逆变器信息实体-APP")
public class InverterInfoDTO implements Serializable {

    @ApiModelProperty("所属电站ID")
    private String plantId;
    @ApiModelProperty("逆变器别名(SN)")
    private String inverterSN;
    @ApiModelProperty("当前功率")
    private String currentPower;
    @ApiModelProperty("当日发电量")
    private String todayEnergy;
    @ApiModelProperty("累计发电量")
    private String totalEnergy;
    @ApiModelProperty("质保日期")
    private String warrantyDate;
}
