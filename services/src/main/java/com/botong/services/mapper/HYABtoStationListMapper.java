package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import entity.BtoStationList;
import entityDTO.AllStationList;
import entityDTO.BTOstationListDTO;
import entityDTO.StationLunBo;
import entityDTO.siteCarouselPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HYABtoStationListMapper extends BaseMapper<BtoStationList> {

    List<BtoStationList> sqlStaion();

    List<BTOstationListDTO> StationAllenergy(@Param("tableNme") String tableNme, @Param("nowtime10") String nowtime10, @Param("nowtime20") String nowtime20);


    Page<siteCarouselPage> selectPages(Page<siteCarouselPage> page, QueryWrapper<Object> wrapper);

    List<StationLunBo> StationStatus2(@Param("tableName") String tableName);

    List<StationLunBo> Stationonline(@Param("tableName")String tableName, @Param("tableNme2") String tableNme2);

    List<AllStationList> selectListt(@Param("page") String page, @Param("pageSize") String pageSize, @Param("time") String time, @Param("time2")String time2);

    List<AllStationList> ListOfTotalPowerStationsstationName(@Param("time") String time,@Param("time2")String time2,@Param("stationName") String stationName);

    Object ListOfTotalPowerStationsPhone(@Param("plantUid") String plantUid);
}