package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.AlarmAnalysis;
import entityDTO.OperatorEvents;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface AlarmAnalysisMapper extends BaseMapper<AlarmAnalysis> {
    List<AlarmAnalysis> selectListt(@Param("page") Integer page, @Param("pageSize") Integer pageSize,@Param("time") String time);

    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery 实时报警——时间区间查询——精确时分秒 plantUid time 非全
    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery time plantUid全--最终
    List<AlarmAnalysis> AlarmAnalysisTimeIntervalQuery(@Param("page") int page, @Param("pageSize") int pageSize,@Param("startTime") String startTime,@Param("endTime")String endTime,@Param("plantUid") List<String> splits);

    List<AlarmAnalysis> AlarmAnalysisTconditionQuery(@Param("page") int page, @Param("pageSize") int pageSize,@Param("startTime") String startTime,@Param("name") String name,@Param("name1") String name1, @Param("sn") String sn);

    List<AlarmAnalysis> AlarmAnalysisPolymerizationAll(@Param("page") String page, @Param("pageSize") String pageSize,@Param("startTime") String startTime,@Param("endTime") String endTime);

    List<AlarmAnalysis> AlarmAnalysisPolymerization(@Param("page") String page, @Param("pageSize") String pageSize,@Param("plantUid") List<String> plantUid);

    List<AlarmAnalysis> AlarmAnalysisHistoryAlarm(@Param("page") String page,@Param("pageSize")  String pageSize,@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("grade")int grade);

    List<AlarmAnalysis> AlarmAnalysisListFormplantUid(@Param("page") int page,@Param("pageSize")  int pageSize,@Param("plantUid") List<String> plantUid);

    List<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll(@Param("startTime") String startTime,@Param("endTime") String endTime, @Param("page") String page,@Param("pageSize")  String pageSize,@Param("plantUid")  String plantUid,@Param("grade")int grade);

    List<AlarmAnalysis> AlarmAnalysisTimeIntervalAll(@Param("time1") String time1,@Param("time2")String time2);

    List<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll3(@Param("page") Integer page, @Param("pageSize") Integer pageSize,@Param("name1") String name1,@Param("startTime") String startTime,@Param("endTime")String endTime,@Param("grade") String grade);

    List<AlarmAnalysis> AlarmAnalysisHistoryAlarmQuann(@Param("page") String page, @Param("pageSize") String pageSize);

    List<AlarmAnalysis> AlarmAnalysisPolymerizationplantUid(@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("plantUid") List<String> list);

    Object totalAlarmStatistics();

    Object totalAlarmStatisticsTime(@Param("time") String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIME(@Param("time") String time,@Param("plantUid") String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMETU(@Param("time")String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgeren(@Param("time")String time,@Param("plantUid") String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenyaoxin(@Param("time")String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennyaoxin(@Param("time")String time,@Param("plantUid") String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEstatistics(@Param("time")String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennstatistics(@Param("time")String time,@Param("plantUid") String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEEquipmentStatistics(@Param("time")String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenEquipmentStatistics(@Param("time")String time,@Param("plantUid") String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMELevelStatistics(@Param("time")String time);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennLevelStatistics(@Param("time")String time,@Param("plantUid") String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenlist(@Param("time")String time,@Param("plantUid") String plantUid);

    List<AlarmAnalysis> totalAlarmStatisticsTIMEyaoxinlistt(@Param("time")String time);

    List<OperatorEvents> OperatorEventData(@Param("page") Integer page,@Param("pageSize")  Integer pageSize,@Param("alarmtime")  String alarmtime,@Param("city")  String city,@Param("endtime")String endtime);

    List<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll2(@Param("page") Integer page,@Param("pageSize")  Integer pageSize,@Param("name1") String name1,@Param("grade") String grade);

    List<AlarmAnalysis> OperatorData(@Param("page") Integer page,@Param("pageSize")  Integer pageSize,@Param("endTime") String endTime,@Param("startTime") String startTime,@Param("name")String name);

    List<AlarmAnalysis> OperatorDataMonth(@Param("page") Integer page,@Param("pageSize")  Integer pageSize,@Param("endTime") String endTime,@Param("startTime") String startTime,@Param("name")String name);

    List<AlarmAnalysis> AlarmByConditionOnListQuery(@Param("startTime") String startTime,@Param("endTime")  String endTime, @Param("plantUidArray") String[] plantUidArray,@Param("alarmMessage")  String alarmMessage, @Param("grade") String grade,@Param("mean")  String mean, @Param("status") String status);

    List<AlarmAnalysis> AlarmByConditionOnAggregation(@Param("startTime") String startTime,@Param("endTime")  String endTime, @Param("plantUidArray") String[] plantUidArray);

    List<HashMap<String, String>>  selectEventMean();
}
