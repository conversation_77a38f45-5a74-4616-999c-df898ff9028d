package com.botong.services.service;

import com.github.pagehelper.PageInfo;
import entityDTO.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import vo.AlarmInfoOnAppConditionVO;
import vo.ChartConditionVO;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 14:25
 */
@Service
public interface UserAppService {
    /**
     * 查询用户APP首页数据
     * @param userUID
     * @return
     */
    IndexPageByUser selectIndexPage(String userUID);


    /**
     * 电站详情信息
     * @param userUID
     * @return
     */
    StationDetailsDTO stationDetails(String userUID);

    /**
     * 逆变器信息列表
     * @param plantIds
     * @return
     */
    List<InverterInfoDTO> inverterInfo(List<String> plantIds);

    /**
     *查询逆变器详情信息：
     *      1.逆变器详细信息
     *      2.光伏组件和电网信息
     * @param inverterSN
     * @return
     */
    HashMap<String, Object> inverterDetails(String inverterSN);

    /**
     * 电站日月年总图表数据信息
     *
     * @param chartConditionVO
     * @return
     */
    <T> T stationChartInfo(ChartConditionVO chartConditionVO);

    /**
     * 运维器信息列表
     * @param userUID
     * @return
     */
    List<OperatorInfoDTO>  operatorInfo(String userUID);
    /**
     * 逆变器实时告警信息列表
     * @param appConditionVO
     * @return
     */
    PageInfo<AppAlarmInfoDTO> inverterRealTimeAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO);
    /**
     * 逆变器历史告警信息列表
     * @param appConditionVO
     * @return
     */
    PageInfo<AppAlarmInfoDTO> inverterHistoryAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO);

    /**
     * 运维器实时告警信息列表
     * @param appConditionVO
     * @return
     */
    PageInfo<OperatorAlarmInfoDTO> operatorRealTimeAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO);

    /**
     * 运维器历史告警信息列表
     * @param appConditionVO
     * @return
     */
    PageInfo<OperatorAlarmInfoDTO> operatorHistoryAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO);

    /**
     * 运维器日月年总图表数据信息
     * @param chartConditionVO
     */
    HashMap<String,Object> operatorChartInfo(ChartConditionVO chartConditionVO);


    /**
     *
     * @param plantId
     * @param plantUID
     */
    OperatorMonitorInfoDTO operatorMonitorInfo(String plantId,String plantUID);
}
