package entityDTO;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 绑定逆变器-光伏板信息列表
 *
 * <AUTHOR>
 * @date 2022-09-21 10:09:49
 */
@Data
public class  BtoDeviceBindingDTO {
    /**
     * 逆变器所属公司的uid
     */
    private String userUid;

    /**
     * 电站id
     */
    private String plantId;

    /**
     * 逆变器SN
     */
    private String deviceSn;

    /**
     * 逆变器厂家
     */
    private String manuFacturer;

    /**
     * 逆变器型号
     */
    private String model;

    /**
     * 逆变器类型
     */
    private String Type;

    /**
     * 该逆变器接入组件功率
     */
    private String componentPower;

    /**
     * 整线
     */
    private Integer special;

    /**
     *逆变器PC码
     */
    private String  devicePc;
    /**
     * 逆变器地址
     */
    private String deviceAddress;
    /**
     * 单块光伏板容量
     */
    private Integer capacity;

    /**
     * DC1组件数量
     */
    private Integer pv1Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv1Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv1Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv1Dc4;

    /**
     * DC1组件数量
     */
    private Integer pv2Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv2Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv2Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv2Dc4;

    /**
     * DC1组件数量
     */
    private Integer pv3Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv3Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv3Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv3Dc4;

    /**
     * DC1组件数量
     */
    private Integer pv4Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv4Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv4Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv4Dc4;

    /**
     * 创建时间
     */
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 系数1
     */
    private Float k1;

    /**
     * 系数2
     */
    private Float k2;

    /**
     * 系数3
     */
    private Float k3;

    /**
     * 系数4
     */
    private Float k4;

    /**
     * 系数5
     */
    private Float k5;

    /**
     * 系数6
     */
    private Float k6;

    private String orentation;

    /**
     *
     */
    private Integer pv5Dc1;

    /**
     *
     */
    private Integer pv5Dc2;

    /**
     *
     */
    private Integer pv5Dc3;

    /**
     *
     */
    private Integer pv5Dc4;

    /**
     *
     */
    private Float k7;

    /**
     *
     */
    private Float k8;

    /**
     *
     */
    private Integer pv6Dc1;

    /**
     *
     */
    private Integer pv6Dc2;

    /**
     *
     */
    private Integer pv6Dc3;

    /**
     *
     */
    private Integer pv6Dc4;

    /**
     *
     */
    private Float k9;

    /**
     *
     */
    private Float k10;

    /**
     *
     */
    private Integer pv7Dc1;

    /**
     *
     */
    private Integer pv7Dc2;

    /**
     *
     */
    private Integer pv7Dc3;

    /**
     *
     */
    private Integer pv7Dc4;

    /**
     *
     */
    private Float k11;

    /**
     *
     */
    private Float k12;

    /**
     *
     */
    private Integer pv8Dc1;

    /**
     *
     */
    private Integer pv8Dc2;

    /**
     *
     */
    private Integer pv8Dc3;

    /**
     *
     */
    private Integer pv8Dc4;

    /**
     *
     */
    private Float k13;

    /**
     *
     */
    private Float k14;

    /**
     *
     */
    private Integer pv9Dc1;

    /**
     *
     */
    private Integer pv9Dc2;

    /**
     *
     */
    private Integer pv9Dc3;

    /**
     *
     */
    private Integer pv9Dc4;

    /**
     *
     */
    private Float k15;

    /**
     *
     */
    private Float k16;

    /**
     *
     */
    private Integer pv10Dc1;

    /**
     *
     */
    private Integer pv10Dc2;

    /**
     *
     */
    private Integer pv10Dc3;

    /**
     *
     */
    private Integer pv10Dc4;

    /**
     *
     */
    private Float k17;

    /**
     *
     */
    private Float k18;

    /**
     *
     */
    private Integer pv11Dc1;

    /**
     *
     */
    private Integer pv11Dc2;

    /**
     *
     */
    private Integer pv11Dc3;

    /**
     *
     */
    private Integer pv11Dc4;

    /**
     *
     */
    private Float k19;

    /**
     *
     */
    private Float k20;

    /**
     *
     */
    private Integer pv12Dc1;

    /**
     *
     */
    private Integer pv12Dc2;

    /**
     *
     */
    private Integer pv12Dc3;

    /**
     *
     */
    private Integer pv12Dc4;

    /**
     *
     */
    private Float k21;

    /**
     *
     */
    private Float k22;
}
