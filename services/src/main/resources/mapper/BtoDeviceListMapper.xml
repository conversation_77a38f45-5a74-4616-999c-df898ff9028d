<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.BtoDeviceListMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoDeviceList" id="btoDeviceListMap">
        <result property="deviceId" column="device_id"/>
        <result property="userUid" column="user_uid"/>
        <result property="plantId" column="plant_id"/>
        <result property="dataloggerSn" column="datalogger_sn"/>
        <result property="deviceSn" column="device_sn"/>
        <result property="manuFacturer" column="manu_facturer"/>
        <result property="model" column="model"/>
        <result property="type" column="type"/>
        <result property="lastUpdateTime" column="last_update_time"/>
        <result property="sysPower" column="sys_power"/>
        <result property="updataDate" column="updata_date"/>
        <result property="tZone" column="t_zone"/>
        <result property="dType" column="d_type"/>
        <result property="eTotal" column="e_total"/>
        <result property="eToday" column="e_today"/>
        <result property="eMonth" column="e_month"/>
        <result property="eYear" column="e_year"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="workStatus" column="work_status"/>
        <result property="deviceEvent" column="device_event"/>
        <result property="devicePc" column="device_pc"/>
        <result property="deviceName" column="device_name"/>
        <result property="iotNum" column="iot_num"/>
        <result property="ccId" column="cc_id"/>
        <result property="moduleSn" column="module_sn"/>
        <result property="cooperationExpireDate" column="cooperation_expire_date"/>
        <result property="cardStatus" column="card_status"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="dateTime" column="dateTime"/>
        <result property="time" column="time"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="cUserName" column="cUserName"/>
        <result property="snName" column="snName"/>
        <result property="power" column="power"/>
        <result property="totalenergy" column="totalenergy"/>
    </resultMap>

    <resultMap type="entityDTO.DeviceDTO" id="DeviceDTOMap">
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="sysPower" column="sysPower"/>
        <result property="sn" column="sn"/>
        <result property="name" column="name"/>
        <result property="time" column="time"/>
        <result property="power" column="Power"/>
        <result property="snName" column="snName"/>
        <result property="sncount" column="sncount"/>
        <result property="userUid" column="userUid"/>
        <result property="plantId" column="plantId"/>
        <result property="model" column="model"/>
        <result property="dataloggersn" column="dataloggersn"/>
        <result property="devicesn" column="devicesn"/>
        <result property="modulesn" column="modulesn"/>
        <result property="cUserName" column="cUserName"/>
    </resultMap>

    <resultMap type="entityDTO.sheBeiJianCe" id="sheBeiJianCeMap">
        <result property="time" column="time"/>
        <result property="snName" column="snName"/>
        <result property="activePower" column="ActivePower"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="fac1" column="fac"/>
        <result property="temperature" column="temperature"/>
        <result property="couser" column="couser"/>
        <result property="pp" column="pp"/>
        <result property="deviceId" column="deviceId"/>
        <result property="devicesn" column="devicesn"/>
    </resultMap>
    <resultMap id="workEfficiencyMap" type="vo.EfficiencyVo">
        <result property="stationName" column="stationName"/>
        <result property="peakPower" column="peakPower"/>
        <result property="plantId" column="plantId"/>
        <result property="realTimePower" column="realTimePower"/>
        <result property="workEfficiency" column="workEfficiency"/>
    </resultMap>

    <resultMap type="entityDTO.TUBIAO" id="tubiao">
        <result property="plantUid" column="plantUid"/>
        <result property="time" column="time"/>
        <result property="power" column="power"/>
    </resultMap>
    <resultMap type="entityDTO.BtoDeviceListDTO" id="BtoDeviceListDTO">
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="devicesn" column="devicesn"/>
        <result property="name" column="name"/>
        <result property="createDate" column="createDate"/>
        <result property="deviceSn" column="deviceSn"/>
    </resultMap>

    <resultMap type="entityDTO.stationNum" id="stationNumDTO">
        <result property="offline" column="offline"/>
        <result property="online" column="online"/>
        <result property="alert" column="alert"/>
        <result property="normal" column="normal"/>
    </resultMap>

    <resultMap type="com.botong.services.vo.InverterVo" id="getInverterByPlantIdMap">
        <result property="plantId" column="plant_id"/>
        <result property="name" column="name1"/>
        <result property="currentPower" column="peak_power"/>
        <result property="sn" column="sn"/>
        <result property="creatTime" column="create_date"/>
    </resultMap>


    <select id="AllStationEnergy" resultMap="BtoDeviceListDTO">
        SELECT sum(today_energy)   todayEnergy
             , SUBSTR(time, 1, 13) a
        FROM bto_device_${dateTime}
        WHERE time LIKE #{time2}
        GROUP BY a
    </select>

    <select id="workEfficiencyRanking" resultMap="workEfficiencyMap">
        SELECT a.plant_id                                  plantId,
               NAME                                        stationName,
               peak_power                                  peakPower,
               sum(power)                                  realTimePower,
               FORMAT((sum(power) / 1000) / peak_power, 4) workEfficiency
        FROM `v_station_list` a
                 LEFT JOIN (
            SELECT plant_id,
                   power
            FROM v_device_list aa
                     LEFT JOIN (SELECT sn, power
                                FROM ${tableName}
                                WHERE time LIKE #{time}
                                GROUP BY SN, time
                                ORDER BY time DESC) bb ON aa.device_sn = bb.sn
            GROUP BY sn
        ) b ON a.plant_id = b.plant_id
        GROUP BY a.plant_id
    </select>

    <select id="DeviceInformation" resultMap="btoDeviceListMap">
        SELECT sn           snName,
               today_energy todayEnergy,
               total_energy totalenergy,
               power        power,
               time         time
        FROM bto_device_${tableNme}
        WHERE time LIKE '${tableNme1}'
          AND sn = '${snName}'
        ORDER BY time DESC
        LIMIT 1;
    </select>
    <select id="DeviceInformationSN" resultMap="btoDeviceListMap">
        SELECT plant_uid plantUid,
               sn        snName
        FROM v1_station_device
        WHERE plant_uid = #{plantUid}
    </select>
    <select id="DeviceInformationTU" resultMap="tubiao">
        SELECT a.plant_uid    plantUid,
               b.power / 1000 power,
               b.time         time
        FROM v1_station_device a
                 LEFT JOIN bto_device_${tableName} b ON a.sn = b.sn
        WHERE b.time like #{time}
          AND plant_uid = #{plantUid}
        order by time asc
    </select>
    <select id="DeviceMonitorUlist" resultType="java.util.HashMap">
        SELECT a.plant_id  plantId,
               a.plant_uid plantUid,
               a.`name`    stationName,
               a.`owner`   userName,
               b.device_sn sn,
               a.user_uid  userUid
        FROM bto_station_list a
                 LEFT JOIN bto_device_list b ON a.plant_id = b.plant_id
        GROUP BY a.plant_id, b.device_sn
    </select>
    <select id="DeviceActivePower" resultMap="sheBeiJianCeMap">
        SELECT sn
             , power ActivePower
             , time  time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tableName2}
          AND sn = #{snName}
    </select>
    <select id="Devicecurrent" resultMap="sheBeiJianCeMap">
        SELECT sn
             , iac1
             , iac2
             , iac3
             , time time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tableName2}
          AND sn = #{snName}
    </select>
    <select id="DeviceVoltage" resultMap="sheBeiJianCeMap">
        SELECT sn
             , vac1
             , vac2
             , vac3
             , time time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tableName2}
          AND sn = #{snName}
    </select>
    <select id="DeviceFrequency" resultMap="sheBeiJianCeMap">
        SELECT sn
             , fac  fac
             , time time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tableName2}
          AND sn = #{snName}
    </select>
    <select id="Devicetemperature" resultMap="sheBeiJianCeMap">
        SELECT sn
             , temperature temperature
             , time        time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tableName2}
          AND sn = #{snName}
    </select>

    <select id="collectionMaxMin" resultMap="sheBeiJianCeMap">
        select 'max(iac1)' couser, iac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableName} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac1 = (
            SELECT max(iac1)
            FROM bto_device_${tableName} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        union
        select 'min(iac1)' couser, iac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac1 = (
            SELECT min(iac1)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
              ##电流1
        union
        select 'max(iac2)' couser, iac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac2 = (
            SELECT max(iac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(iac2)' couser, iac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac2 = (
            SELECT min(iac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电流2
        union
        select 'max(iac3)' couser, iac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac3 = (
            SELECT max(iac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(iac3)' couser, iac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac3 = (
            SELECT min(iac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电流3
        union
        select 'max(vac1)' couser, vac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac1 = (
            SELECT max(vac1)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        union
        select 'min(vac1)' couser, vac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac1 = (
            SELECT min(vac1)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
              ##电压1
        union
        select 'max(vac2)' couser, vac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac2 = (
            SELECT max(vac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(vac2)' couser, vac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac2 = (
            SELECT min(vac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电压2
        union
        select 'max(vac3)' couser, vac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac3 = (
            SELECT max(vac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(vac3)' couser, vac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac3 = (
            SELECT min(vac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电压3
        union
        select 'max(power)' couser, power pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where power = (
            SELECT max(power)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(power)' couser, power pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where power = (
            SELECT min(power)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##功率
        union
        select 'max(fac)' couser, fac pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where fac = (
            SELECT max(fac)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(fac)' couser, fac pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where fac = (
            SELECT min(fac)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##频率
        union
        select 'max(temperature)' couser, temperature pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where temperature = (
            SELECT max(temperature)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(temperature)' couser, temperature pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where temperature = (
            SELECT min(temperature)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        ##温度
    </select>
    <select id="collectioAvg" resultMap="sheBeiJianCeMap">
        SELECT avg(iac1)        iac1,
               avg(iac2)        iac2,
               avg(iac3)        iac3,
               avg(vac1)        vac1,
               avg(vac2)        vac2,
               avg(vac3)        vac3,
               avg(power)       power,
               avg(fac)         fac,
               avg(temperature) temperature
        FROM (
                 SELECT sn
                      , power      #输出功率
                      , iac1
                      , iac2
                      , iac3       #输出电流
                      , vac1
                      , vac2
                      , vac3       #输出电压
                      , fac#频率
                      , temperature#温度
                      , time       #数据时间

                 FROM bto_device_${tablName} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) qw
        ;
    </select>
    <select id="selectListt" resultMap="sheBeiJianCeMap">
        SELECT a.name        name
             , a.plant_id    plantId
             , a.plant_uid
             , a.create_date createDate
             , a.device_id   deviceId
             , a.device_sn   deviceSn
             , b.*
        from v1_base_device a
                 left join (
            SELECT sn, time
            FROM bto_device_${tableName}
            WHERE time LIKE '${Nowtime10}'
               OR time LIKE '${Nowtime20}'
        ) b on a.device_sn = b.sn
    </select>
    <select id="EquipmentMonitoringlike" resultType="entityDTO.sheBeiJianCe">
        SELECT *
        FROM (
                 SELECT a.name        name
                      , a.plant_id    plantId
                      , a.plant_uid
                      , a.create_date createDate
                      , a.device_id
                      , a.device_sn
                      , b.*
                 from v1_base_device a
                          left join (
                     SELECT sn, time
                     FROM bto_device_${tableNme}
                     WHERE time LIKE '${Nowtime10}'
                        OR time LIKE '${Nowtime20}'
                 ) b on a.device_sn = b.sn
             ) aa
        where device_id LIKE '%${deviceId}%'
           or device_sn LIKE '%${devicesn}%'
           OR plant_uid LIKE '%${plantUid}%';
    </select>
    <select id="devicealert" resultMap="stationNumDTO">
        SELECT count(DISTINCT (a.sn)) alert
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${tableName}'
          and status = '0'
        ;
    </select>
    <select id="Deviceonline" resultMap="stationNumDTO">
        SELECT count(distinct (a.sn)) online
        FROM bto_device_${tableNme2} a
        WHERE time LIKE '${tableName}'
    </select>
    <select id="EquipmentMonitoringplantUid" resultType="entityDTO.sheBeiJianCe">

        SELECT a.name        name
             , a.plant_id    plantId
             , a.plant_uid
             , a.create_date createDate
             , a.device_id   deviceId
             , a.device_sn   deviceSn
             , b.*
        from v1_base_device a
                 left join (
            SELECT sn, time
            FROM bto_device_${tableNme}
            WHERE time LIKE '${Nowtime10}'
               OR time LIKE '${Nowtime20}'
        ) b on a.device_sn = b.sn
        where plant_uid = '${plantUid}'
    </select>
    <select id="getInverterByPlantId" parameterType="string" resultMap="getInverterByPlantIdMap">
        SELECT plant_id,
               name1,
               peak_power,
               create_date,
               sn
        FROM v1_station_device
        WHERE plant_id = #{plantId}
    </select>
    <select id="GetBasicInverterInformation" resultMap="DeviceDTOMap">
        SELECT plant_id      plantId,
               model         model,
               datalogger_sn dataloggersn,
               device_sn     devicesn,
               module_sn     modulesn,
               b.c_user_name cUserName

        FROM v_device_list a
                 LEFT JOIN v_user_list b ON a.user_uid = b.c_user_uid
        WHERE device_sn = #{sn}
    </select>
    <select id="InverterOfflineList" resultMap="BtoDeviceListDTO">
        SELECT b.`name`      name,
               b.create_date createDate,
               a.device_sn   deviceSn
        FROM v_device_list a
                 LEFT JOIN v_station_base b ON a.plant_id = b.plant_id
        WHERE device_sn NOT IN (SELECT DISTINCT (sn) FROM bto_device_${time1} WHERE time LIKE '${time3}')
    </select>
    <select id="deviceAlert" resultType="java.lang.Integer">
        SELECT count(
                       DISTINCT (a.sn))
        FROM v_event_mean a
                 LEFT JOIN v_device_list b ON a.sn = b.device_sn
        WHERE start_time LIKE '${curTime}'
          AND `status` = '0'
    </select>
    <select id="deviceOnline" resultType="java.lang.Integer">
        SELECT count(DISTINCT (a.sn)) ONLINE
        FROM bto_device_${tableName} a
                 LEFT JOIN bto_device_list b ON a.sn = b.device_sn
        WHERE left(time, 13) = #{nearTime};
    </select>


</mapper>