package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.VDay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VDayMapper extends BaseMapper<VDay> {
    List<VDay> selectMonth(@Param("month") String month,@Param("plantId") String plantId);

    List<VDay> selectYear(@Param("plantId") String plantId);

    List<VDay> selectYield(@Param("plantId") String plantId, @Param("date") String date, @Param("table") String table);

    List<VDay> selectYieldAll(@Param("plantId") String plantId,@Param("day") String day);

    List<VDay> selectYieldMonth(@Param("plantId") String plantId , @Param("month") String month);

    List<VDay> selectYieldMonthAll(@Param("plantId") String plantId , @Param("month") String month);

    List<VDay> selectYieldYear(@Param("plantId") String plantId , @Param("year") String year);
}
