package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.github.pagehelper.PageInfo;
import entity.BtoStationList;
import entityDTO.*;
import org.springframework.stereotype.Service;
import utils.R;
import vo.StationListVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 电站列表
 *
 * <AUTHOR> @date 2022-08-11 15:36:33
 */
@Service
public interface BtoStationListService extends IService<BtoStationList> {

    Long StaionAllNum();

    Long StationStatus0();

    Long StationStatus1();

    BtoStationList StationAllpeakPower();

    List<BTOstationListDTO> StationAllenergy(String tableNme, String nowtime10, String nowtime20);

    List<BtoStationList> GFmonitoringCenter();

    /**
     * 获取在线电站数
     */

    Map<String, Integer> Stationonline();

    List<StationListVo> getAllStationName();

    /**
     * 根据电站id获取装机容量
     */
    String getInstalledCapacityById(String plantId);

    /**
     * 根据电站id获取电站uid
     */
    String getIdByUId(String plantId);

    PageInfo<AllStationList> ListOfTotalPowerStations(String status, String stationName, String page, String pageSize);

    PageInfo<AllStationList> ListOfTotalPowerStationsstationName(String page, String pageSize, String stationName);

    Object ListOfTotalPowerStationsPhone(String plantUid);

    String getPhoneNumByUid(String userUid);

    Map<String, Double> getStationEnergy();

    /**
     * 电站数量及状态列表
     *
     * @return
     */
    HashMap<String, Integer> StationNum();

    /**
     * 总装机容量&日-月-总-近六月-近七日发电量
     * @return
     */
    HashMap<String, Integer> peakPowerAndstationEnergy();

    //    Object Staionuptime();
//    List<BtoStationList> sqlStaion();
//    BtoStationList getInstalledCapacity(String plantId);
//    List<stationNum> Stationonline();
}

