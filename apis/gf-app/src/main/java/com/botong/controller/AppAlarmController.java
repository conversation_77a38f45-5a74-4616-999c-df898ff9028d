package com.botong.controller;

import com.botong.services.service.UserAppService;
import com.github.pagehelper.PageInfo;
import entityDTO.AppAlarmInfoDTO;
import entityDTO.OperatorAlarmInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;
import utils.HttpStatus;
import vo.AlarmInfoOnAppConditionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 8:59
 */
@RestController
@RequestMapping("alarmInfo")
@Api(tags = "告警信息接口")
public class AppAlarmController {
    @Autowired
    private UserAppService userAppService;

    @ApiOperation("逆变器/运维器-实时/历史告警信息列表")
    @PostMapping("alarmInfo")
    public AjaxResult alarmInfo(@RequestBody AlarmInfoOnAppConditionVO appConditionVO){
        switch (appConditionVO.getType()){
            case "0":
                PageInfo<AppAlarmInfoDTO> inverterRealTimeAlarmInfo = userAppService.inverterRealTimeAlarmInfo(appConditionVO);
                return AjaxResult.success(inverterRealTimeAlarmInfo);
            case "1":
                PageInfo<AppAlarmInfoDTO> inverterHistoryAlarmInfo = userAppService.inverterHistoryAlarmInfo(appConditionVO);
                return AjaxResult.success(inverterHistoryAlarmInfo);
            case "2":
                PageInfo<OperatorAlarmInfoDTO> operatorRealTimeAlarmInfo = userAppService.operatorRealTimeAlarmInfo(appConditionVO);
                return AjaxResult.success(operatorRealTimeAlarmInfo);
            case "3":
                PageInfo<OperatorAlarmInfoDTO> operatorHistoryAlarmInfo = userAppService.operatorHistoryAlarmInfo(appConditionVO);
                return AjaxResult.success(operatorHistoryAlarmInfo);
            default:
        }
        return AjaxResult.error(HttpStatus.BAD_REQUEST,"查询类型输入有误");
    }

}
