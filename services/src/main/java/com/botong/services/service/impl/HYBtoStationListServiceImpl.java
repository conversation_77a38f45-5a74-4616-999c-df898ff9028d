package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoStationListMapper;
import com.botong.services.mapper.HYBtoDeviceListMapper;
import com.botong.services.mapper.HYBtoStationListMapper;
import com.botong.services.service.HYBtoStationListService;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entity.BtoStationList;
import entityDTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import vo.StationListVo;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class HYBtoStationListServiceImpl extends ServiceImpl<HYBtoStationListMapper, BtoStationList> implements HYBtoStationListService {
    @Autowired
    HYBtoStationListMapper hyBtoStationListMapper;
    @Autowired
    private HYBtoDeviceListMapper hyBtoDeviceListMapper;
    @Autowired
    private BtoStationListMapper btoStationListMapper;


    @Override
    @Cacheable(cacheNames = "HY_WEB3M", key = " 'ListOfTotalPowerStations' ", cacheManager = "HY_Web_Cache_3M")
    public PageInfo<AllStationList> ListOfTotalPowerStations() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String curDay = sdf.format(date);
        String curMonth = curDay.substring(0, 7);
        String curYear = curMonth.substring(0, 4);

        List<AllStationList> allStationList = hyBtoStationListMapper.ListOfTotalPowerStationsByName();
        PageInfo<AllStationList> allStationListPageInfo = new PageInfo<>(allStationList);
            return allStationListPageInfo;
        }


    @Override
    @Cacheable(cacheNames = "HY_WEB", key = " 'GFmonitoringCenter' ", cacheManager = "HY_Web_Cache")
    public List<BtoStationList> GFmonitoringCenter() {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("special", "1");
        return baseMapper.selectList(queryWrapper);
    }


    @Override
    public List<StationListVo> getAllStationName() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("plant_id", "name");
        queryWrapper.eq("special", 1);
        ArrayList<StationListVo> stationListVos = new ArrayList<>();
        List<BtoStationList> btoStationLists = baseMapper.selectList(queryWrapper);
        for (BtoStationList btoStationList : btoStationLists) {
            StationListVo stationListVo = new StationListVo();
            stationListVo.setName(btoStationList.getName());
            stationListVo.setPlantId(btoStationList.getPlantId());
            stationListVos.add(stationListVo);
        }
        return stationListVos;
    }

    @Override
    public String getInstalledCapacityById(String plantId) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("peak_power");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper).getPeakPower();
    }

    @Override
    public String getIdByUId(String plantId) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("plant_uid");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper).getPlantUid();
    }


//    @Override
//    public PageInfo<AllStationList> ListOfTotalPowerStationsstationName(String page, String pageSize ) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AllStationList> stationLunBos = hyBtoStationListMapper.ListOfTotalPowerStationsByName();
//        PageInfo<AllStationList> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }

    @Override
    public Object ListOfTotalPowerStationsPhone(String plantUid) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String time = simpleDateFormat.format(date);
        return hyBtoStationListMapper.ListOfTotalPowerStationsPhone(time, plantUid);
    }

    @Override
    @Cacheable(cacheNames = "HY_Web3M", key = "'stationAndDeviceNum'", cacheManager = "HY_Web_Cache_3M")
    public HashMap<String, Integer> stationAndDeviceNum() {
        HashMap<String, Integer> stationAndDeivceNumMap = new HashMap<>();
        //查询总电站数量
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(" special", 1);
        //Long类型的数据需要转换为Integer
        Long allStationNumL = baseMapper.selectCount(queryWrapper);
        Integer allStationNum = allStationNumL.intValue();
        //查询在线电站数量
        Date date = new Date();
        Date near_date = new Date(date.getTime() - 10 * 60 * 1000);
        SimpleDateFormat time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = time.format(near_date);
//        String nearTime = str.substring(0, 15).concat("%");
        //告警时间参数
        String alarmTime = str.substring(0, 10).concat("%");
        String curTime = time.format(date).substring(0, 10).concat("%");
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
//        String tableName = simpleDateFormat.format(near_date);
        //在线电站数量
        Integer onlineStationNum = hyBtoStationListMapper.Stationonline();
        //查询告警电站数量
        Integer alterStationNum = hyBtoStationListMapper.alarmNum(curTime);
        //查询告警运维器数量
//        Integer alarmOperatorNum = hyBtoStationListMapper.alarmOperatorNum();
        //正常电站
        Integer normalStationNum = Math.max(onlineStationNum - alterStationNum, 0);
        //离线电站
        Integer offlineStationNum = allStationNum - onlineStationNum;
        //告警记录数 = 告警逆变器数量+ 告警运维器数量
        Integer alertNum = hyBtoStationListMapper.selectCountAlarm(alarmTime);
//        alertNum += alarmOperatorNum;
        //自检提示数
        Integer stationSelfInspectionNum = hyBtoStationListMapper.selectSelfInspectionNum();
        //逆变器数量
        //获取所有逆变器数量
        QueryWrapper<BtoDeviceList> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.eq("special", 1);
        Long allDeviceNumL = hyBtoDeviceListMapper.selectCount(deviceQueryWrapper);
        Integer allDeviceNum = allDeviceNumL.intValue();
        //获取在线逆变器数量
        Integer onlineDeviceNum = hyBtoDeviceListMapper.deviceOnline();
        //获取告警逆变器数量
        Integer alertDeviceNum = hyBtoDeviceListMapper.deviceAlert(curTime);
        //获取正常逆变器数量
        Integer normalDeviceNum = onlineDeviceNum - alertDeviceNum;
        //获取离线逆变器数量
        Integer offlineDeviceNum = allDeviceNum - onlineDeviceNum;
        //获取自检提示数
        Integer deiviceSelfInspectionNum = hyBtoDeviceListMapper.selectSelfInspectionNum();

        stationAndDeivceNumMap.put("alertNum", alertNum);
        stationAndDeivceNumMap.put("allStationNum", allStationNum);
        stationAndDeivceNumMap.put("onlineStationNum", onlineStationNum);
        stationAndDeivceNumMap.put("alterStationNum", alterStationNum);
        stationAndDeivceNumMap.put("normalStationNum", normalStationNum);
        stationAndDeivceNumMap.put("offlineStationNum", offlineStationNum);
        stationAndDeivceNumMap.put("stationSelfInspectionNum", stationSelfInspectionNum);

        stationAndDeivceNumMap.put("allDeviceNum", allDeviceNum);
        stationAndDeivceNumMap.put("normalDeviceNum", normalDeviceNum);
        stationAndDeivceNumMap.put("offlineDeviceNum", offlineDeviceNum);
        stationAndDeivceNumMap.put("alertDeviceNum", alertDeviceNum);
        stationAndDeivceNumMap.put("onlineDeviceNum", onlineDeviceNum);
        stationAndDeivceNumMap.put("deiviceSelfInspectionNum", deiviceSelfInspectionNum);

        return stationAndDeivceNumMap;
    }
    @Override
    @Cacheable(cacheNames = "HY_Common",key = "'getEnergyAndPeakPower'",cacheManager = "HY_Common_Cache")
    public HashMap<String, Double> stationAllenergyAndAllpeakPower() {
        HashMap<String,Double> energyMap = btoStationListMapper.getElectricity();
        Double allPeakPower = btoStationListMapper.getAllPeakPower();
        energyMap.put("allPeakPower",allPeakPower);
        return energyMap;
    }



    @Override
    @Caching(evict = {@CacheEvict(cacheNames = "HY_WEB3M", key = " 'ListOfTotalPowerStations' ", cacheManager = "HY_Web_Cache_3M"),
            @CacheEvict(cacheNames = "HY_WEB3M", key = " 'stationAndDeviceNum' ", cacheManager = "HY_Web_Cache_3M"),
            @CacheEvict(cacheNames = "HY_APP3M",  allEntries = true, cacheManager = "HY_APP_Cache_3M")
    })
    public String updateStationInfo(UpdateStationInfoForm updateStationInfoForm) {
        if (updateStationInfoForm.getStationName() != null && updateStationInfoForm.getPeakPower()!=null){
            Integer listCount = hyBtoStationListMapper.updateStationBaseInfo(updateStationInfoForm);
            Integer baseCount = hyBtoStationListMapper.updateStationListInfo(updateStationInfoForm);
            String msg = listCount > 0 ? "bto_station_base表修改成功" : "bto_station_base表修改出现异常或数据未发生变动";
            msg += baseCount > 0 ? "bto_station_list表修改成功" : "bto_station_base表修改出现异常或数据未发生变动";
            return msg;
        }else {
            return "取消了修改！";
        }


    }


}
