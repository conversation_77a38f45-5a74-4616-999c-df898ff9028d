package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 9:47
 */
@Data
@ApiModel("电站详情信息实体-APP")
public class StationDetailsDTO implements Serializable {
    @ApiModelProperty(value = "电站Id集合")
    private List<String> plantIds;
//    @ApiModelProperty(value = "电站UID")
//    private String plantUID;
    @ApiModelProperty(value = "电站名称")
    private String plantName;
    @ApiModelProperty(value = "建站时间")
    private String createTime;
    @ApiModelProperty(value = "装机容量")
    private String peakPower;
//    @ApiModelProperty(value = "状态")
//    private String status;
    @ApiModelProperty(value = "当前功率")
    private String currentPower;
    @ApiModelProperty(value = "日发电量")
    private String todayEnergy;
    @ApiModelProperty(value = "月发电量")
    private String monthEnergy;
    @ApiModelProperty(value = "年发电量")
    private String yearEnergy;
    @ApiModelProperty(value = "总发电量")
    private String totalEnergy;
    @ApiModelProperty(value = "当日收益")
    private String todayProfit;
    @ApiModelProperty(value = "总收益")
    private String totolProfit;
    @ApiModelProperty(value = "累计植树(棵)")
    private String totolTree;
    @ApiModelProperty(value = "累计二氧化碳减排(吨)")
    private String totolCo2;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;
    @ApiModelProperty(value = "数据时间")
    private String dateTime;

    @ApiModelProperty(value = "用户联系电话")
    private String phone;
    @ApiModelProperty(value = "电站地址")
    private String address;

    @ApiModelProperty(value = "项目专项")
    private String special;

}
