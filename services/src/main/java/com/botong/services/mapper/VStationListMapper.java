package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.VStationList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VStationListMapper extends BaseMapper<VStationList> {
    List<VStationList> VStationListSelecEta(@Param("plantId") String plantId, @Param("month") String month);


    List<VStationList> VStationListSelecAll(@Param("plantId") String plantId, @Param("year") String year);

}
