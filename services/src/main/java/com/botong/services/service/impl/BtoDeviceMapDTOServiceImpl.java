package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoDeviceMapDTOMapper;
import com.botong.services.service.BtoDeviceMapDTOService;
import entityDTO.BtoDeviceMapDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class BtoDeviceMapDTOServiceImpl extends ServiceImpl<BtoDeviceMapDTOMapper, BtoDeviceMapDTO> implements BtoDeviceMapDTOService {

    @Autowired
    private BtoDeviceMapDTOMapper btoDeviceMapDTOMapper;

    @Override
    public List<BtoDeviceMapDTO> selectBtoDeviceMap(String plantId ) {    //查询日全部的数据

        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String format1 = DateUtil.format(new Date(), "yyyyMMdd");
        String table =format1;
        List<BtoDeviceMapDTO> list = new ArrayList<>();

        for (int i = 0; i < 24; i++) {
            if (i < 10) {
                String b = format + " 0"+ i+ ":%";
                List<String> sns = btoDeviceMapDTOMapper.selectDistinct(b, plantId,table);
                for (String sn:sns) {
                    BtoDeviceMapDTO dtoList = btoDeviceMapDTOMapper.selectBtoDeviceMap(table,b, plantId,sn);
                    if (dtoList == null) {
                        continue;
                    } else {
                        list.add(dtoList);
                    }
                }

            } else {
                String b = format +" "+i+ ":%";
                List<String> sns = btoDeviceMapDTOMapper.selectDistinct(b, plantId,table);
                for (String sn:sns) {
                    BtoDeviceMapDTO dtoList = btoDeviceMapDTOMapper.selectBtoDeviceMap(table,b, plantId, sn);
                    if (dtoList == null) {
                        continue;
                    } else {
                        list.add(dtoList);
                    }
                }
            }

        }

       return list;

    }

    @Override
    public List<BtoDeviceMapDTO> selectBtoDeviceMapDay(String plantId ,String sn, String day) {
        List<BtoDeviceMapDTO> list = new ArrayList<>();
        String[] split = day.split("-");
        String table =split[0] + split[1] + split[2];
        String b = day + "%";
        list = btoDeviceMapDTOMapper.selectBtoDeviceMap2(table,b, plantId,sn);
        return list;
    }


    @Override
    public List<BtoDeviceMapDTO> selectBtoDeviceMapDay1(String plantId ,String sn, String day) {
        String[] split = day.split("-");
        StringBuffer sb = new StringBuffer();
        for(int i = 0; i < split.length; i++){
            sb. append(split[i]);
        }
        String time = sb.toString();     //yyyyMMdd
        String table =time;
        List<BtoDeviceMapDTO> list = new ArrayList<>();

        for (int i = 0; i < 24; i++) {
            if (i < 10) {
                String b = day + " 0" + i + ":%";
                BtoDeviceMapDTO mapDTO = btoDeviceMapDTOMapper.selectBtoDeviceMap(table, b, plantId, sn);
                if (mapDTO == null) {
                    continue;
                } else {
                    list.add(mapDTO);
                }
            } else {
                String b = day + " " + i + ":%";
                BtoDeviceMapDTO mapDTO = btoDeviceMapDTOMapper.selectBtoDeviceMap(table, b, plantId, sn);
                if(mapDTO == null ){
                    continue;
                } else {
                    list.add(mapDTO);
                }
            }
        }
        return  list;
    }

}




