<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.StatisticalReportMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.StatisticalReport" id="StatisticalReport">
        <result property="sn" column="sn"/>
        <result property="time" column="time"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="plantId" column="plantId"/>
        <result property="date" column="date"/>
        <result property="energy" column="energy"/>
    </resultMap>
    <select id="PowerGenerationStatisticsDay" resultMap="StatisticalReport">
        SELECT
            sn sn,
            time time,
            today_energy todayEnergy,
            power
        FROM
           #{shi}
        WHERE
            time LIKE '#{time2}'
          AND sn ='${sn}'
    </select>
    <select id="PowerGenerationStatisticsMonth" resultMap="StatisticalReport">
        SELECT
            plant_id,
            date,
            energy
        FROM
            v_day
        WHERE
            date LIKE '%${date}%'
          AND plant_id = '${sn}'
        ORDER BY
            date
    </select>
    <select id="PowerGenerationStatisticsYear" resultMap="StatisticalReport">
        SELECT
            plant_id,
            LEFT ( date, 7 ) q,
            sum( energy )
        FROM
            v_day
        WHERE
            date LIKE '%${date}%'
          AND plant_id = '${sn}'
        GROUP BY
            q
    </select>
</mapper>