package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYIncomeStatisticMapper;
import com.botong.services.service.HYIncomeStatisticService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.IncomeCondDTO;
import entityDTO.IncomeStatisticDTO;
import entityDTO.IncomeStatisticsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class HYIncomeStatisticServiceImpl extends ServiceImpl<HYIncomeStatisticMapper, IncomeStatisticDTO> implements HYIncomeStatisticService {
    @Autowired
    private HYIncomeStatisticMapper hyincomeStatisticMapper;

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsDay( String plant_id,String time1,String time2,Integer page,Integer pageSize ){
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String[] split = plant_id.split(",");
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        String emm="bto_device_"+time1;
        List<IncomeStatisticDTO> incomeStatisticLunbos=hyincomeStatisticMapper.IncomeStatisticsDay(Arrays.asList(split),emm,time2,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getToday_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;

    }

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsMonth( String plant_id, String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String[] split = plant_id.split(",");
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=hyincomeStatisticMapper.IncomeStatisticsMonth(Arrays.asList(split),time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getMonth_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }

    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsYear( String plant_id, String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String[] split = plant_id.split(",");
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=hyincomeStatisticMapper.IncomeStatisticsYear(Arrays.asList(split),time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getYear_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }
    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsAllDay(  String time1,String time2,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        String emm="bto_device_"+time1;
        List<IncomeStatisticDTO> incomeStatisticLunbos=hyincomeStatisticMapper.IncomeStatisticsAllDay(emm,time2,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getToday_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }
    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsAllMonth(  String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=hyincomeStatisticMapper.IncomeStatisticsAllMonth(time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getMonth_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }
    @Override
    public PageInfo<IncomeStatisticDTO> IncomeStatisticsAllYear(  String time,Integer page,Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<IncomeStatisticDTO> incomeStatisticDTOS=new ArrayList<>();
        List<IncomeStatisticDTO> incomeStatisticLunbos=hyincomeStatisticMapper.IncomeStatisticsAllYear(time,page,pageSize);
        for(IncomeStatisticDTO ce : incomeStatisticDTOS){
            System.out.println(ce.getYear_energy());
        }
        PageInfo<IncomeStatisticDTO> incomeStatisticLunbosInfo=new PageInfo<>(incomeStatisticLunbos);
        return incomeStatisticLunbosInfo;
    }

    @Override
    public PageInfo<IncomeStatisticsDTO> IncomeStatisticsByCondition(IncomeCondDTO incomeCondDTO) {
        Integer page = incomeCondDTO.getPage();
        Integer pageSize = incomeCondDTO.getPageSize();
        String date = incomeCondDTO.getDate();
        //SQL中需要截取的时间格式长度
        int length = date.length();
        String[] plantIds = incomeCondDTO.getPlantIds();
        PageHelper.startPage(page, pageSize);
        List<IncomeStatisticsDTO> incomeStatisticDTOS=hyincomeStatisticMapper.IncomeStatisticsByCondition(plantIds, date, length);
        PageInfo<IncomeStatisticsDTO> incomeStatistic = new PageInfo<>(incomeStatisticDTOS);
        return incomeStatistic;
    }
}
