package entityDTO;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class userEnginerDTO implements Serializable {


    /**
     * 用户id
     */
    private Integer cUserId;

    /**
     * 用户uid
     */
    private String cUserUid;

    /**
     * 用户姓名
     */
    private String cUserName;

    /**
     * 用户电话
     */
    private String cUserTel;

    /**
     * 帐号状态（0：正常，1：已删除）
     */
    private Integer delFlag;

    /**
     * 用户注册日期
     */
    private Date cUserRegtime;

    /**
     * 用户档案更新时间
     */
    private Date updateDate;

    /**
     * 用户注册邮箱
     */
    private String cUserEmail;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private String password;

    /**
     * 河源整线为`1`
     */
    private Integer special;
    /**
     * 所属工程师ID
     */
    private String enginerId;

}
