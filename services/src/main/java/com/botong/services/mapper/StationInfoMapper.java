package com.botong.services.mapper;

import entityDTO.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.ChartConditionVO;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3 14:22
 */
@Mapper
public interface StationInfoMapper {

    /**
     * 查询用户APP-单个用户的电站信息
     *
     * @param userUID
     * @return
     */
    List<StationInfoByUser> selectIndexPage(@Param("userUID") String userUID);


    /**
     * 逆变器信息列表
     *
     * @param plantIds
     * @return
     */
    List<InverterInfoDTO> inverterInfo(@Param("plantIds") List<String> plantIds);

    /**
     * 逆变器详情信息
     *
     * @param inverterSN
     * @return
     */
    InverterDetailsDTO inverterDetails(@Param("inverterSN") String inverterSN);

    /**
     * 查询光伏组件和电网信息
     *
     * @param today
     * @param inverterSN
     * @return
     */
    PvMoudleDTO getPvMoudle(@Param("today") String today, @Param("inverterSN") String inverterSN);

    /**
     * 查询逆变器月年总图表数据信息
     *
     * @param chartConditionVO
     * @return
     */
    List<OtherChartDTO> stationChartInfo(@Param("chartConditionVO") ChartConditionVO chartConditionVO);

    /**
     * 查询逆变器日图表数据信息
     *
     * @param chartConditionVO
     * @param
     * @return
     */
    List<DayChartDTO> stationChartInfoByDay(@Param("chartConditionVO") ChartConditionVO chartConditionVO, @Param("tableSuffix") String tableSuffix);

    /**
     * 查询逆变器日图表数据-汇总信息
     *
     * @param chartConditionVO
     * @return
     */
    List<DayChartDTO> totalStationChartInfoByDay(@Param("chartConditionVO") ChartConditionVO chartConditionVO, @Param("tableSuffix") String tableSuffix);

    /**
     * 运维器信息列表
     *
     * @param userUID
     * @return
     */
    List<OperatorInfoDTO> operatorInfo(@Param("userUID") String userUID);

    /**
     * 逆变器实时告警信息列表
     *
     * @param plantIds
     */
    List<AppAlarmInfoDTO> inverterRealTimeAlarmInfo(@Param("plantIds") List<String> plantIds);

    /**
     * 逆变器历史告警信息列表
     *
     * @param plantIds
     */
    List<AppAlarmInfoDTO> inverterHistoryAlarmInfo(@Param("plantIds") List<String> plantIds);

    /**
     * 运维器实时告警信息列表
     *
     * @param userUID
     */
    List<OperatorAlarmInfoDTO> operatorRealTimeAlarmInfo(@Param("userUID") String userUID);

    /**
     * 运维器历史告警信息列表
     *
     * @param userUID
     */
    List<OperatorAlarmInfoDTO> operatorHistoryAlarmInfo(@Param("userUID") String userUID);
    /**
     * 运维器日月年总图表数据信息
     *
     * @param chartConditionVO
     */
    List<OperatorChartInfoDTO> operatorChartInfo(@Param("chartConditionVO") ChartConditionVO chartConditionVO);

    /**
     * 查询运维器实时监控信息
     * @param plantUID
     * @return
     */
    OperatorMonitorInfoDTO operatorMonitorInfo(@Param("plantUID") String plantUID);

    /**
     * 查询运维器信号量强度信息
     * @param plantId
     * @return
     */
    String selectSemaphore(@Param("plantId") String plantId);

    /**
     * 单个运维器故障信息
     * @param plantId
     * @return
     */
    OperatorAlarmInfoDTO selectOperatorAlarmInfo(@Param("plantId") String plantId);

    /**
     * 查询逆变器光伏输入电路数量
     * @param inverterModel
     * @return
     */
    Integer selectPvInputNumber(@Param("inverterModel")String inverterModel);
}
