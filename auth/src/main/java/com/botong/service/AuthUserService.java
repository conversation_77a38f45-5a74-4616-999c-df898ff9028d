package com.botong.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.services.entity.GUser;
import com.botong.services.service.UserListService;
import com.botong.services.service.UserService;
import entity.BtoUserList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;


@Slf4j
@Component
public class AuthUserService implements UserDetailsService {

    @Autowired
    private UserService userService;

    @Autowired
    private UserListService userListService;

    @Autowired
    private PasswordEncoder passwordEncoder;


    /*@Override
    public UserDetails loadUserByUsername(String cUsername) throws UsernameNotFoundException {
        // 查询数据库操作
        BaseMapper<GUser> baseMapper = userService.getBaseMapper();

        QueryWrapper<GUser> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("c_user_name", cUsername);
        GUser user = baseMapper.selectOne(userQueryWrapper);
        if (StringUtils.isBlank(user.getPassword())||StringUtils.isBlank(user.getCUserName())) {
            throw new UsernameNotFoundException("the user is not found");
        }
        String password = passwordEncoder.encode(user.getPassword());
        return new User(cUsername, password, new ArrayList<>());
    }*/

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 查询数据库操作
        BaseMapper<BtoUserList> baseMapper = userListService.getBaseMapper();
        QueryWrapper<BtoUserList> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("c_user_name", username);
        BtoUserList user = baseMapper.selectOne(userQueryWrapper);
        if (StringUtils.isBlank(user.getPassword())||StringUtils.isBlank(user.getCUserName())) {
            throw new UsernameNotFoundException("the user is not found");
        }
        String password = passwordEncoder.encode(user.getPassword());
        return new User(username, password, new ArrayList<>());
    }
}


