package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.VStationBaseDTOMapper;
import com.botong.services.mapper.VStationBaseMapper;
import com.botong.services.service.VStationBaseDTOService;
import com.botong.services.service.VStationBaseService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.VStationBase;
import entityDTO.VStationBaseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.rmi.CORBA.Util;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class VStationBaseDTOServiceImpl extends ServiceImpl<VStationBaseDTOMapper, VStationBaseDTO> implements VStationBaseDTOService {

    @Autowired
    private VStationBaseDTOMapper vStationBaseDTOMapper;

    @Override
    public PageInfo<VStationBaseDTO> selectVStationBaseDTO(Integer page,Integer pageSize,String time1){
        String[] split = time1.split("-");
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            buffer.append(split[i]);
        }
        String s = buffer.toString();
        PageHelper.startPage(page,pageSize);
        String table = "bto_device_"+s;
        String time = time1 + "%";
        List<VStationBaseDTO> dtos = vStationBaseDTOMapper.selectStationBase(time, table);
        PageInfo<VStationBaseDTO> info = new PageInfo<>(dtos);
        return info;
    }


    @Override
    public List<VStationBaseDTO> selectStationBaseSign(String time1){
      //  String format = DateUtil.format(new Date(), "yyyyMMdd");
        String[] split = time1.split("-");
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            buffer.append(split[i]);
        }
        String s = buffer.toString();
        String table = "bto_device_"+s;
        String time = time1 + "%";
        List<VStationBaseDTO> dtos = vStationBaseDTOMapper.selectStationBaseSign(time, table);
        return dtos;
    }

    @Override
    public List<VStationBaseDTO> selectStationBasePlantId(String time1,String[] plantIds){
        String[] split = time1.split("-");
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            buffer.append(split[i]);
        }
        String s = buffer.toString();
        String table = "bto_device_"+s;
        String time = time1 + "%";

        ArrayList<VStationBaseDTO> list = new ArrayList<>();
        for (String plantId:plantIds) {
          //  String sub = StrUtil.sub(plantId, 1, -1);
            VStationBaseDTO dtos = vStationBaseDTOMapper.selectStationBasePlantId(time,table,plantId);
            list.add(dtos);
        }
        return list;


      /*  ArrayList<VStationBaseDTO> list = new ArrayList<>();
        for (int i = 0; i < strings.length; i++) {
            System.out.println(strings[i]);
            VStationBaseDTO dtos = vStationBaseDTOMapper.selectStationBasePlantId(time,table,strings[i]);
            list.add(dtos);
        }
        return list;*/
    }



    @Override
    public PageInfo<VStationBaseDTO> selectVStationBaseDTOSign(Integer page, Integer pageSize, String time1 ,  String  Param ,String sort){
    //    String format = DateUtil.format(new Date(), "yyyyMMdd");
        String[] split = time1.split("-");
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            buffer.append(split[i]);
        }
        String s = buffer.toString();
        PageHelper.startPage(page,pageSize);
        if (sort == null) {
            String px = "order by " + Param;
            String table = "bto_device_" + s;
            String time = time1 + "%";
            List<VStationBaseDTO> dtos = vStationBaseDTOMapper.selectStationBasePx(time, table, px, sort);
            PageInfo<VStationBaseDTO> info = new PageInfo<>(dtos);
            return info;
        }else {
            String px = "order by " + Param + " desc";
            String table = "bto_device_" + s;
            String time = time1 + "%" ;
            List<VStationBaseDTO> dtos = vStationBaseDTOMapper.selectStationBasePx(time, table, px, sort);
            PageInfo<VStationBaseDTO> info = new PageInfo<>(dtos);
            return info;
        }
    }

    @Override
    public String updateVStationBase(String plantId){
        vStationBaseDTOMapper.updateStation0();
        vStationBaseDTOMapper.updateStation1(plantId);
        return "标杆成功！";
    }

    @Override
    public PageInfo<VStationBaseDTO> VStationBaseDTOPx2(Integer page, Integer pageSize, String time, String param, String sort) {
        PageHelper.startPage(page,pageSize);
        //判断是正序还是倒叙
        if (sort.equals("0")){
            List<VStationBaseDTO> dtos =vStationBaseDTOMapper.VStationBaseDTOPx2(page,pageSize,time,param,"ASC");
            PageInfo<VStationBaseDTO> info = new PageInfo<>(dtos);
            return info;
        }else {
            List<VStationBaseDTO> dtos =vStationBaseDTOMapper.VStationBaseDTOPx2(page,pageSize,time,param,"DESC");
            PageInfo<VStationBaseDTO> info = new PageInfo<>(dtos);
            return info;
        }
    }

    @Override
    public Object selectVStationBaseDTOPx2XcCount() {
        return vStationBaseDTOMapper.selectVStationBaseDTOPx2XcCount();
    }
}
