package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.BtoUserList;
import entityDTO.Resource;
import entityDTO.Role;
import entityDTO.roleresoure;

import javax.management.relation.RoleResult;
import java.util.List;

public interface BtoUserService extends IService<BtoUserList> {

    //获取用户列表
    List<BtoUserList> test();

    PageInfo<BtoUserList> BTOuserList(Integer page, Integer pagesize);

    PageInfo<BtoUserList> BTOuserListLike( String userName,String page,String pagesize);

    Object UserRoleQuery(String userUid);

    List<roleresoure> getPermissionId(String userUid);

    List<roleresoure> getroleResourceId(int roleId);

    List<Resource> listResource();

    List<Role> listRole();

    Object returnPrimaryId(String plantId, String plantUid);

    int updatePassword(String userUid, String password);

}
