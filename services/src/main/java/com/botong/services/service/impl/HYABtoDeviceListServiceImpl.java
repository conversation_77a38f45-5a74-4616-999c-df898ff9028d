package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYABtoDeviceListMapper;
import com.botong.services.service.HYABtoDeviceService;
import com.botong.services.vo.InverterVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entityDTO.BtoDeviceListDTO;
import entityDTO.TUBIAO;
import entityDTO.sheBeiJianCe;
import entityDTO.stationNum;
import org.springframework.stereotype.Service;
import vo.EfficiencyVo;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class HYABtoDeviceListServiceImpl extends ServiceImpl<HYABtoDeviceListMapper, BtoDeviceList> implements com.botong.services.service.HYABtoDeviceListService {
    @Resource
    private HYABtoDeviceListMapper hyaBtoDeviceListMapper;

    @Resource
    private HYABtoDeviceService hyaBtoDeviceService;


    @Override
    public Long DeviceAllNum() {
        return baseMapper.selectCount(null);
    }

    @Override
    public Long DeviceStatus0() {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_status", "0");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Long DeviceStatus1() {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_status", "1");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<stationNum> DeviceStatus2() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String tableName = simpleDateFormat.format(date) + "%";
        return hyaBtoDeviceListMapper.devicealert(tableName);
    }

    @Override
    public double Deviceuptime() {
        Long DeviceStatus1 = DeviceStatus1();
        Long online = DeviceAllNum() - DeviceStatus0();
        double Staionuptime = online / DeviceStatus1;
        return Staionuptime;
    }

    @Override
    public Long DeviceStatus3() {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_status", "3");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<BtoDeviceListDTO> AllStationEnergy() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String DateTime = simpleDateFormat.format(date);
        String Time2 = simpleDateFormat2.format(date) + "%";
        List<BtoDeviceListDTO> list = hyaBtoDeviceListMapper.AllStationEnergy(DateTime, Time2);
        for (int i = 0; i < list.size() - 1; i++) {
            list.get(i).setTodayEnergy(list.get(i + 1).getTodayEnergy() - list.get(i).getTodayEnergy());
        }
        return hyaBtoDeviceListMapper.AllStationEnergy(DateTime, Time2);
    }

    @Override
    public List<BtoDeviceList> DeviceInformation(String plantUid) {
        Date date = new Date();
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat simpleDateFormat20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        //表名
        String tableNme = simpleDateFormat.format(date);
        //截取当前时间前10分钟
        String Nowtime10 = (simpleDateFormat10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (simpleDateFormat20.format(now20)).substring(0, 15) + "%";
        return hyaBtoDeviceListMapper.DeviceInformation(plantUid, tableNme, Nowtime10, Nowtime20);
    }

    @Override
    public List<BtoDeviceList> DeviceInformationSN(String plantUid) {
        return hyaBtoDeviceListMapper.DeviceInformationSN(plantUid);
    }

    @Override
    public List<TUBIAO> DeviceInformationTU(String plantUid) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat time = new SimpleDateFormat("yyyy-MM-dd");
        //表名
        String tableNme = simpleDateFormat.format(date);
        String tabletime2 = time.format(date) + "%";
        return hyaBtoDeviceListMapper.DeviceInformationTU(plantUid, tableNme, tabletime2);
    }

    @Override
    public List<BtoDeviceList> DeviceMonitorUlist() {
        return hyaBtoDeviceListMapper.DeviceMonitorUlist();
    }

    @Override
    public List<sheBeiJianCe> DeviceActivePower(String snName, String tableNme, String tabletime2) {
        return hyaBtoDeviceListMapper.DeviceActivePower(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> Devicecurrent(String snName, String tableNme, String tabletime2) {
        return hyaBtoDeviceListMapper.Devicecurrent(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> DeviceVoltage(String snName, String tableNme, String tabletime2) {
        return hyaBtoDeviceListMapper.DeviceVoltage(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> DeviceFrequency(String snName, String tableNme, String tabletime2) {
        return hyaBtoDeviceListMapper.DeviceFrequency(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> Devicetemperature(String snName, String tableNme, String tabletime2) {
        return hyaBtoDeviceListMapper.Devicetemperature(snName, tableNme, tabletime2);
    }

    @Override
    public List<EfficiencyVo> workEfficiencyRanking(String pageNum) {

        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        tableName += format.format(date);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time = dateFormat.format(date);
        time += "%";
        return baseMapper.workEfficiencyRanking(tableName, time);
    }


    @Override
    public List<sheBeiJianCe> collectionMaxMin(String snName, String tableNme, String tabletime2) {
        return hyaBtoDeviceListMapper.collectionMaxMin(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> collectioAvg(String snName, String tableNme, String tabletime2) {
        return hyaBtoDeviceListMapper.collectioAvg(snName, tableNme, tabletime2);
    }

    @Override
    public Object EquipmentMonitoring(String page, String pageSize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableNme = simpleDateFormat.format(date);
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat dateFormattime10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormattime20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //截取当前时间前10分钟
        String Nowtime10 = (dateFormattime10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (dateFormattime20.format(now20)).substring(0, 15) + "%";
        List<sheBeiJianCe> stationLunBos = hyaBtoDeviceListMapper.selectListt(page, pageSize, tableNme, Nowtime10, Nowtime20);
        PageInfo<sheBeiJianCe> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public Object EquipmentMonitoringlike(String devicesn, String deviceId, String plantUid) {
        Date date = new Date();
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String tableNme = time.format(date);
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat dateFormattime10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormattime20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //截取当前时间前10分钟
        String Nowtime10 = (dateFormattime10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (dateFormattime20.format(now20)).substring(0, 15) + "%";
        List<sheBeiJianCe> ceList = hyaBtoDeviceListMapper.EquipmentMonitoringlike(devicesn, deviceId, tableNme, Nowtime20, Nowtime10, plantUid);
        return ceList;
    }

    @Override
    public List<BtoDeviceList> getUserInverterNum(String plantId) {
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("datalogger_sn", "device_status");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<stationNum> Deviceonline() {
        Date date = new Date();
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String tableNme2 = time.format(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String tableName = simpleDateFormat.format(date) + "%";
        List<stationNum> KONG = hyaBtoDeviceListMapper.Deviceonline(tableName, tableNme2);
        if (hyaBtoDeviceListMapper.Deviceonline(tableName, tableNme2) == null) {
            return KONG;
        }
        return hyaBtoDeviceListMapper.Deviceonline(tableName, tableNme2);
    }

    @Override
    public Object EquipmentMonitoringplantUid(String page, String pageSize, String plantUid) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableNme = simpleDateFormat.format(date);
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat dateFormattime10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormattime20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //截取当前时间前10分钟
        String Nowtime10 = (dateFormattime10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (dateFormattime20.format(now20)).substring(0, 15) + "%";
        List<sheBeiJianCe> stationLunBos = hyaBtoDeviceListMapper.EquipmentMonitoringplantUid(page, pageSize, tableNme, Nowtime10, Nowtime20, plantUid);
        PageInfo<sheBeiJianCe> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public List<String> getDeviceIdsByPlantId(String plantId) {
        ArrayList<String> deviceIds = new ArrayList<>();
        QueryWrapper<BtoDeviceList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("device_sn");
        queryWrapper.eq("plant_id", plantId);
        for (BtoDeviceList btoDeviceList : baseMapper.selectList(queryWrapper)) {
            deviceIds.add(btoDeviceList.getDeviceSn());
        }
        return deviceIds;
    }

    @Override
    public List<InverterVo> getInverterByPlantId(String plantId) {
        List<InverterVo> inverterByPlantId = baseMapper.getInverterByPlantId(plantId);
        for (InverterVo inverterVo : inverterByPlantId) {
            //根据sn获取发电量
            inverterVo.setBtoDevices(hyaBtoDeviceService.getElectricityBySn(inverterVo.getSn()));
        }
        return inverterByPlantId;
    }

    @Override
    public Object getInverterDetails(String sn) {

        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        tableName += dateFormat.format(date);
        String time = format.format(date);

        return hyaBtoDeviceService.getInverterDetails(sn, tableName, time += "%");
    }

    @Override
    public Object GetBasicInverterInformation(String sn) {
        return hyaBtoDeviceListMapper.GetBasicInverterInformation(sn);
    }

    @Override
    public PageInfo<BtoDeviceList> InverterOfflineList(String page, String pageSize, String time1, String time2) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        String time3=time2+"%";
        List<BtoDeviceList> list= hyaBtoDeviceListMapper.InverterOfflineList(page,pageSize,time1,time3);
        PageInfo<BtoDeviceList> stationLunBoPageInfo = new PageInfo<>(list);
        return stationLunBoPageInfo;
    }

}
