
package com.botong.controller;



import com.botong.services.service.IncomeStatisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@CrossOrigin
@RestController
@Api(tags = "收益统计")
@RequestMapping("IncomeStatistic")
public class IncomeStatisticController {
    @Autowired
    private IncomeStatisticService incomeStatisticService;

    @ApiOperation("收益统计——日（单个或多个）")
    @GetMapping("IncomeStatisticsDay")
    public AjaxResult IncomeStatisticsDay(String plant_id,String time1,String time2,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",incomeStatisticService.IncomeStatisticsDay(plant_id,time1,time2,page,pageSize));
    }

    @ApiOperation("收益统计——月（单个或多个）")
    @GetMapping("IncomeStatisticsMonth")
    public AjaxResult IncomeStatisticsMonth( String plant_id, String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",incomeStatisticService.IncomeStatisticsMonth(plant_id,time,page,pageSize));
    }

    @ApiOperation("收益统计——年（单个或多个）")
    @GetMapping("IncomeStatisticsYear")
    public AjaxResult IncomeStatisticsYear( String plant_id, String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",incomeStatisticService.IncomeStatisticsYear(plant_id,time,page,pageSize));
    }
    @ApiOperation("收益统计——日（全部）")
    @GetMapping("IncomeStatisticsAllDay")
    public AjaxResult IncomeStatisticsAllDay( String time1,String time2,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",incomeStatisticService.IncomeStatisticsAllDay(time1,time2,page,pageSize));
    }

    @ApiOperation("收益统计——月（全部）")
    @GetMapping("IncomeStatisticsAllMonth")
    public AjaxResult IncomeStatisticsAllMonth(  String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",incomeStatisticService.IncomeStatisticsAllMonth(time,page,pageSize));
    }

    @ApiOperation("收益统计——年（全部）")
    @GetMapping("IncomeStatisticsAllYear")
    public AjaxResult IncomeStatisticsAllYear(  String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",incomeStatisticService.IncomeStatisticsAllYear(time,page,pageSize));
    }
}
