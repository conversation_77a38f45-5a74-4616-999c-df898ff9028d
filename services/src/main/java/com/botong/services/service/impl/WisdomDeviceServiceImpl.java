package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.WisdomDeviceMapper;
import com.botong.services.service.WisdomDeviceService;
import entityDTO.WisdomDeviceDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class WisdomDeviceServiceImpl extends ServiceImpl<WisdomDeviceMapper, WisdomDeviceDTO> implements WisdomDeviceService {
    @Autowired
    private WisdomDeviceMapper wisdomDeviceMapper;
    @Override
    public HashMap<String, String> WisdomDeviceInformation(String plantId) {
        HashMap<String, String> WisdomDeviceInformation = wisdomDeviceMapper.WisdomDeviceInformation(plantId);
        HashMap<String, String> RssiMap = wisdomDeviceMapper.selectRssi(plantId);
        WisdomDeviceInformation.put("Rssi", RssiMap.get("Rssi"));
        WisdomDeviceInformation.put("RssiTime", RssiMap.get("RssiTime"));
        return WisdomDeviceInformation;
    }

    @Override
    public List<WisdomDeviceDTO> wisdomDeviceAlarmInformation(String imei) {
        return wisdomDeviceMapper.wisdomDeviceAlarmInformation(imei);
    }


}
