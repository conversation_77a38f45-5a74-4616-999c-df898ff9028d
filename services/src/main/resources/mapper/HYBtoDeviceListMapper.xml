<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYBtoDeviceListMapper">

    <resultMap type="entityDTO.stationNum" id="stationNumDTO">
        <result property="offline" column="offline"/>
        <result property="online" column="online"/>
        <result property="alert" column="alert"/>
        <result property="normal" column="normal"/>
    </resultMap>

    <resultMap id="workEfficiencyMap" type="vo.EfficiencyVo">
        <result property="stationName" column="stationName"/>
        <result property="peakPower" column="peakPower"/>
        <result property="plantId" column="plantId"/>
        <result property="realTimePower" column="realTimePower"/>
        <result property="workEfficiency" column="workEfficiency"/>
    </resultMap>

    <resultMap type="entityDTO.DeviceDTO" id="DeviceDTOMap">
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="sysPower" column="sysPower"/>
        <result property="sn" column="sn"/>
        <result property="name" column="name"/>
        <result property="time" column="time"/>
        <result property="power" column="Power"/>
        <result property="snName" column="snName"/>
        <result property="sncount" column="sncount"/>
        <result property="userUid" column="userUid"/>
        <result property="plantId" column="plantId"/>
        <result property="model" column="model"/>
        <result property="dataloggersn" column="dataloggersn"/>
        <result property="devicesn" column="devicesn"/>
        <result property="modulesn" column="modulesn"/>
        <result property="cUserName" column="cUserName"/>
    </resultMap>
    <resultMap type="entityDTO.DeviceMonitorUListDTO" id="DeviceMonitorUListDTOMap">
        <result property="sn" column="sn"/>
        <result property="plantId" column="plantId"/>
        <result property="plantUid" column="plantUid"/>
        <result property="plantName" column="plantName"/>
        <result property="userUid" column="userUid"/>
        <result property="userName" column="userName"/>
    </resultMap>

    <resultMap type="entityDTO.TUBIAO" id="tubiao">
        <result property="plantUid" column="plantUid"/>
        <result property="time" column="time"/>
        <result property="power" column="power"/>
    </resultMap>

    <resultMap type="entityDTO.sheBeiJianCe" id="sheBeiJianCeMap">
        <result property="time" column="time"/>
        <result property="snName" column="snName"/>
        <result property="activePower" column="power"/>
        <result property="power1" column="power1"/>
        <result property="power2" column="power2"/>
        <result property="power3" column="power3"/>
        <result property="power4" column="power4"/>
        <result property="power5" column="power5"/>
        <result property="power6" column="power6"/>
        <result property="power7" column="power7"/>
        <result property="power8" column="power8"/>
        <result property="power9" column="power9"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="iac4" column="iac4"/>
        <result property="iac5" column="iac5"/>
        <result property="iac6" column="iac6"/>
        <result property="iac7" column="iac7"/>
        <result property="iac8" column="iac8"/>
        <result property="iac9" column="iac9"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="vac4" column="vac4"/>
        <result property="vac5" column="vac5"/>
        <result property="vac6" column="vac6"/>
        <result property="vac7" column="vac7"/>
        <result property="vac8" column="vac8"/>
        <result property="vac9" column="vac9"/>
        <result property="fac1" column="fac"/>
        <result property="fac2" column="fac1"/>
        <result property="fac3" column="fac2"/>
        <result property="fac4" column="fac3"/>
        <result property="fac5" column="fac4"/>
        <result property="fac6" column="fac5"/>
        <result property="fac7" column="fac6"/>
        <result property="fac8" column="fac7"/>
        <result property="fac9" column="fac8"/>
        <result property="temperature" column="temperature"/>
        <result property="couser" column="couser"/>
        <result property="pp" column="pp"/>
        <result property="deviceId" column="deviceId"/>
        <result property="devicesn" column="devicesn"/>
    </resultMap>

    <select id="deviceAlert" resultType="java.lang.Integer">
        SELECT count(DISTINCT (device_sn))
        FROM `bto_device_list`
        WHERE special = '1'
          AND device_status = '2'
    </select>


    <select id="deviceOnline" resultType="java.lang.Integer">
        SELECT count(DISTINCT (device_sn))
        FROM bto_device_list
        WHERE special = '1'
          AND device_status IN (1, 2, 3);
    </select>
    <select id="workEfficiencyRanking" resultMap="workEfficiencyMap">
        SELECT a.plant_id     plantId,
               a.`name1`      stationName,
               a.peak_power   peakPower,
               CASE
                   WHEN b.update_time > DATE_SUB(now(), INTERVAL 30 MINUTE) THEN sum(DISTINCT (b.power))
                   ELSE 0
                   END        realTimePower,
               CASE
                   WHEN b.update_time > DATE_SUB(now(), INTERVAL 30 MINUTE)
                       THEN (sum(DISTINCT (b.power)) / (a.peak_power * 1000))
                   ELSE 0 END workEfficiency
        FROM v1_station_device a
                 LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
        GROUP BY a.plant_id
        ORDER BY a.create_date DESC
    </select>
    <select id="DeviceInformation" resultMap="DeviceDTOMap">
        SELECT a.plant_uid,
               name           name,
               b.device_sn    sn,
               update_time    time,
               b.power        power,
               b.today_energy todayEnergy,
               b.total_energy totalEnergy
        FROM v1_station_device a
                 LEFT JOIN bto_device_total b ON a.device_sn = b.device_sn
        WHERE plant_uid = #{plantUid}
        GROUP BY b.device_sn;
    </select>
    <select id="DeviceInformationSN" resultMap="DeviceDTOMap">
        SELECT plant_uid plantUid,
               sn        snName
        FROM v1_station_device
        WHERE plant_uid = #{plantUid}
    </select>
    <select id="DeviceInformationTU" resultMap="tubiao">
        SELECT a.plant_uid                      plantUid,
               sum(b.power) / 1000              power,
               CONCAT(LEFT(b.time, 15), '0:00') time
        FROM v1_station_device a
                 LEFT JOIN bto_device_${tableName} b ON a.sn = b.sn
        WHERE LEFT(b.time, 10) = CURDATE()
          AND plant_uid = #{plantUid}
        GROUP BY a.plant_uid, time
        ORDER BY b.time;
    </select>
    <select id="DeviceMonitorUlist" resultMap="DeviceMonitorUListDTOMap">
        SELECT a.`owner`   userName,
               a.plant_id  plantId,
               a.plant_uid plantUid,
               a.NAME      plantName,
               b.device_sn sn,
               a.user_uid  userUid
        FROM v_station_list a
                 LEFT JOIN v_device_list b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
        ORDER BY create_date DESC
    </select>
    <select id="collectionMaxMin" resultMap="sheBeiJianCeMap">
        select 'max(iac1)' couser, iac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac1 = (
            SELECT max(iac1)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        union
        select 'min(iac1)' couser, iac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac1 = (
            SELECT min(iac1)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
              ##电流1
        union
        select 'max(iac2)' couser, iac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac2 = (
            SELECT max(iac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(iac2)' couser, iac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac2 = (
            SELECT min(iac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电流2
        union
        select 'max(iac3)' couser, iac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac3 = (
            SELECT max(iac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(iac3)' couser, iac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where iac3 = (
            SELECT min(iac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电流3
        union
        select 'max(vac1)' couser, vac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac1 = (
            SELECT max(vac1)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        union
        select 'min(vac1)' couser, vac1 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac1 = (
            SELECT min(vac1)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
              ##电压1
        union
        select 'max(vac2)' couser, vac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac2 = (
            SELECT max(vac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(vac2)' couser, vac2 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac2 = (
            SELECT min(vac2)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电压2
        union
        select 'max(vac3)' couser, vac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac3 = (
            SELECT max(vac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(vac3)' couser, vac3 pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where vac3 = (
            SELECT min(vac3)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##电压3
        union
        select 'max(power)' couser, power pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where power = (
            SELECT max(power)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(power)' couser, power pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where power = (
            SELECT min(power)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##功率
        union
        select 'max(fac)' couser, fac pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where fac = (
            SELECT max(fac)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(fac)' couser, fac pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where fac = (
            SELECT min(fac)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
                 ##频率
        union
        select 'max(temperature)' couser, temperature pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where temperature = (
            SELECT max(temperature)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        union
        select 'min(temperature)' couser, temperature pp, time
        from (
                 select *
                 FROM bto_device_${tableNme} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) a
        where temperature = (
            SELECT min(temperature)
            FROM bto_device_${tableNme} #传入电量日表
            WHERE time LIKE '${tabletime2}'
              AND sn = '${snName}')
        group by pp
        ##温度
    </select>
    <select id="collectioAvg" resultMap="sheBeiJianCeMap">
        SELECT avg(iac1)        iac1,
               avg(iac2)        iac2,
               avg(iac3)        iac3,
               avg(vac1)        vac1,
               avg(vac2)        vac2,
               avg(vac3)        vac3,
               avg(power)       power,
               avg(fac)         fac,
               avg(temperature) temperature
        FROM (
                 SELECT sn
                      , power      #输出功率
                      , iac1
                      , iac2
                      , iac3       #输出电流
                      , vac1
                      , vac2
                      , vac3       #输出电压
                      , fac#频率
                      , temperature#温度
                      , time       #数据时间

                 FROM bto_device_${tableName} #传入电量日表
                 WHERE time LIKE '${tabletime2}'
                   AND sn = '${snName}'#传入电站SN
             ) qw
        ;
    </select>
    <select id="Devicecurrent" resultMap="sheBeiJianCeMap">
        SELECT sn,
               iac1,
               iac2,
               iac3,
               iac4,
               iac5,
               iac6,
               iac7,
               iac8,
               iac9,
               time time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tabletime2}
          AND sn = #{snName}
    </select>
    <select id="EquipmentMonitoringplantUid" resultType="java.util.HashMap">
        SELECT a.plant_id                                          plantId,
               a.plant_uid                                         plantUid,
               `name`                                              stationName,
               address1                                            stationAddress,
               orentation,
               `status`,
               b.device_sn                                         sn,
               a.create_time                                       createTime,
               b.update_time                                       updateTime,
               TIMESTAMPDIFF(SECOND, a.create_time, b.update_time) betweenTimeMs
        FROM bto_station_list a
                 LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
          AND a.plant_id = #{plantId}
    </select>
    <select id="DeviceVoltage" resultMap="sheBeiJianCeMap">
        SELECT sn,
               vac1,
               vac2,
               vac3,
               vac4,
               vac5,
               vac6,
               vac7,
               vac8,
               vac9,
               time time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tableName2}
          AND sn = #{snName}
    </select>

    <select id="DeviceFrequency" resultMap="sheBeiJianCeMap">
        SELECT sn,
               fac,
               fac1,
               fac2,
               fac3,
               fac4,
               fac5,
               fac6,
               fac7,
               fac8,
               time time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tabletime2}
          AND sn = #{snName}
    </select>
    <select id="Devicetemperature" resultMap="sheBeiJianCeMap">
        SELECT sn
             , temperature temperature
             , time        time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tabletime2}
          AND sn = #{snName}
    </select>
    <select id="DeviceActivePower" resultMap="sheBeiJianCeMap">
        SELECT sn,
               ipv1 * vpv1 power1,
               ipv2 * vpv2 power2,
               ipv3 * vpv3 power3,
               ipv4 * vpv4 power4,
               ipv5 * vpv5 power5,
               ipv6 * vpv6 power6,
               ipv7 * vpv7 power7,
               ipv8 * vpv8 power8,
               ipv9 * vpv9 power9,
               power,
               time        time
        FROM bto_device_${tableName}
        WHERE time LIKE #{tableName2}
          AND sn = #{snName}
    </select>
    <select id="deviceDetail" resultType="java.util.HashMap">
        SELECT a.wisdom_device_sn imei,
               b.device_sn        SN,
               b.manu_facturer    manuFacturer,
               b.model,
               a.display,
               a.`master`,
               a.`slave`,
               a.time
        FROM bto_device_list b
                 LEFT JOIN
             bto_version a
             ON a.sn = b.device_sn
        WHERE b.device_sn = #{sn}
    </select>
    <select id="selectSnByPlantId" resultType="java.lang.String">
        select device_sn
        from bto_device_list
        where plant_id in
        <foreach collection="plantId" item="plantId" separator="," open="(" close=")">
            #{plantId}
        </foreach>
    </select>
    <select id="selectSelfInspectionNum" resultType="java.lang.Integer">
        SELECT count(DISTINCT (device_sn))
        FROM `bto_device_list`
        WHERE special = '1'
          AND device_status = '3'
    </select>
    <select id="totalEnergyAndPower" resultType="java.util.HashMap">
        SELECT a.plant_uid                      plantUid,
               a.plant_id                       plantId,
               SUM(b.power)                     actualPower,
               SUM(b.today_energy)              todayEnergy,
               SUM(b.month_energy)              monthEnergy,
               SUM(b.year_energy)               yearEnergy,
               SUM(b.total_energy)              totalEnergy,
               CONCAT(LEFT(b.time, 15), '0:00') dateTime
        FROM v1_station_device a
                 LEFT JOIN bto_device_${tableName} b ON a.sn = b.sn
        WHERE LEFT(b.time, 10) = #{date}
          AND plant_id = #{plantId}
        GROUP BY a.plant_id, time
        ORDER BY b.time
    </select>
    <select id="totalEnergy" resultType="java.lang.String">
        SELECT SUM(total_energy) totalEnergy
        FROM bto_device_total
        WHERE plant_id = #{plantId}
    </select>
    <!--逆变器列表数据-->
    <select id="deviceList" resultType="entityDTO.DeviceListDTO">
        SELECT
            a.device_sn deviceSn,
            a.name1 stationName,
            a.device_status `status`,
            CASE WHEN b.update_time > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                     THEN SUM(DISTINCT (b.power)) ELSE 0 END realTimePower,
            CASE WHEN LEFT (b.update_time, 10) = CURDATE()
                     THEN SUM(DISTINCT(b.today_energy)) ELSE 0 END todayEnergy,
            CASE WHEN MONTH (b.update_time)= MONTH (NOW())
                     THEN SUM(DISTINCT(b.month_energy)) ELSE 0 END monthEnergy,
            CASE WHEN YEAR (b.update_time)= YEAR (NOW())
                     THEN SUM(DISTINCT (b.year_energy)) ELSE 0 END yearEnergy,
            SUM(DISTINCT (b.total_energy)) totalEnergy,
            CONCAT(LEFT (b.update_time, 15), '0:00') updateTime
        FROM v1_station_device a
                 LEFT JOIN bto_device_total b ON a.device_sn = b.device_sn
        WHERE a.special = '1'
        AND a.device_status IN
        <foreach collection="statusArray" item="status" separator="," open="(" close=")">
        #{status}
        </foreach>
        GROUP BY a.device_sn
        ORDER BY a.name1
    </select>


</mapper>