package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class siteCarouselPage implements Serializable {

    private String stationName;

    private String stationAdress;

    //(W)
    private Double OutputPower;

    //电站容量(kWp)
    private Double stationCapacity;

    //当日发电量(kWh)
    private Double todayEnergy;

    //累计发电量(kWh)
    private Double AllEnergy;

    //CO2减排(t)
    private Double CO2;

    //创建时间
    private String creatTime;

    //运行状态
    private int status;

    //离线时长
    private String offlineTime;

    private String monthEnergy;

    private String totalEnergy;

}
