package com.botong.controller;

import com.botong.services.service.BtoDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import utils.AjaxResult;


/**
 * 逆变器数据
 *
 * <AUTHOR> @date 2022-08-16 10:10:10
 */
@RestController
@RequestMapping("btodevice")
@Api(tags = "逆变器数据")
@CrossOrigin
public class BtoDeviceController {

    @Autowired
    private BtoDeviceService btoDeviceService;

    @ApiOperation("获取所有逆变器日月年总发电信息数据")
    @GetMapping("inverterElectricityInfo")
    public AjaxResult inverterElectricityInfo(){return AjaxResult.success("操作成功",btoDeviceService.inverterElectricityInfo());}

    @ApiOperation("获取用户的综合信息")
    @GetMapping("getUserComplexInfo")
    public AjaxResult getUserComplexInfo(String plantId){return AjaxResult.success("操作成功",btoDeviceService.getUserComplexInfo(plantId));}

}
