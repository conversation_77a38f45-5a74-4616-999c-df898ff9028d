package com.botong.controller;

import com.botong.services.service.BtoDeviceListService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;
import vo.EfficiencyVo;

import java.util.*;

/**
 * 逆变器列表
 *
 * <AUTHOR> @date 2022-08-11 15:36:33
 */
@CrossOrigin
@RestController
@RequestMapping("btodevicelist")
@Api(tags = "逆变器列表")
public class BtoDeviceListController {

    @Autowired
    private BtoDeviceListService btoDeviceListService;

//    @ApiOperation("逆变器数量")
//    @GetMapping("DeviceAllNum")
//    public AjaxResult DeviceAllNum(){return AjaxResult.success("操作成功",btoDeviceListService.DeviceAllNum());}

//    @ApiOperation("逆变器状态_0_离线")
//    @GetMapping("DeviceStatus0")
//    public AjaxResult DeviceStatus0(){
//        return AjaxResult.success("操作成功",btoDeviceListService.DeviceStatus0());
//    }
//
//    @ApiOperation("逆变器状态_1_正常运行")
//    @GetMapping("DeviceStatus1")
//    public AjaxResult DeviceStatus1(){return AjaxResult.success("操作成功",btoDeviceListService.DeviceStatus1());}

//    @ApiOperation("逆变器状态_2_电站告警")
//    @GetMapping("DeviceStatus2")
//    public AjaxResult DeviceStatus2(){return AjaxResult.success("操作成功",btoDeviceListService.DeviceStatus2());}
//
//    @ApiOperation("逆变器状态_2_电站在线")
//    @GetMapping("Deviceonline")
//    public AjaxResult Deviceonline(){return AjaxResult.success("操作成功",btoDeviceListService.Deviceonline());}
//
//    @ApiOperation("逆变器状态_3_离线_测试")
//    @GetMapping("DeviceStatus3")
//    public AjaxResult DeviceStatus3(){return AjaxResult.success("操作成功",btoDeviceListService.DeviceStatus3());}

//    @ApiOperation("逆变器正常运行率_测试")
//    @GetMapping("Staionuptime")
//    public AjaxResult Staionuptime(){return AjaxResult.success("操作成功",btoDeviceListService.Deviceuptime());}

//    @ApiOperation("总的日发电量")
//    @GetMapping("AllStationEnergy")
//    public AjaxResult AllStationEnergy(){return AjaxResult.success("操作成功",btoDeviceListService.AllStationEnergy());}

    @ApiOperation("逆变器数量及状态列表")
    @GetMapping("/deviceNum")
    public AjaxResult DeviceNum(){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceNum());
    }


    @ApiOperation("实时监控——设备概况-逆变器信息")
    @GetMapping("DeviceInformation")
    public AjaxResult DeviceInformation(String plantUid){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceInformation(plantUid));
    }
    @ApiOperation("实时监控——设备概况-逆变器对应的sn")
    @GetMapping("DeviceInformationSN")
    public AjaxResult DeviceInformationSN(String plantUid){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceInformationSN(plantUid));
    }

    @ApiOperation("实时监控——设备概况-逆变器对应的图表")
    @GetMapping("DeviceInformationTU")
    public AjaxResult DeviceInformationTU(String plantUid){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceInformationTU(plantUid));
    }

    @ApiOperation("实时监控——设备监测-列表")
    @GetMapping("DeviceMonitorUlist")
    public AjaxResult DeviceMonitorUlist(){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceMonitorUlist());
    }

    @ApiOperation("实时监控——设备监测-有功功率")
    @GetMapping("DeviceActivePower")
    public AjaxResult DeviceActivePower(String snName,String tableNme,String tableNme2){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceActivePower(snName,tableNme,tableNme2));
    }

    @ApiOperation("实时监控——设备监测-电流")
    @GetMapping("Devicecurrent")
    public AjaxResult Devicecurrent(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",btoDeviceListService.Devicecurrent(snName,tableName,tableName2));
    }

    @ApiOperation("实时监控——设备监测-电压")
    @GetMapping("DeviceVoltage")
    public AjaxResult DeviceVoltage(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceVoltage(snName,tableName,tableName2));
    }

    @ApiOperation("实时监控——设备监测-频率")
    @GetMapping("DeviceFrequency")
    public AjaxResult DeviceFrequency(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",btoDeviceListService.DeviceFrequency(snName,tableName,tableName2));
    }
    @ApiOperation("实时监控——设备监测-温度")
    @GetMapping("Devicetemperature")
    public AjaxResult Devicetemperature(String snName,String tableName,String tableName2){
        return AjaxResult.success("操作成功",btoDeviceListService.Devicetemperature(snName,tableName,tableName2));
    }

    @ApiOperation("实时发电电站列表")
    @GetMapping("workEfficiencyRanking")
    public AjaxResult workEfficiencyRanking() {
        HashMap<String, Object> workEfficiencyRanking = btoDeviceListService.workEfficiencyRanking();
        return AjaxResult.success("操作成功", workEfficiencyRanking);
    }

    @ApiOperation("采集因子-最大值最小值")
    @GetMapping("collectionMaxMin")
    public AjaxResult collectionMaxMin(String snName,String tablName,String tabletime2) {
        return AjaxResult.success("操作成功", btoDeviceListService.collectionMaxMin(snName,tablName,tabletime2));
    }
    @ApiOperation("采集因子-平均值")
    @GetMapping("collectioAvg")
    public AjaxResult collectioAvg(String snName,String tablName,String tabletime2) {
        return AjaxResult.success("操作成功", btoDeviceListService.collectioAvg(snName,tablName,tabletime2));
    }


    @ApiOperation("融合接口采集因子，最大值最小值")
    @GetMapping("mergecollectionMaxMin")
    public AjaxResult mergecollectionMaxMin(String snName,String tableName,String tableName2) {
        return AjaxResult.success("操作成功", btoDeviceListService.mergecollectionMaxMin(snName,tableName,tableName2));
    }












    @ApiOperation("通讯监控-设备监控_分页")
    @GetMapping("EquipmentMonitoring")
    public AjaxResult EquipmentMonitoring(String page, String pageSize) {
        return AjaxResult.success("操作成功", btoDeviceListService.EquipmentMonitoring(page,pageSize));
    }

    @ApiOperation("通讯监控-设备监控_模糊查询")
    @GetMapping("EquipmentMonitoringlike")
    public AjaxResult EquipmentMonitoringlike(String deviceId,String devicesn,String plantUid) {
        return AjaxResult.success("操作成功", btoDeviceListService.EquipmentMonitoringlike(devicesn,deviceId,plantUid));
    }

    @ApiOperation("通讯监控-设备监控_分页plantUid")
    @GetMapping("EquipmentMonitoringplantUid")
    public AjaxResult EquipmentMonitoringplantUid(String page, String pageSize,String plantUid) {
        return AjaxResult.success("操作成功", btoDeviceListService.EquipmentMonitoringplantUid(page,pageSize,plantUid));
    }

    @ApiOperation("逆变器离线列表")
    @GetMapping("InverterOfflineList")
    public AjaxResult InverterOfflineList(String page, String pageSize,String time1,String time2) {
        return AjaxResult.success("操作成功", btoDeviceListService.InverterOfflineList(page,pageSize,time1,time2));
    }

}
