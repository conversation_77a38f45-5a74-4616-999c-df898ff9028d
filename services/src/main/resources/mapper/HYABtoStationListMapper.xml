<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYABtoStationListMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoStationList" id="btoStationListMap">
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="userUid" column="user_uid"/>
        <result property="status" column="status"/>
        <result property="country" column="country"/>
        <result property="city" column="city"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="peakPower" column="peak_power"/>
        <result property="createDate" column="create_date"/>
        <result property="currentPower" column="current_power"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="planType" column="plan_type"/>
        <result property="installer" column="installer"/>
        <result property="operator" column="operator"/>
        <result property="owner" column="owner"/>
        <result property="ownerPhone" column="owner_phone"/>
        <result property="wisdomDeviceSn" column="wisdom_device_sn"/>
        <result property="imageUrl" column="image_url"/>
        <result property="locale" column="locale"/>
        <result property="plantNo" column="plant_no"/>
        <result property="ownerEmail" column="owner_email"/>
        <result property="ownerName" column="owner_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.BTOstationListDTO" id="btoStationListMapDTO">
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="userUid" column="user_uid"/>
        <result property="status" column="status"/>
        <result property="country" column="country"/>
        <result property="city" column="city"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="peakPower" column="peak_power"/>
        <result property="createDate" column="create_date"/>
        <result property="currentPower" column="current_power"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="planType" column="plan_type"/>
        <result property="installer" column="installer"/>
        <result property="operator" column="operator"/>
        <result property="owner" column="owner"/>
        <result property="ownerPhone" column="owner_phone"/>
        <result property="wisdomDeviceSn" column="wisdom_device_sn"/>
        <result property="imageUrl" column="image_url"/>
        <result property="locale" column="locale"/>
        <result property="plantNo" column="plant_no"/>
        <result property="ownerEmail" column="owner_email"/>
        <result property="ownerName" column="owner_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="monthEnergy" column="monthEnergy"/>
        <result property="totalEnergy" column="totalEnergy"/>
    </resultMap>

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.siteCarouselPage" id="siteCarouselPage">
        <result property="stationName" column="stationName"/>
        <result property="stationAdress" column="stationAdress"/>
        <result property="OutputPower" column="OutputPower"/>
        <result property="stationCapacity" column="stationCapacity"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="AllEnergy" column="AllEnergy"/>
        <result property="CO2" column="CO2"/>
        <result property="creatTime" column="creatTime"/>
        <result property="status" column="status"/>
        <result property="offlineTime" column="offlineTime"/>

    </resultMap>

    <resultMap type="entityDTO.stationNum" id="stationNumDTO">
        <result property="offline" column="offline"/>
        <result property="online" column="online"/>
        <result property="alert" column="alert"/>
        <result property="normal" column="normal"/>
    </resultMap>

    <resultMap type="entityDTO.AllStationList" id="AllStationList">
        <result property="plantId" column="plantId"/>
        <result property="status" column="status"/>
        <result property="name" column="name"/>
        <result property="peakPower" column="peakPower"/>
        <result property="deviceCount" column="deviceCount"/>
        <result property="plantType" column="plantType"/>
        <result property="time" column="time"/>
        <result property="createDate" column="createDate"/>
        <result property="realTimePower" column="realTimePower"/>
        <result property="workEfficiency" column="workEfficiency"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="monthEnergy" column="monthEnergy"/>
        <result property="yearEnergy" column="yearEnergy"/>
        <result property="hoursperday" column="hoursperday"/>
        <result property="totalEnergy" column="totalEnergy"/>
        <result property="city" column="city"/>
        <result property="sn" column="sn"/>
        <result property="plantUid" column="plantUid"/>
        <result property="ownerPhone" column="ownerPhone"/>
        <result property="owner" column="owner"/>
    </resultMap>

    <select id="sqlStaion" resultType="entity.BtoStationList"></select>

    <select id="StationAllenergy" resultType="entityDTO.BTOstationListDTO">
        SELECT sum(today_energy) todayEnergy,sum(month_energy) monthEnergy,sum(total_energy) totalEnergy,time
        <if test="null!=tableNme and ''!=tableNme">
            FROM
            bto_device_${tableNme}
        </if>
        WHERE
        time LIKE #{nowtime10}
        OR
        time LIKE #{nowtime20}
    </select>


    <select id="selectPages" resultType="entityDTO.siteCarouselPage">
    </select>
    <select id="StationStatus2" resultMap="stationNumDTO">
        SELECT
            count(DISTINCT(plant_id)) alert
        FROM
            v_event_mean a
                LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
            start_time LIKE '${tableName}'
    </select>
    <select id="Stationonline" resultMap="stationNumDTO">
        SELECT
            count(distinct(plant_id)) online
        FROM
            bto_device_${tableNme2} a
                LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
            time LIKE '${tableName}'
    </select>
    <select id="selectListt" resultMap="AllStationList">
        SELECT
            a.plant_id plantId,
            a.plant_uid plantuId,
            CASE
                WHEN `status` = 0 THEN'离线'
                WHEN `status` = 1 THEN'正常'
                ELSE '告警'
                END 运行状态,
            name name,
            peak_power peakPower,
            count( b.sn ) deviceCount,
            CASE
                WHEN `plant_type` = 0 THEN'并网'
                WHEN `plant_type` = 1 THEN'储能'
                WHEN `plant_type` = 2 THEN'混合'
                ELSE '交流耦合'
                END 类型,
            time time,
            create_date createDate,
            FORMAT(sum(power),4) realTimePower,
            FORMAT((sum(power)/1000)/peak_power,4) workEfficiency,
            sum(today_energy) todayEnergy,
            sum(month_energy) monthEnergy,
            sum(year_energy) yearEnergy,
            FORMAT(sum( today_energy )/peak_power,4) hoursperday,
            sum( total_energy ) totalEnergy,
            city city
        FROM
            `v_station_base` a
            LEFT JOIN (
            SELECT
            plant_id,
            count(DISTINCT(sn)) 逆变器数,
            sn,
            power,
            today_energy,
            month_energy,
            year_energy,
            total_energy,
            time
            FROM
            v_device_list aa
            LEFT	JOIN (

            SELECT sn,power,today_energy,month_energy,year_energy,total_energy,time
            FROM bto_device_${time}
            WHERE
            time LIKE '${time2}'
            GROUP BY SN,time
            ORDER BY time desc
            ) bb
            ON aa.device_sn = bb.sn

            GROUP BY
            sn
            ) b ON a.plant_id = b.plant_id
        GROUP BY a.plant_id
    </select>
    <select id="ListOfTotalPowerStationsstationName" resultMap="AllStationList">
        SELECT
            a.plant_id plantId,
            a.plant_uid plantuId,
            CASE
                WHEN `status` = 0 THEN'离线'
                WHEN `status` = 1 THEN'正常'
                ELSE '告警'
                END 运行状态,
            name name,
            peak_power peakPower,
            count( b.sn ) deviceCount,
            CASE
                WHEN `plant_type` = 0 THEN'并网'
                WHEN `plant_type` = 1 THEN'储能'
                WHEN `plant_type` = 2 THEN'混合'
                ELSE '交流耦合'
                END 类型,
            time time,
            create_date createDate,
            FORMAT(sum(power),4) realTimePower,
            FORMAT((sum(power)/1000)/peak_power,4) workEfficiency,
            sum(today_energy) todayEnergy,
            sum(month_energy) monthEnergy,
            sum(year_energy) yearEnergy,
            FORMAT(sum( today_energy )/peak_power,4) hoursperday,
            sum( total_energy ) totalEnergy,
            city city
        FROM
            `v_station_base` a
            LEFT JOIN (
            SELECT
            plant_id,
            count(DISTINCT(sn)) 逆变器数,
            sn,
            power,
            today_energy,
            month_energy,
            year_energy,
            total_energy,
            time
            FROM
            v_device_list aa
            LEFT	JOIN (

            SELECT sn,power,today_energy,month_energy,year_energy,total_energy,time
            FROM bto_device_${time}
            WHERE
            time LIKE '${time2}'
            GROUP BY SN,time
            ORDER BY time desc
            ) bb
            ON aa.device_sn = bb.sn

            GROUP BY
            sn
            ) b ON a.plant_id = b.plant_id
        where  name like '%${stationName}%'
        GROUP BY a.plant_id
    </select>
    <select id="ListOfTotalPowerStationsPhone" resultMap="AllStationList">
        SELECT
            plant_id plantId,
            plant_uid plantUid,
            `name` name,
            OWNER owner,
            owner_phone ownerPhone
        FROM
            v_station_list
        WHERE
            plant_uid = #{plantUid}
    </select>

</mapper>