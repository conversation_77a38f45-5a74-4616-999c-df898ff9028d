package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("总装机容量&日月年总&近六月&近七日发电量实体")
public class PeakPowerAndEnergy implements Serializable {

    @ApiModelProperty(value = "总装机容量",notes = "总装机容量")
    private String allPeakPower;

    @ApiModelProperty(value = "总发电量",notes = "总发电量")
    private String totalEnergy;

    @ApiModelProperty(value = "日发电量",notes = "日发电量")
    private String todayEnergy;

    @ApiModelProperty(value = "月发电量",notes = "月发电量")
    private String monthEnergy;

    @ApiModelProperty(value = "年发电量",notes = "年发电量")
    private String yearEnergy;

    @ApiModelProperty(value = "近七日发电量",notes = "近七日发电量")
    private List<HashMap<String,Object>> sevenDayEnergy;

    @ApiModelProperty(value = "近六月发电量",notes = "近六月发电量")
    private List<HashMap<String,Object>> sixMonthEnergy;

}
