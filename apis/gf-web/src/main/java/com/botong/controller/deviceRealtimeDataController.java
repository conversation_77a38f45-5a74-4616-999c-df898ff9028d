package com.botong.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@Api(tags="逆变器实时数据")
@RestController
@RequestMapping("deviceRealtimeData")
public class deviceRealtimeDataController {
    @Autowired
    private com.botong.services.service.deviceRealtimeDataService deviceRealtimeDataService;

    @ApiOperation("逆变器实时数据查询")
    @GetMapping("selectDeviceRealtimeData")
    public AjaxResult selectDeviceRealtimeData(String time,String sn,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功", deviceRealtimeDataService.selectDeviceRealtimeData(time,sn,page,pageSize));
    }

}
