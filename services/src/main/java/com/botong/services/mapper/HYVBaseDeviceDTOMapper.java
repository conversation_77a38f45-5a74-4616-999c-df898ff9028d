package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.AreaDeviceDTO;
import entityDTO.VBaseDeviceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HYVBaseDeviceDTOMapper extends BaseMapper<VBaseDeviceDTO> {
//    List<VBaseDeviceDTO> selectAll(@Param("time") String time, @Param("state") String state, @Param("city") String city, @Param("address") String address, @Param("table") String table, @Param("sn") String sn);
    String[] selectAllSn(@Param("table") String table);

    String[] selectSn(@Param("state") String state,@Param("city") String city,@Param("address") String address,@Param("table") String table);

    String[] selectMonthSn(@Param("state") String state,@Param("city") String city,@Param("address") String address);

    List<AreaDeviceDTO> areaStatisticsByDay(@Param("time") String time, @Param("town") String town, @Param("tableName") String tableName);

    List<AreaDeviceDTO> areaStatisticsByMonth(@Param("time") String time,@Param("town") String town);

    List<AreaDeviceDTO> areaStatisticsByYear(@Param("time") String time,@Param("town") String town);

}
