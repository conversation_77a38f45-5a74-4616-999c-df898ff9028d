package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class BtoDeviceTypeDTO implements Serializable {
    /*
    * 设备类型
    * */
    private String model;
    private String Manufacturer;
    private String type;
    private int No;

    private String userUid;
    private String userName;
    private String userTel;
    private String special;
    private String peakPower;
    private String plantName;
    private String address;
    private String updateTime;
    private String enrol;


}
