package com.botong.controller;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.services.service.BtoStationBaseService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoStationBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import utils.AjaxResult;


/**
 * 电站基本信息
 *
 * <AUTHOR> @date 2022-08-15 15:35:23
 */
@RestController
@RequestMapping("btostationbase")
@Api(tags = "电站基本信息")
@CrossOrigin
public class BtoStationBaseController {

    @Autowired
    private BtoStationBaseService btoStationBaseService;


    @ApiOperation("省级电站数量")
    @GetMapping("provincialNumber")
    public AjaxResult provincialNumber() {
        return AjaxResult.success("操作成功", btoStationBaseService.provincialNumber());
    }


    @ApiOperation("市级电站数量")
    @GetMapping("municipalNumber")
    public AjaxResult municipalNumber() {
        return AjaxResult.success("操作成功", btoStationBaseService.municipalNumber());
    }

    @ApiOperation("电站基本详情信息")
    @GetMapping("stationBaseDetail")
    public AjaxResult stationBaseDetail() {
        return AjaxResult.success("操作成功", btoStationBaseService.stationBaseDetail());
    }

    @ApiOperation("分页——测试")
    @GetMapping("paginationTest")
    public AjaxResult paginationTest(String pageNum, String pageSize) {
        PageHelper.startPage(Integer.valueOf(pageNum), Integer.valueOf(pageSize));
        BaseMapper<BtoStationBase> baseMapper = btoStationBaseService.getBaseMapper();
        List<BtoStationBase> btoStationBases = baseMapper.selectList(null);
        PageInfo<BtoStationBase> btoStationBasePageInfo = new PageInfo<>(btoStationBases);
        return AjaxResult.success("操作成功",btoStationBasePageInfo);
    }

    @ApiOperation("根据省或市获取相应的电站，省级type：provincial，市级type：municipal")
    @GetMapping("getProvincialStation")
    public AjaxResult getProvincialStation(String type, String areaName) {
        return AjaxResult.success("操作成功", btoStationBaseService.getProvincialStation(type, areaName));
    }

    @ApiOperation("获取所有的电站数量")
    @GetMapping("getAllStation")
    public AjaxResult getProvincialStation() {
        return AjaxResult.success("操作成功", btoStationBaseService.getAllStation());
    }

    @ApiOperation("获取砍伐减排信息")
    @GetMapping("getDeforestation")
    public AjaxResult getDeforestation() {
        return AjaxResult.success("操作成功", btoStationBaseService.getDeforestation());
    }



}
