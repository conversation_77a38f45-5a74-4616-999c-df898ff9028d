package com.botong.controller;

import com.botong.services.service.HYBtoStationListService;
import entityDTO.UpdateStationInfoForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

@CrossOrigin
@RestController
@RequestMapping("btostationlist")
@Api(tags = "首页及电站列表")
public class HYBtoStationListController {

    @Resource
    private HYBtoStationListService hyBtoStationListService;

    @ApiOperation("电站列表--带筛选")
    @GetMapping("ListOfTotalPowerStations")
    public AjaxResult ListOfTotalPowerStations() {
        return AjaxResult.success("操作成功", hyBtoStationListService.ListOfTotalPowerStations());
    }

    @ApiOperation("电站列表--修改电站信息")
    @PostMapping("updateStationInfo")
    public AjaxResult updateStationInfo(@RequestBody UpdateStationInfoForm updateStationInfoForm) {
        return AjaxResult.success("操作成功", hyBtoStationListService.updateStationInfo(updateStationInfoForm));
    }

/*    @ApiOperation("总装机容量")
    @GetMapping("StationAllpeakPower")
    public AjaxResult StationAllpeakPower(){
        return AjaxResult.success("操作成功",hyBtoStationListService.StationAllpeakPower());
    }*/

    @ApiOperation("获取所有的电站名称名称")
    @GetMapping("getAllStationName")
    public AjaxResult getAllStationName() {
        return AjaxResult.success("操作成功", hyBtoStationListService.getAllStationName());
    }

    @ApiOperation("光伏监控中心")
    @GetMapping("GFmonitoringCenter")
    public AjaxResult GFmonitoringCenter() {
        return AjaxResult.success("操作成功", hyBtoStationListService.GFmonitoringCenter());
    }

//  @ApiOperation("日—月-总发电量")
//  @GetMapping("StationAllenergy")
//  public AjaxResult StationAllenergy(){
//    Date date=new Date();
//    Date now20 = new Date(date.getTime() - 1200000);
//    Date now10 = new Date(date.getTime() - 600000);
//    SimpleDateFormat dateFormat= new SimpleDateFormat("yyyyMMdd");
//    SimpleDateFormat dateFormattime10= new SimpleDateFormat("yyyy-MM-dd HH:mm");
//    SimpleDateFormat dateFormattime20= new SimpleDateFormat("yyyy-MM-dd HH:mm");
//    //表名
//    String tableNme=dateFormat.format(date);
//    //截取当前时间前10分钟
//    String Nowtime10=(dateFormattime10.format(now10)).substring(0,15)+"%";
//    //当前时间前20分钟
//    String Nowtime20=(dateFormattime20.format(now20)).substring(0,15)+"%";
//    return AjaxResult.success("操作成功",hyBtoStationListService.StationAllenergy(tableNme,Nowtime10,Nowtime20));
//  }

    @ApiOperation("总电站数量列表_电话")
    @GetMapping("ListOfTotalPowerStationsPhone")
    public AjaxResult ListOfTotalPowerStationsPhone(String plantUid) {
        return AjaxResult.success("操作成功", hyBtoStationListService.ListOfTotalPowerStationsPhone(plantUid));
    }

//  @ApiOperation("总电站数量列表_模糊查询")
//  @GetMapping("ListOfTotalPowerStationsstationName")
//  public AjaxResult ListOfTotalPowerStationsstationName(String page,String pageSize,String stationName){
//    return AjaxResult.success("操作成功",hyBtoStationListService.ListOfTotalPowerStationsstationName(page,pageSize,stationName));
//  }

    @ApiOperation("电站&逆变器数量及状态列表")
    @GetMapping("/stationAndDeviceNum")
    public AjaxResult stationAndDeviceNum() {
        return AjaxResult.success("操作成功", hyBtoStationListService.stationAndDeviceNum());
    }

    @ApiOperation("总装机容量及日-月-总发电量")
    @GetMapping("/stationAllenergyAndAllpeakPower")
    public AjaxResult stationAllenergyAndAllpeakPower() {
        return AjaxResult.success("操作成功", hyBtoStationListService.stationAllenergyAndAllpeakPower());
    }


}



