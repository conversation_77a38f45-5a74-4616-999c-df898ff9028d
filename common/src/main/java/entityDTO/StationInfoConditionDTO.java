package entityDTO;

import cn.hutool.core.date.DateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value="StationInfoConditionDTO",description = "河源电站统计信息页面参数实体类")
public class StationInfoConditionDTO implements Serializable {
    /**
     * 按年、月、日搜索 标记
     *   Y(年)、M(月)、D(日)
     */
    @ApiModelProperty(value = "按年、月、日搜索 标记",required = true,example = "Y",notes = "Y(年)、M(月)、D(日)")
    private String timeFlag;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间",required = true)
    private String startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间",required = true)
    private String endTime;
    /**
     * 电站名称
     */
    @ApiModelProperty(value = "电站名称",notes = "(模糊字符串)")
    private String stationName;
    /**
     * 创建时间（start）
     */
    @ApiModelProperty(value = "创建时间-开始时间段")
    private String createStartTime;
    /**
     * 创建时间（end）
     */
    @ApiModelProperty(value = "创建时间-结束时间段")
    private String createEndTime;
    /**
     * 模糊搜索（地址）
     */
    @ApiModelProperty(value = "模糊搜索（地址）")
    private String address;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;
    /**
     * 页面大小
     */
    @ApiModelProperty(value = "页面大小")
    private Integer pageSize;

}
