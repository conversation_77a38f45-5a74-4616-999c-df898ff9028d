package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "更新逆变器表单数据")
public class UpdateDeviceFormDTO implements Serializable {

    @ApiModelProperty(value = "旧逆变器SN", notes = "旧逆变器SN")
    private String oldDeviceSN;

    @ApiModelProperty(value = "新逆变器SN", notes = "新逆变器SN")
    private String newDeviceSN;

    @ApiModelProperty(value = "逆变器地址", notes = "逆变器地址")
    private String deviceAddress;

    @ApiModelProperty(value = "逆变器厂家", notes = "逆变器厂家")
    private String manufacturer;

    @ApiModelProperty(value = "逆变器型号", notes = "逆变器型号")
    private String model;

}
