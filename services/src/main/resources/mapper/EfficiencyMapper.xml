<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.EfficiencyMapper">

    <resultMap type="entityDTO.EfficiencyDTO" id="EfficiencyMap">
        <result property="name" column="name"/>
        <result property="peakPower" column="peak_power"/>
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="date" column="date"/>
        <result property="date" column="date1"/>
        <result property="energy" column="energy"/>
        <result property="yoy" column="yoy"/>
        <result property="mom" column="mom"/>
        <result property="xl" column="xl"/>
    </resultMap>
    <select id="PowerGenerationMonth" resultMap="EfficiencyMap">
        SELECT
            b.name1 name,
            peak_power,
            a.plant_id plant_id,
            b.plant_uid plant_uid,
            date,
            energy,
            energy /(3.5 * peak_power ) xl
        FROM
            v_day a
                LEFT JOIN v1_station_device b ON a.plant_id = b.plant_id
        WHERE
            a.plant_id = #{plantId}
          AND date LIKE #{month}
        GROUP BY
            date
        ORDER BY
            date
    </select>

    <select id="PowerGenerationYear" resultMap="EfficiencyMap">
        SELECT
            b.name1 NAME,
            peak_power,
            a.plant_id plant_id,
            b.plant_uid,
            LEFT ( date, 7 ) time,
            date date1,
            energy,
            energy /(3.5 * peak_power ) xl
        FROM
            v_day a
                LEFT JOIN v1_station_device b ON a.plant_id = b.plant_id
        WHERE
            a.plant_id = #{plantId}
          AND date LIKE #{year}
        GROUP BY
            time
        ORDER BY
            time
    </select>
</mapper>