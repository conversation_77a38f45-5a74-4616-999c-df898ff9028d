<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.StationInfoMapper">

    <!--查询用户APP-单个用户的电站信息-->
    <select id="selectIndexPage" resultType="entityDTO.StationInfoByUser">
        SELECT plant_id         plantId,
               plant_uid        plantUID,
               `name`           plantName,
               create_date      createTime,
               peak_power       peakPower,
               `status`         status,
               wisdom_device_sn imei,
               current_power    currentPower,
               today_energy     todayEnergy,
               month_energy     monthEnergy,
               year_energy      yearEnergy,
               total_energy     totalEnergy,
               date_time        dateTime,
               longitude,
               latitude,
               owner_phone      phone,
               address1         address,
               special
        FROM `bto_station_list`
        WHERE deleted = 0
          AND user_uid = #{userUID}; -- 通过 user_uid 电站基本信息
    </select>
    <!--逆变器告警信息列表-->
    <select id="inverterAlarmInfo" resultType="entityDTO.AppAlarmInfoDTO">
        SELECT
        t2.sn SN,
        t2.alarm_message alarmMessage,
        t2.mean alarmMean,
        t2.start_time startTime
        FROM
        bto_device_total t1
        LEFT JOIN v_event_r t2 ON t1.device_sn = t2.sn
        WHERE
        date( start_time )= CURDATE() AND
        t1.plant_id IN
        <foreach collection="plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
    </select>
    <!--逆变器信息列表-->
    <select id="inverterInfo" resultType="entityDTO.InverterInfoDTO">
        SELECT
        t1.plant_id plantId,
        t1.device_sn inverterSN,
        t1.power currentPower,
        t1.today_energy todayEnergy,
        t1.total_energy totalEnergy,
        t2.create_date warrantyDate,
        t2.update_time
        FROM
        bto_device_total t1
        LEFT JOIN bto_station_list t2 ON t1.plant_id = t2.plant_id
        WHERE
        t1.plant_id IN
        <foreach collection="plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
        AND t1.device_sn != ''
    </select>
    <select id="inverterDetails" resultType="entityDTO.InverterDetailsDTO">
        SELECT t1.device_sn   inverterSN,
               t1.model,
               t1.manu_facturer  manufacturer,
               t3.c_user_name userName,
               t2.display     displayVersion,
               t2.master      masterVersion,
               t2.slave       slaveVersion
        FROM bto_device_list t1
                 LEFT JOIN bto_version t2 ON t1.device_sn = t2.sn
                 LEFT JOIN bto_user_list t3 ON t1.user_uid = t3.c_user_uid
        WHERE t1.device_sn = #{inverterSN}
    </select>
    <!--光伏组件信息-->
    <select id="getPvMoudle" resultType="entityDTO.PvMoudleDTO">
        SELECT t1.ipv1,
               t1.ipv2,
               t1.ipv3,
               t1.ipv4,
               t1.ipv5,
               t1.ipv6,
               t1.ipv7,
               t1.ipv8,
               t1.ipv9,
               t1.ipv10,
               t1.ipv11,
               t1.ipv12,
               t1.vpv1,                                                                                  -- PV电压
               t1.vpv2,
               t1.vpv3,
               t1.vpv4,
               t1.vpv5,
               t1.vpv6,
               t1.vpv7,
               t1.vpv8,
               t1.vpv9,
               t1.vpv10,
               t1.vpv11,
               t1.vpv12,
               t1.iac1,                                                                                  -- AC电流
               t1.iac2,
               t1.iac3,
               t1.vac1,                                                                                  -- AC电压
               t1.vac2,
               t1.vac3,
               t1.fac                                                                          fac1,     -- 频率
               t1.fac1                                                                         fac2,
               t1.fac2                                                                         fac3,
               t1.time                                                                         dateTime, -- 数据时间
               concat(t2.pv1_dc1, ' - ', t2.pv1_dc2, ' - ', t2.pv1_dc3, ' - ', t2.pv1_dc4)     pv1,-- 组串信息
               concat(t2.pv2_dc1, ' - ', t2.pv2_dc2, ' - ', t2.pv2_dc3, ' - ', t2.pv2_dc4)     pv2,
               concat(t2.pv3_dc1, ' - ', t2.pv3_dc2, ' - ', t2.pv3_dc3, ' - ', t2.pv3_dc4)     pv3,
               concat(t2.pv4_dc1, ' - ', t2.pv4_dc2, ' - ', t2.pv4_dc3, ' - ', t2.pv4_dc4)     pv4,
               concat(t2.pv5_dc1, ' - ', t2.pv5_dc2, ' - ', t2.pv5_dc3, ' - ', t2.pv5_dc4)     pv5,
               concat(t2.pv6_dc1, ' - ', t2.pv6_dc2, ' - ', t2.pv6_dc3, ' - ', t2.pv6_dc4)     pv6,
               concat(t2.pv7_dc1, ' - ', t2.pv7_dc2, ' - ', t2.pv7_dc3, ' - ', t2.pv7_dc4)     pv7,
               concat(t2.pv8_dc1, ' - ', t2.pv8_dc2, ' - ', t2.pv8_dc3, ' - ', t2.pv8_dc4)     pv8,
               concat(t2.pv9_dc1, ' - ', t2.pv9_dc2, ' - ', t2.pv9_dc3, ' - ', t2.pv9_dc4)     pv9,
               concat(t2.pv10_dc1, ' - ', t2.pv10_dc2, ' - ', t2.pv10_dc3, ' - ', t2.pv10_dc4) pv10,
               concat(t2.pv11_dc1, ' - ', t2.pv11_dc2, ' - ', t2.pv11_dc3, ' - ', t2.pv11_dc4) pv11,
               concat(t2.pv12_dc1, ' - ', t2.pv12_dc2, ' - ', t2.pv12_dc3, ' - ', t2.pv12_dc4) pv12
        FROM bto_device_${today} t1
                 LEFT JOIN bto_device_modules t2 ON t1.sn = t2.sn
        WHERE t1.sn = #{inverterSN}
        ORDER BY t1.time DESC
        LIMIT 1
    </select>
    <!--逆变器图表信息-->
    <select id="stationChartInfo" parameterType="vo.ChartConditionVO" resultType="entityDTO.OtherChartDTO">
        SELECT
        <if test="chartConditionVO.conditionType == 'M'.toString">
            date dateTime,
        </if>
        <if test="chartConditionVO.conditionType == 'Y'.toString">
            LEFT(date, 7) dateTime,
        </if>
        <if test="chartConditionVO.conditionType == 'A'.toString">
            LEFT(date, 4) dateTime,
        </if>
        sum(energy) totalEnergy
        FROM bto_station_day
        WHERE
        <if test="chartConditionVO.conditionType == 'M'.toString">
            LEFT ( date, 7 ) =LEFT ( #{chartConditionVO.dateTime}, 7 ) AND
        </if>
        <if test="chartConditionVO.conditionType == 'Y'.toString">
            LEFT(date, 4) = LEFT ( #{chartConditionVO.dateTime}, 4 ) AND
        </if>
        plant_id IN
        <foreach collection="chartConditionVO.plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
        group by dateTime
    </select>
    <!--逆变器图表信息-日-->
    <select id="stationChartInfoByDay" parameterType="vo.ChartConditionVO" resultType="entityDTO.DayChartDTO">
        SELECT
        t1.sn inverterSN,
        t1.power,
        t1.today_energy todayEnergy,
        t1.time dateTime
        FROM bto_device_${tableSuffix} t1
        LEFT JOIN bto_device_total t2 ON t1.sn = t2.device_sn
        WHERE t2.plant_id IN
        <foreach collection="chartConditionVO.plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
        AND LEFT(t1.time,10) = LEFT(#{chartConditionVO.dateTime},10)
        GROUP BY t1.time,t1.sn
        ORDER BY t1.sn, t1.time
    </select>
    <select id="totalStationChartInfoByDay" parameterType="vo.ChartConditionVO" resultType="entityDTO.DayChartDTO">
        SELECT
        'total' inverterSN,
        sum(t1.power) power,
        sum(t1.today_energy) todayEnergy,
        t1.time dateTime
        FROM bto_device_${tableSuffix} t1
        LEFT JOIN bto_device_total t2 ON t1.sn = t2.device_sn
        WHERE t2.plant_id IN
        <foreach collection="chartConditionVO.plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
        AND LEFT(t1.time,10) = LEFT(#{chartConditionVO.dateTime},10)
        GROUP BY t1.time
        ORDER BY t1.time
    </select>
    <!--运维器信息列表-->
    <select id="operatorInfo" resultType="entityDTO.OperatorInfoDTO">
        SELECT plant_id         plantId,
               plant_uid        plantUID,
               wisdom_device_sn imei,
               special
        FROM bto_station_list
        WHERE deleted = 0
          AND user_uid = #{userUID}
    </select>
    <!--逆变器实时告警信息列表-->
    <select id="inverterRealTimeAlarmInfo" resultType="entityDTO.AppAlarmInfoDTO">
        SELECT
        t1.sn SN,
        t1.mean alarmMean,
        t1.start_time startTime,
        t1.alarm_message alarmMessage,
        t1.grade
        FROM
        v_event_r t1 LEFT JOIN bto_device_list t2 ON t1.sn=t2.device_sn
        WHERE
        t2.plant_id IN
        <foreach collection="plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
        AND t1.`status` = 0
        AND t1.alarm_message NOT LIKE 'PV%exception'
        ORDER BY t1.start_time DESC
    </select>
    <!--逆变器历史告警信息列表-->
    <select id="inverterHistoryAlarmInfo" resultType="entityDTO.AppAlarmInfoDTO">
        SELECT
        t1.sn SN,
        t1.mean alarmMean,
        t1.start_time startTime,
        t1.alarm_message alarmMessage,
        t1.grade
        FROM
        ( SELECT sn, mean, start_time, grade, alarm_message FROM v_event_r UNION SELECT sn, mean, start_time, grade,
        alarm_message FROM v_event_h ) t1
        LEFT JOIN bto_device_list t2 ON t1.sn=t2.device_sn
        WHERE
        t2.plant_id IN
        <foreach collection="plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
        AND t1.alarm_message NOT LIKE 'PV%exception'
        ORDER BY t1.start_time DESC
    </select>
    <!--运维器实时告警信息列表-->
    <select id="operatorRealTimeAlarmInfo" resultType="entityDTO.OperatorAlarmInfoDTO">
        SELECT t1.wisdom_device_sn IMEI,
               t1.alarm_name       alarmName,
               t1.alarm_time       alarmTime,
               t1.a_point_voltage  aPointVoltage,
               t1.b_point_voltage  bPointVoltage,
               t1.c_point_voltage  cPointVoltage
        FROM bto_operation_fault t1
                 LEFT JOIN bto_station_list t2 ON t1.plant_uid = t2.plant_uid
        WHERE t2.user_uid = #{userUID}
          AND LEFT(t1.end_time, 15) > LEFT(DATE_SUB(now(), INTERVAL 20 MINUTE), 15)
        ORDER BY t1.alarm_time DESC
    </select>
    <!--运维器历史告警信息列表-->
    <select id="operatorHistoryAlarmInfo" resultType="entityDTO.OperatorAlarmInfoDTO">
        SELECT t1.wisdom_device_sn IMEI,
               t1.alarm_name       alarmName,
               t1.alarm_time       alarmTime,
               t1.a_point_voltage  aPointVoltage,
               t1.b_point_voltage  bPointVoltage,
               t1.c_point_voltage  cPointVoltage
        FROM bto_operation_fault t1
                 LEFT JOIN bto_station_list t2 ON t1.plant_uid = t2.plant_uid
        WHERE t2.user_uid = #{userUID}
        ORDER BY t1.alarm_time DESC
    </select>
    <!--运维器日月年总图表数据信息-->
    <select id="operatorChartInfo" parameterType="vo.ChartConditionVO" resultType="entityDTO.OperatorChartInfoDTO">
        SELECT
        plant_uid plantUID,
        wisdom_device_sn IMEI,
        ROUND(SUM(pv_tel),2) pvEnergy,
        <if test="chartConditionVO.special != '3'.toString ">
            ROUND(SUM(load_tel),2) useEnergy,
            ROUND(SUM(auto_tel),2) selfEnergy,
            ROUND(SUM(sell_tel),2) sellEnergy,
            ROUND(SUM(buy_tel),2) buyEnergy,
            ROUND(SUM(pv_tel),2)-ROUND(SUM(sell_tel),2) selfUseEnergy
        </if>
        <if test="chartConditionVO.special == '3'.toString ">
            '0' useEnergy,
            '0' selfEnergy,
            '0' sellEnergy,
            '0' buyEnergy,
            '0' selfUseEnergy
        </if>
        <if test="chartConditionVO.conditionType == 'M'.toString">
            , LEFT(time,10) date
        </if>
        <if test="chartConditionVO.conditionType == 'Y'.toString">
            , LEFT(time,7) date
        </if>
        <if test="chartConditionVO.conditionType == 'A'.toString">
            , LEFT(time,4) date
        </if>
        <if test="chartConditionVO.conditionType == 'D'.toString">
            , time date
        </if>
        FROM
        bto_operation_day
        WHERE
        plant_uid
        IN
        <foreach collection="chartConditionVO.plantIds" item="plantId" open="(" separator="," close=")" index="index">
            #{plantId}
        </foreach>
        <if test="chartConditionVO.conditionType == 'M'.toString ">
            AND LEFT(time,7) = LEFT(#{chartConditionVO.dateTime},7)
        </if>
        <if test="chartConditionVO.conditionType == 'Y'.toString ">
            AND LEFT(time,4) = LEFT(#{chartConditionVO.dateTime},4)
        </if>
        <if test="chartConditionVO.conditionType != '' and chartConditionVO.conditionType!=null ">
            GROUP BY date
            ORDER BY date
        </if>
    </select>
    <!--运维器实时监控信息-->
    <select id="operatorMonitorInfo" resultType="entityDTO.OperatorMonitorInfoDTO">
        SELECT wisdom_device_sn IMEI, apv, bpv, cpv, dpv, update_time dataTime
        FROM bto_operation
        WHERE plant_uid = #{plantUID}
    </select>
    <select id="selectSemaphore" resultType="java.lang.String">
        SELECT Rssi semaphore
        FROM bto_device_total
        WHERE plant_id = #{plantId}
    </select>
    <!--单个运维器故障信息-->
    <select id="selectOperatorAlarmInfo" resultType="entityDTO.OperatorAlarmInfoDTO">
        SELECT
            wisdom_device_sn IMEI,
               alarm_name alarmName,
               alarm_time alarmTime,
               a_point_voltage aPointVoltage,
               b_point_voltage bPointVoltage,
               c_point_voltage cPointVoltage
        FROM bto_operation_fault
        WHERE plant_uid = #{plantId}
          AND LEFT(end_time, 15) > LEFT(DATE_SUB(now(), INTERVAL 20 MINUTE), 15)
    </select>
    <!--查询逆变器光伏输入电路数量-->
    <select id="selectPvInputNumber" resultType="java.lang.Integer">
        SELECT circuit FROM bto_device_type WHERE model = #{inverterModel}
    </select>
</mapper>