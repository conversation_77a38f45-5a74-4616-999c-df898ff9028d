<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYABtoDeviceMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoDevice" id="btoDeviceMap">
        <result property="dataloggerSn" column="datalogger_sn"/>
        <result property="sn" column="sn"/>
        <result property="ipv1" column="ipv1"/>
        <result property="ipv2" column="ipv2"/>
        <result property="ipv3" column="ipv3"/>
        <result property="vpv1" column="vpv1"/>
        <result property="vpv2" column="vpv2"/>
        <result property="vpv3" column="vpv3"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="power" column="power"/>
        <result property="powerFactor" column="power_factor"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="monthEnergy" column="month_energy"/>
        <result property="yearEnergy" column="year_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="temperature" column="temperature"/>
        <result property="fac" column="fac"/>
        <result property="fac1" column="fac1"/>
        <result property="fac2" column="fac2"/>
        <result property="time" column="time"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <resultMap id="ElectricityStatisticsVoMap" type="vo.ElectricityStatisticsVo">
        <result property="dayElectricity" column="dayElectricity"/>
        <result property="monthElectricity" column="monthElectricity"/>
        <result property="yearElectricity" column="yearElectricity"/>
        <result property="allElectricity" column="allElectricity"/>
    </resultMap>

    <resultMap type="com.botong.services.vo.InverterVo" id="getInverterByPlantIdMap">
        <result property="plantId" column="plant_id"/>
        <result property="name" column="name1"/>
        <result property="currentPower" column="peak_power"/>
        <result property="sn" column="sn"/>
        <result property="creatTime" column="create_date"/>
    </resultMap>

    <resultMap id="InverterVoMap" type="vo.InverterVo">
        <result property="inverterName" column="name1"/>
        <result property="inverterSn" column="sn"/>
    </resultMap>

    <resultMap id="getMonthElectricitySnMap" type="com.botong.services.vo.ChartVo">
        <result property="plantId" column="plant_id"/>
        <result property="date" column="date"/>
        <result property="energy" column="energy"/>
    </resultMap>

    <resultMap id="getYearElectricitySnMap" type="com.botong.services.vo.ChartVo">
        <result property="plantId" column="plant_id"/>
        <result property="date" column="t"/>
        <result property="energy" column="sum(energy)"/>
    </resultMap>

    <resultMap id="getYearChartVoMap" type="com.botong.services.vo.YearChartVo">
        <result property="plantId" column="plant_id"/>
        <result property="time" column="time"/>
        <result property="energy" column="energy"/>
    </resultMap>

    <resultMap type="com.botong.services.vo.DayPowerVo" id="getDayPowerBySnMap">
        <result property="power" column="power"/>
        <result property="time" column="time"/>
    </resultMap>
    <resultMap type="com.botong.services.vo.ChartVo" id="getAllElectricitySnMap">
        <result property="plantId" column="sn"/>
        <result property="date" column="time"/>
        <result property="energy" column="total_energy"/>
    </resultMap>

    <resultMap type="vo.MonthElectricityVo" id="MonthElectricityVoMap">
        <result property="energy" column="energy"/>
        <result property="date" column="date"/>
    </resultMap>

    <select id="inverterElectricityInfo" parameterType="string" resultMap="ElectricityStatisticsVoMap">
        SELECT sum(today_energy) dayElectricity,
               sum(month_energy) monthElectricity,
               sum(year_energy)  yearElectricity,
               sum(total_energy) allElectricity
        FROM (SELECT today_energy,
                     month_energy,
                     year_energy,
                     total_energy
              FROM v_device_list aa
                       LEFT JOIN (SELECT sn,
                                         today_energy,
                                         month_energy,
                                         year_energy,
                                         total_energy
                                  FROM ${tableName}
                                  WHERE left(time, 10) = #{time}
                                  GROUP BY SN,
                                           time
                                  ORDER BY time DESC) bb ON aa.device_sn = bb.sn
              WHERE special = '1'
                AND sn IS NOT NULL
              GROUP BY sn) q;
    </select>
    <select id="getUserByInverterMoreOne" resultType="list">
        select name1
        from v1_station_device
        group by name1
        having count(name1) > 1
    </select>

    <select id="getUserByInverterEqualOne" resultType="java.lang.String">
        select name1
        from v1_station_device
        group by name1
        having count(name1) = 1
    </select>
    <select id="getCurrentPowerBySn" parameterType="string" resultType="float">
        select power
        from ${tableName}
        where datalogger_sn = #{sn}
        ORDER BY update_time desc
        limit 1
    </select>
    <select id="getDayMonthYearAllInfo" resultMap="btoDeviceMap">
        select today_energy, month_energy, year_energy, total_energy
        from ${tableName}
        where datalogger_sn = #{dataloggerSn}
        order by update_time desc
        limit 1
    </select>

    <select id="getDayPowerBySn" parameterType="string" resultMap="getDayPowerBySnMap">
        SELECT sn,
               power,
               time
        FROM ${tableName1}
        WHERE time LIKE #{time}
          AND sn = #{sn}
    </select>
    <select id="getMonthElectricitySn" parameterType="string" resultMap="getMonthElectricitySnMap">
        SELECT plant_id,
               date,
               energy
        FROM v_day
        WHERE date LIKE #{time}
          AND plant_id = #{plantId}
        ORDER BY date
    </select>
    <select id="getYearElectricitySn" parameterType="string" resultMap="getYearChartVoMap">
        SELECT plant_id,
               LEFT(date, 7)  time,
               sum(energy) as energy
        FROM v_day
        WHERE plant_id = #{plantId}
          AND LEFT(date, 4) = #{time}
        GROUP BY time
        ORDER BY date
    </select>
    <select id="getAllElectricitySn" parameterType="string" resultMap="getAllElectricitySnMap">
        SELECT sn,
               total_energy,
               time
        FROM ${tableName}
        WHERE time LIKE #{time}
          AND sn = #{sn}
        ORDER BY time DESC
        LIMIT 1
    </select>

    <select id="getAllElectricity" resultType="com.botong.services.vo.ChartVo">
        select plant_id    plantId,
               `year`      date,
               year_energy energy
        from v_station_year
        where plant_id = #{plantId}
        GROUP BY `year`
        ORDER BY `year`
    </select>
    <select id="getElectricityBySn" parameterType="string" resultMap="btoDeviceMap">
        SELECT sn,
               today_energy,
               total_energy,
               time
        FROM ${tableName}
        WHERE time LIKE #{time}
          AND sn = #{sn}
        ORDER BY time DESC
        LIMIT 1
    </select>
    <select id="getInverterDetails" resultType="entity.BtoDevice">
        SELECT sn,
               time,
               ipv1,
               ipv2,
               ipv3,
               vpv1,
               vpv2,
               vpv3,
               iac1,
               iac2,
               iac3,
               vac1,
               vac2,
               vac3,
               fac,
               fac1,
               fac2,
               today_energy,
               total_energy,
               power
        FROM ${tableName}
        WHERE sn = #{sn}
          AND time LIKE #{time}
        ORDER BY time DESC
        LIMIT 1
    </select>
    <select id="selectListt" resultType="entityDTO.StationLunBo">
        SELECT `name`            name,
               address1          address1,
               create_date       createDate,
               sum(power)        power,
               sum(today_energy) todayenergy,
               sum(total_energy) totalenergy

        FROM `v1_station_device` a
                 LEFT JOIN (SELECT plant_id,
                                   power,
                                   today_energy,
                                   total_energy,
                                   time
                            FROM v_device_list aa
                                     LEFT JOIN (SELECT sn, power, today_energy, total_energy, time
                                                FROM bto_device_${time}
                                                WHERE time LIKE '#{time2}%'
                                                GROUP BY SN, time
                                                ORDER BY time desc) bb
                                               ON aa.device_sn = bb.sn
                            WHERE sn is not null
                            GROUP BY sn) b ON a.plant_id = b.plant_id
        where a.special = '1'
        group by a.plant_id
        ;
    </select>


    <select id="getNewDayPowerBySn" resultMap="getDayPowerBySnMap">
        SELECT power,
               concat(LEFT(time, 15), '0:00') time
        FROM ${tableName}
        WHERE sn = #{sn}
    </select>

    <select id="getNewMonthElectricitySn" resultMap="MonthElectricityVoMap">
        SELECT date,
               energy
        FROM v_day
        WHERE date LIKE #{time}
          AND plant_id = #{stationId}
        ORDER BY date
    </select>
    <select id="getStationDetailsHome" resultType="vo.StationDetailsHomeVo">
        SELECT plant_id,
               time,
               sum(DISTINCT (power))        power,
               sum(DISTINCT (today_energy)) today_energy,
               sum(DISTINCT (total_energy)) total_energy
        FROM (SELECT plant_id,
                     sn,
                     time,
                     power,
                     today_energy,
                     total_energy
              FROM (SELECT plant_id,
                           sn,
                           time,
                           power,
                           today_energy,
                           total_energy
                    FROM v_sn_hy a
                             LEFT JOIN ${tableName} b ON b.sn = a.device_sn
                    WHERE plant_id = #{stationId}
                    GROUP BY sn,
                             time
                    ORDER BY time DESC) q
              GROUP BY sn) w
        GROUP BY plant_id
    </select>
    <select id="getDeviceDetails" resultType="entity.BtoDevice">
        SELECT a.sn,
               power,
               a.today_energy,
               a.total_energy,
               a.ipv1,
               a.ipv2,
               a.ipv3,
               a.ipv4,
               a.ipv5,
               a.ipv6,
               a.ipv7,
               a.ipv8,
               a.ipv9,
               a.vpv1,
               a.vpv2,
               a.vpv3,
               a.vpv4,
               a.vpv5,
               a.vpv6,
               a.vpv7,
               a.vpv8,
               a.vpv9,
               a.iac1,
               a.iac2,
               a.iac3,
               a.iac4,
               a.iac5,
               a.iac6,
               a.iac7,
               a.iac8,
               a.iac9,
               a.vac1,
               a.vac2,
               a.vac3,
               a.vac4,
               a.vac5,
               a.vac6,
               a.vac7,
               a.vac8,
               a.vac9,
               a.fac,
               a.fac1,
               a.fac2,
               a.fac3,
               a.fac4,
               a.fac5,
               a.fac6,
               a.fac7,
               a.fac8,
               CONCAT(b.pv1_dc1, ' - ', b.pv1_dc2, ' - ', b.pv1_dc3, ' - ', b.pv1_dc4) pv1,
               CONCAT(b.pv2_dc1, ' - ', b.pv2_dc2, ' - ', b.pv2_dc3, ' - ', b.pv2_dc4) pv2,
               CONCAT(b.pv3_dc1, ' - ', b.pv3_dc2, ' - ', b.pv3_dc3, ' - ', b.pv3_dc4) pv3,
               CONCAT(b.pv4_dc1, ' - ', b.pv4_dc2, ' - ', b.pv4_dc3, ' - ', b.pv4_dc4) pv4,
               CONCAT(b.pv5_dc1, ' - ', b.pv5_dc2, ' - ', b.pv5_dc3, ' - ', b.pv5_dc4) pv5,
               CONCAT(b.pv6_dc1, ' - ', b.pv6_dc2, ' - ', b.pv6_dc3, ' - ', b.pv6_dc4) pv6,
               CONCAT(b.pv7_dc1, ' - ', b.pv7_dc2, ' - ', b.pv7_dc3, ' - ', b.pv7_dc4) pv7,
               CONCAT(b.pv8_dc1, ' - ', b.pv8_dc2, ' - ', b.pv8_dc3, ' - ', b.pv8_dc4) pv8,
               CONCAT(b.pv9_dc1, ' - ', b.pv9_dc2, ' - ', b.pv9_dc3, ' - ', b.pv9_dc4) pv9,
               a.time
        FROM ${tableName} a
                 LEFT JOIN bto_device_modules b ON a.sn = b.sn
        WHERE a.sn = #{sn}
        ORDER BY time DESC
        LIMIT 1
    </select>
    <select id="getDeviceVersionInfo" resultType="vo.VersionInfoVo">
        SELECT a.plant_id,
               model,
               device_sn,
               c.wisdom_device_sn,
               MASTER,
               display,
               SLAVE,
               c_user_name
        FROM v_device_list a
                 LEFT JOIN v_user_list b ON b.c_user_uid = a.user_uid
                 LEFT JOIN v_station_list c ON a.plant_id = c.plant_id
                 LEFT JOIN bto_version d ON a.device_sn = d.sn
        WHERE device_sn = #{sn}
    </select>
    <select id="getDeviceAlertInfo" resultType="vo.DeviceAlertVo">
        SELECT a.sn,
               mean,
               alarm_code,
               start_time,
               grade,
               NAME,
               name1,
               tel
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE b.special = '1'
          AND plant_id = #{stationId}
           <!--AND start_time LIKE #{time}-->
        ORDER BY a.start_time DESC
    </select>

    <select id="selectDeviceByStation" resultType="java.lang.String">
        SELECT sn
        FROM v1_station_device
        WHERE plant_id = #{stationId}
    </select>
    <select id="getElectricityBySnToOne" resultType="entity.BtoDevice">
        SELECT sn,
               power,
               today_energy,
               month_energy,
               year_energy,
               total_energy,
               time
        FROM ${tableName}
        WHERE sn = #{sn}
          AND time LIKE #{time}
        ORDER BY time DESC
        LIMIT 1
    </select>
    <select id="getEnergy" resultType="Double">
        SELECT sum(energy)
        FROM bto_station_day
        WHERE plant_id = #{plantId}AND
        <if test="timeType=='day'.toString ">
            date = #{time}
        </if>
        <if test="timeType=='month'.toString ">
            left(date,7)=#{time}
        </if>
        <if test="timeType=='year'.toString ">
            left(date,4)=#{time}
        </if>
    </select>
    <select id="getTotalEnergy" resultType="Double">
        SELECT sum(DISTINCT (total_energy))
        FROM bto_device_total
        WHERE plant_id = #{plantId}
        GROUP BY plant_id
    </select>
    <select id="getDeviceChart" resultType="com.botong.services.vo.DayPowerVo">
        SELECT
        sn,
        power,
        today_energy todayEnergy,
        concat( LEFT ( time, 15 ), '0:00' ) dateTime
        FROM
        ${tableName}
        WHERE
        sn IN
        <foreach collection="SNs" item="sn" separator="," open="(" close=")">
            #{sn}
        </foreach>
        AND LEFT(time,10) = #{time}
    </select>
    <select id="getDateTimeList" resultType="java.lang.String">
        <!--        SELECT
                CONCAT( LEFT ( time, 15 ), '0:00' ) dateTime
                FROM
                ${tableName}
                WHERE
                sn IN
                <foreach collection="SNs" item="sn" separator="," open="(" close=")">
                    #{sn}
                </foreach>
                GROUP BY DATETIME
                ORDER BY DATETIME-->
        SELECT
        t2.time dateTime
        FROM
        bto_device_list t1
        LEFT JOIN ${tableName} t2 ON t1.device_sn = t2.sn
        WHERE
        t1.plant_id = #{plantId}
        AND DATE_FORMAT(t2.time,'%Y-%m-%d')=#{dateTime}
        GROUP BY
        t2.time
        ORDER BY
        t2.time
    </select>
    <select id="selectTotalPower" resultType="java.lang.String">
        SELECT sum(t2.power)
        FROM bto_device_list t1
                 LEFT JOIN ${tableName} t2 ON t1.device_sn = t2.sn
        WHERE t1.plant_id = #{plantId}
          AND LEFT(t2.time, 10) = curdate()
        GROUP BY t2.time
        ORDER BY t2.time
    </select>
    <select id="selectStatusByDeviceSN" resultType="java.lang.String">
        SELECT device_status status
        FROM bto_device_list
        WHERE device_sn = #{deviceSn}
    </select>
    <select id="getWisdomALarmInfo" resultType="vo.OperatorInfoVO">
        SELECT t1.plant_uid        plantUid,
               t1.name             stationName,
               t1.wisdom_device_sn imei,
               t2.alarm_name       alarmName,
               t2.alarm_time       alarmTime,
               t2.a_point_voltage  aPointVoltage,
               t2.b_point_voltage  bPointVoltage,
               t2.c_point_voltage  cPointVoltage,
               t2.update_time      updateTime
        FROM bto_station_list t1
                 LEFT JOIN bto_operation_fault t2
                           ON t1.plant_uid = t2.plant_uid
        WHERE t2.PLANT_UID = #{plantUid}
        <!--AND t2.ALARM_TIME LIKE #{time}-->
        ORDER BY t2.ALARM_TIME DESC
    </select>


</mapper>