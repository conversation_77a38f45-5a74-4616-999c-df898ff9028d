package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.IncomeStatisticDTO;


public interface IncomeStatisticService extends IService<IncomeStatisticDTO> {


    PageInfo<IncomeStatisticDTO> IncomeStatisticsDay( String plant_id, String time1,String time2,Integer page,Integer pageSize);

    PageInfo<IncomeStatisticDTO> IncomeStatisticsAllDay( String time1,String time2,Integer page,Integer pageSize);
    PageInfo<IncomeStatisticDTO> IncomeStatisticsMonth(  String plant_id, String time,Integer page,Integer pageSize);

    PageInfo<IncomeStatisticDTO> IncomeStatisticsAllMonth(String time,Integer page,Integer pageSize);

    PageInfo<IncomeStatisticDTO> IncomeStatisticsYear( String plant_id, String time,Integer page,Integer pageSize);
    PageInfo<IncomeStatisticDTO> IncomeStatisticsAllYear(String time,Integer page,Integer pageSize);
}
