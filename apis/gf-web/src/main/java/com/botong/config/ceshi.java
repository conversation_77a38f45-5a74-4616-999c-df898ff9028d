package com.botong.config;

public class ceshi {
    public static String getNumber(Integer length) {
        String uid = "";
        for (Integer i = 0; i < length; i++) {
            String randChar = String.valueOf(Math.round(Math.random() * 9));
            uid = uid.concat(randChar);
        }
        return uid;
    }

    public static void main(String[] args) {
        System.out.println(ceshi.getNumber(13));
    }
}
