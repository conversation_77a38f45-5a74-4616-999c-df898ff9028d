package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.VStationBase;

import java.util.List;

public interface VStationBaseService extends IService<VStationBase> {
    PageInfo<VStationBase> selectAll(Integer page, Integer pageSize);

    PageInfo<VStationBase> Likeselect(Integer page, Integer pageSize, String a);

    int add(VStationBase vStationBase);

    int update(VStationBase vStationBase);

    int delete(String plantUid);
}
