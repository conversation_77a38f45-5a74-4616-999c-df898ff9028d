<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.VStationBaseDTOMapper">
    <resultMap type="entityDTO.VStationBaseDTO" id="vStationBaseDTOMap">
        <result property="plantId" column="plantId"/>
        <result property="plantUid" column="plantUid"/>
        <result property="name" column="name"/>
        <result property="peakPower" column="peakPower"/>
        <result property="todayEnergySum" column="todayEnergySum"/>
        <result property="hour" column="hour"/>
        <result property="eta" column="eta"/>
        <result property="power" column="power"/>
        <result property="self" column="self"/>
        <result property="sellTel" column="selltel"/>
        <result property="sign" column="sign"/>
    </resultMap>
<select id="selectStationBase" resultMap="vStationBaseDTOMap">
    SELECT
        a.plant_id AS plantId,
        a.plant_uid AS plantUid,
        NAME,
        peak_power,
        sum( today_energy ) AS todayEnergySum,
        FORMAT( sum( today_energy )/ peak_power, 4 ) AS HOUR,
FORMAT( sum( power ), 0 ) AS eta,
FORMAT(( sum( power )/ 1000 )/ peak_power, 4 ) AS power,
c.auto_tel AS self,
c.sell_tel AS selltel,
a.sign as sign
    FROM
        `v_station_base` a
        LEFT JOIN (
        SELECT
        plant_id,
        sn,
        power,
        today_energy,
        time
        FROM
        v_device_list aa
        LEFT JOIN ( SELECT sn, power, today_energy, time FROM ${table} WHERE time LIKE #{time} GROUP BY SN, time ORDER BY time DESC ) bb ON aa.device_sn = bb.sn
        GROUP BY
        sn
        ) b ON a.plant_id = b.plant_id
        LEFT JOIN ( SELECT plant_uid, auto_tel, sell_tel, concat( chart_month, '-', `day` ) time FROM bto_operation_day HAVING time LIKE #{time} ) c ON a.plant_uid = c.plant_uid
    GROUP BY
        a.plant_id


</select>



    <select id="selectStationBasePx" resultMap="vStationBaseDTOMap">
        SELECT
            a.plant_id AS plantId,
            a.plant_uid AS plantUid,
            NAME,
            peak_power peakPower,
            sum( today_energy ) AS todayEnergySum,
            FORMAT( sum( today_energy )/ peak_power, 4 ) AS HOUR,
FORMAT( sum( power ), 0 ) AS eta,
FORMAT(( sum( power )/ 1000 )/ peak_power, 4 ) AS power,
c.auto_tel AS self,
c.sell_tel AS selltel,
a.sign as sign
        FROM
            `v_station_base` a
            LEFT JOIN (
            SELECT
            plant_id,
            sn,
            power,
            today_energy,
            time
            FROM
            v_device_list aa
            LEFT JOIN ( SELECT sn, power, today_energy, time FROM ${table} WHERE time LIKE #{time} GROUP BY SN, time ORDER BY time DESC ) bb ON aa.device_sn = bb.sn
            GROUP BY
            sn
            ) b ON a.plant_id = b.plant_id
            LEFT JOIN ( SELECT plant_uid, auto_tel, sell_tel,  time FROM bto_operation_day HAVING time LIKE #{time} ) c ON a.plant_uid = c.plant_uid
        GROUP BY
            a.plant_id
          ${px}


    </select>


    <update id="updateStation1" parameterType="String">
        update v_station_base set sign = "1"  where plant_id = #{plantId};
    </update>
    <update id="updateStation0" parameterType="String">
        update v_station_base set sign = "0" ;
    </update>

    <select id="selectStationBaseSign" resultMap="vStationBaseDTOMap">
        SELECT
            a.plant_id AS plantId,
            a.plant_uid AS plantUid,
            NAME,
            peak_power,
            sum( today_energy ) AS todayEnergySum,
            FORMAT( sum( today_energy )/ peak_power, 4 ) AS HOUR,
	FORMAT( sum( power ), 0 ) AS eta,
	FORMAT(( sum( power )/ 1000 )/ peak_power, 4 ) AS power,
	c.auto_tel AS self,
	c.sell_tel AS selltel,
	a.sign AS sign
        FROM
            `v_station_base` a
            LEFT JOIN (
            SELECT
            plant_id,
            sn,
            power,
            today_energy,
            time
            FROM
            v_device_list aa
            LEFT JOIN ( SELECT sn, power, today_energy, time FROM ${table} WHERE time LIKE #{time} GROUP BY SN, time ORDER BY time DESC ) bb ON aa.device_sn = bb.sn
            GROUP BY sn ) b ON a.plant_id = b.plant_id
            LEFT JOIN (
            SELECT
            plant_uid,
            auto_tel,
            sell_tel,
            concat( chart_month, '-', `day` ) time
            FROM
            bto_operation_day
            HAVING
            time LIKE #{time} ) c ON a.plant_uid = c.plant_uid

        where sign = '1'
    </select>


    <select id="selectStationBasePlantId" resultMap="vStationBaseDTOMap">
        SELECT
            a.plant_id AS plantId,
            a.plant_uid AS plantUid,
            NAME,
            peak_power,
            sum( today_energy ) AS todayEnergySum,
            FORMAT( sum( today_energy )/ peak_power, 4 ) AS HOUR,
	FORMAT( sum( power ), 0 ) AS eta,
	FORMAT(( sum( power )/ 1000 )/ peak_power, 4 ) AS power,
	c.auto_tel AS self,
	c.sell_tel AS selltel,
	a.sign AS sign
        FROM
            `v_station_base` a
            LEFT JOIN (
            SELECT
            plant_id,
            sn,
            power,
            today_energy,
            time
            FROM
            v_device_list aa
            LEFT JOIN ( SELECT sn, power, today_energy, time FROM ${table} WHERE time LIKE #{time} GROUP BY SN, time ORDER BY time DESC ) bb ON aa.device_sn = bb.sn
            GROUP BY sn ) b ON a.plant_id = b.plant_id
            LEFT JOIN (
            SELECT
            plant_uid,
            auto_tel,
            sell_tel,
            time
            FROM
            bto_operation_day
            HAVING
            time LIKE #{time} ) c ON a.plant_uid = c.plant_uid

        where a.plant_id = #{plantId}
    </select>
    <select id="VStationBaseDTOPx2" resultMap="vStationBaseDTOMap">
    SELECT
            name name,
            a.plant_id plantid,
            peak_power peakpower,
            sign sign,
            (sum(DISTINCT ( today_energy ))/(4*peak_power)) hour,
            sum(DISTINCT ( power1 )) power,
            sum(DISTINCT ( today_energy )) todayenergy,
            sum( DISTINCT ( total_energy ) ) totalenergy
        FROM
            `v1_base_device` a
                LEFT JOIN (
                SELECT
                    plant_id,
                    power power1,
                    today_energy,
                    total_energy,
                    time
                FROM
                    v_device_list aa
                    LEFT JOIN (
                    SELECT sn, power, today_energy, total_energy, time FROM bto_device_${time} GROUP BY SN, time ORDER BY time DESC
                    ) bb ON aa.device_sn = bb.sn
                WHERE
                    sn IS NOT NULL
                GROUP BY
                    sn

            ) b ON a.plant_id = b.plant_id
        GROUP BY
            plantid
        ORDER BY
        ${param} ${sort}
    </select>
    <select id="selectVStationBaseDTOPx2XcCount" resultType="java.lang.Object">
        SELECT  COUNT(DISTINCT (plant_id)) from v_station_base
    </select>

</mapper>