package com.botong.controller;

import com.botong.services.service.UserAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("userlogion")
@Api(tags = "app接口")
public class APPfrontPageController {
    @Autowired
    private com.botong.services.service.userlogionService userlogionService;
    @Autowired
    private UserAppService userAppService;

    @ApiOperation("用户APP首页")
    @GetMapping("indexPage")
    public AjaxResult indexPage(String userUID) {
        return AjaxResult.success("操作成功", userAppService.selectIndexPage(userUID));
    }
    @ApiOperation("版本查看")
    @GetMapping("versionView")
    public AjaxResult versionView() {
        return AjaxResult.success("版本查询成功", userlogionService.versionView());
    }

}
