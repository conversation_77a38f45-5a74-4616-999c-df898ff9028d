package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.AreaDeviceDTO;
import entityDTO.AreaStatisticsForm;
import entityDTO.VBaseDeviceDTO;

import java.util.List;


public interface HYVBaseDeviceDTOService extends IService<VBaseDeviceDTO> {
//    PageInfo<AreaDeviceDTO> selectDay(String time, String town, String address , Integer page, Integer pageSize);
//
//    PageInfo<AreaDeviceDTO> selectMonth(String month, String town ,Integer page,Integer pageSize);
//
//    PageInfo<AreaDeviceDTO> selectYear(String year, String town, Integer page,Integer pageSize);

    /**
     * 区域统计——查询日月年数据--三合一
     * @param areaStatisticsForm
     * @return
     */
    PageInfo<List<AreaDeviceDTO>> areaStatistics(AreaStatisticsForm areaStatisticsForm);
}
