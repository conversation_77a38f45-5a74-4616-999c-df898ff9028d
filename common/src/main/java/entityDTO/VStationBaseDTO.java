package entityDTO;

import lombok.Data;

@Data
public class VStationBaseDTO {

    /*
    * 电站id
    * */
    private String plantId;
    /*
     * 电站uid
     * */
    private String plantUid;
    /*
     * 站点名称
     * */
    private String name;
    /*
     * 装机容量
     * */
    private String peakPower;
    /*
     * 发电量
     * */
    private String todayEnergySum;
    /*
     * 日等效小时数
     * */
    private String hour;
    /*
     * 实时功率
     * */
    private String eta;
    /*
     * 工作效率
     * */
    private String power;
    /*
     * 买电量
     * */
    private String self;
    /*
     * 卖电量
     * */
    private String sellTel;

    /*
    * 标杆状态
    * */
    private String sign;
}
