# 应用服务 WEB 访问端口
server:
  port: 8802
dubbo:
  registry:
    # 挂载到 Spring Cloud 注册中心
    address: spring-cloud://localhost
  cloud:
    # 订阅服务提供方的应用列表，订阅多个服务提供者使用 "," 连接
    subscribed-services: services
security:
  oauth2:
    client:
      client-id: user-client
      client-secret: user-secret-8888
      user-authorization-uri: http://localhost:8804/oauth/authorize
      access-token-uri: http://localhost:8804/oauth/token
      access-token-validity-seconds: 43200
    resource:
      id: user-client
      user-info-uri: user-info
    authorization:
      check-token-access: http://localhost:8804/oauth/check_token
spring:
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    timeout: 5000
    password: botongredis666
  profiles:
    #dev开发，test测试，prod生产
    active: dev
  application:
    name: gf-app # 应用名称
  cloud:
    # Nacos帮助文档: https://nacos.io/zh-cn/docs/concepts.html
    # Nacos认证信息
    nacos:
      discovery:
        # Nacos 服务发现与注册配置，其中子属性 server-addr 指定 Nacos 服务器主机和端口
        server-addr: 127.0.0.1:8848
        service: gf-app
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

swagger:
  production: false
  basic:
    enable: true
    username: admin
    password: botongapi666

