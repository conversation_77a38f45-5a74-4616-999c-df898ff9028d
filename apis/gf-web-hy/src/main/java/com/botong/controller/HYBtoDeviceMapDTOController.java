package com.botong.controller;

import com.botong.services.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("/BtoDeviceMapDTO")
@Api(tags = "光伏分析")
@CrossOrigin
public class HYBtoDeviceMapDTOController {

    @Autowired
    private HYBtoDeviceMapDTOService hybtoDeviceMapDTOService;
    @Autowired
    private VDayService vDayService;
    @Autowired
    private VStationListService vStationListService;
    @Autowired
    private VStationBaseDTOService vStationBaseDTOService;


    @ApiOperation("发电统计——查询所有逆变器信息-列表")
    @GetMapping("/selectAll")
    public AjaxResult selectAll(String plantId ){
        return AjaxResult.success("查询成功", hybtoDeviceMapDTOService.selectBtoDeviceMap(plantId));
    }

    @ApiOperation("发电统计——查询日指定数据")
    @GetMapping("/selectDay")
    public AjaxResult selectDay(String plantId , String sn, String day){
        return AjaxResult.success("查询成功", hybtoDeviceMapDTOService.selectBtoDeviceMapDay(plantId,sn ,day));
    }


    @ApiOperation("发电统计——查询月发电量")
    @GetMapping("/selectMonth")
    public AjaxResult selectMonth(String plantId) {
        return AjaxResult.success("查询成功！",vDayService.selectMonth(plantId));
    }

    @ApiOperation("发电统计——查询指定月发电量")
    @GetMapping("/selectMonthAll")
    public AjaxResult selectMonthAll(String plantId , String date) {
        return AjaxResult.success("查询成功！",vDayService.selectMonthAll(plantId , date));
    }

    @ApiOperation("发电统计——查询指定年发电量")
    @GetMapping("/selectYearAll")
    public AjaxResult selectYearAll(String plantId) {
        return AjaxResult.success("查询成功！" ,vDayService.selectYear(plantId));
    }

    @ApiOperation("收益统计——查询收益统计-日")
    @GetMapping("/selectYieldDay")
    public AjaxResult selectYieldDay(String plantId) {
        return AjaxResult.success("查询成功！" ,vDayService.selectYieldDay(plantId));
    }

    @ApiOperation("收益统计——查询指定收益统计-日")
    @GetMapping("/selectYieldDayAll")
    public AjaxResult selectYieldDayAll(String plantId , String day) {
        return AjaxResult.success("查询成功！" ,vDayService.selectYieldDayAll(plantId, day));
    }

    @ApiOperation("收益统计——查询收益统计-月")
    @GetMapping("/selectYieldMonthAll")
    public AjaxResult selectYieldMonthAll(String plantId ) {
        return AjaxResult.success("查询成功！" ,vDayService.selectYieldMonthAll(plantId));
    }

    @ApiOperation("收益统计——查询指定收益统计-月")
    @GetMapping("/selectYieldMonth")
    public AjaxResult selectYieldMonth(String plantId , String month) {
        return AjaxResult.success("查询成功！" ,vDayService.selectYieldMonth(plantId , month));
    }

    @ApiOperation("收益统计——查询收益统计指定-年")
    @GetMapping("/selectYieldYear")
    public AjaxResult selectYieldYear(String plantId , String year) {
        return AjaxResult.success("查询成功！" ,vDayService.selectYieldYear(plantId,year));
    }

    @ApiOperation("发电效率——查询当前月发电效率")
    @GetMapping("/selectVStationList")
    public AjaxResult selectVStationList(String plantId) {
        return AjaxResult.success("查询成功！", vStationListService.VStationListSelecEta(plantId));
    }

    @ApiOperation("发电效率——查询指定月发电效率")
    @GetMapping("/selectVStationListMonth")
    public AjaxResult selectVStationListMonth(String plantId, String month) {
        return AjaxResult.success("查询成功！", vStationListService.VStationListSelecEtaMonth(plantId,month));
    }


    @ApiOperation("发电效率——查询当前年发电效率")
    @GetMapping("/selectVStationListAll")
    public AjaxResult selectVStationListAll(String plantId) {
        return AjaxResult.success("查询成功！", vStationListService.VStationListSelecEtaSum(plantId));
    }


    @ApiOperation("发电效率——查询指定年发电效率")
    @GetMapping("/selectVStationListAllYear")
    public AjaxResult selectVStationListAllYear(String plantId, String year) {
        return AjaxResult.success("查询成功！", vStationListService.VStationListSelecEtaSumYear(plantId,year));
    }


    @ApiOperation("光伏对标——查询标杆配置")
    @GetMapping("/selectVStationBaseDTO")
    public AjaxResult VStationBaseDTO(Integer page,Integer pageSize,String time){
        return AjaxResult.success("查询成功！" , vStationBaseDTOService.selectVStationBaseDTO(page, pageSize, time));
    }

    @ApiOperation("光伏对标——查询标杆配置——排序")
    @GetMapping("/selectVStationBaseDTOPx")
    public AjaxResult VStationBaseDTOPx(Integer page,Integer pageSize,String time, String param, String sort){
        return AjaxResult.success("查询成功！" , vStationBaseDTOService.selectVStationBaseDTOSign(page, pageSize, time, param, sort));
    }

    @ApiOperation("光伏对标——修改标杆配置状态")
    @GetMapping("/updateVStationBaseDTO")
    public AjaxResult updateVStationBaseDTO(String plantId){
        return AjaxResult.success(  vStationBaseDTOService.updateVStationBase(plantId));
    }


    @ApiOperation("光伏对标——对标分析——求标杆")
    @GetMapping("/SelectVStationBaseDTOSign")
    public AjaxResult SelectVStationBaseDTOSign(String time){
        return AjaxResult.success(  vStationBaseDTOService.selectStationBaseSign(time));
    }

    @ApiOperation("光伏对标——对标分析——标杆对比的电站")
    @GetMapping("/SelectVStationBaseDTOplantId")
    public AjaxResult SelectVStationBaseDTOplantId(String time,String[] plantIds){
        return AjaxResult.success(  vStationBaseDTOService.selectStationBasePlantId(time,plantIds));
    }

}
