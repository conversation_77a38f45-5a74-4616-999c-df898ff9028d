package com.botong.services.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.AlarmAnalysisMapper;
import com.botong.services.mapper.HYAlarmAnalysisMapper;
import com.botong.services.service.HYAlarmAnalysisService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.HyStationAlarmInfo;
import entityDTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import utils.AjaxResult;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class HYAlarmAnalysisServiceImpl extends ServiceImpl<AlarmAnalysisMapper, AlarmAnalysis> implements HYAlarmAnalysisService {
    @Autowired
    private HYAlarmAnalysisMapper hyalarmAnalysisMapper;

    //    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisListForm(Integer page, Integer pageSize) {
//        QueryWrapper<Object> objectQueryWrapper = new QueryWrapper<>();
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        Date date = new Date();
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        String time = simpleDateFormat.format(date);
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.selectListt(page, pageSize, time);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery 实时报警——时间区间查询——精确时分秒 plantUid time 非全
//    //// GET /AlarmAnalysis/AlarmAnalysisTimeIntervalQuery time plantUid全
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisTimeIntervalQuery(String time1, String time2, int page, int pagesize,String plantUid) {
//        String[] split = plantUid.split(",");
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pagesize));
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisTimeIntervalQuery(page, pagesize, time1, time2,Arrays.asList(split));
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisTconditionQuery(String startTime, int page, int pagesize, String name, String name1, String sn) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pagesize));
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisTconditionQuery(page, pagesize, startTime, name,name1,sn);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisPolymerization( String page, String pageSize,String plantUid) {
//
//        String[] split = plantUid.split(",");
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisPolymerization(page, pageSize,Arrays.asList(split));
//            PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//            return stationLunBoPageInfo;
//    }
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarm(String startTime, String endTime, String page, String pageSize,int grade) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarm(page, pageSize,endTime, startTime,grade);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisListFormplantUid(Integer page, Integer pageSize, String plantUid) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> stationLunBos = hyalarmAnalysisMapper.AlarmAnalysisListFormplantUid(page, pageSize, plantUid);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }

    //
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll(String startTime, String endTime, String page, String pageSize, int grade, String plantUid) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmAll(startTime,endTime,page, pageSize,plantUid,grade);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//    @Override
//    public List<AlarmAnalysis> AlarmAnalysisTimeIntervalAll(String time1, String time2) {
////        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
// //       List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisTimeIntervalAll(time1,time2);
////        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
////        return stationLunBoPageInfo;
//        return alarmAnalysisMapper.AlarmAnalysisTimeIntervalAll(time1,time2);
//    }
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll3(Integer page, Integer pageSize,String name1,String startTime,String endTime,String grade) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        if (name1==""){
//            name1="%";
//        }
//        if (startTime==""){
//            startTime="%";
//        }
//        if (endTime==""){
//            endTime="%";
//        }
//        if (grade==""){
//            grade= "%";
//        }
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmAll3(page,pageSize,name1,startTime,endTime,grade);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmQuann(String page, String pageSize) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmQuann(page, pageSize);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisPolymerizationplantUid(String startTime, String endTime, String page, String pageSize, String plantUid) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<String> list = new ArrayList<>();
//        if(plantUid!=null &&!"".equals(plantUid)){
//            String[] array = plantUid.split(",");
//            if(array!=null&&array.length>0){
//                list = Arrays.asList(array);
//            }
//        }
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisPolymerizationplantUid(startTime,endTime, list);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//    @Override
//    public Object totalAlarmStatistics() {
//        return alarmAnalysisMapper.totalAlarmStatistics();
//    }
//
//    @Override
//    public Object totalAlarmStatisticsTime(String time) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTime(time);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIME(String time, String plantUid) {
//        return baseMapper.totalAlarmStatisticsTIME(time,plantUid);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMETU(String time) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMETU(time);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgeren(String time, String plantUid) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgeren(time,plantUid);
//
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenyaoxin(String time) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerenyaoxin(time);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennyaoxin(String time, String plantUid) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerennyaoxin(time,plantUid);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEstatistics(String time) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEstatistics(time);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennstatistics(String time, String plantUid) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerennstatistics(time,plantUid);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEEquipmentStatistics(String time) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEEquipmentStatistics(time);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenEquipmentStatistics(String time, String plantUid) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerenEquipmentStatistics(time,plantUid);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMELevelStatistics(String time) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMELevelStatistics(time);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennLevelStatistics(String time, String plantUid) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerennLevelStatistics(time,plantUid);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEyaoxinlist(String time) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEyaoxinlistt(time);
//    }
//
//    @Override
//    public List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenlist(String time, String plantUid) {
//        return alarmAnalysisMapper.totalAlarmStatisticsTIMEgerenlist(time,plantUid);
//    }
//

    //    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll2(Integer page, Integer pageSize, String name1, String grade) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        if (name1==""){
//            name1="%";
//        }
//        if (grade==""){
//            grade= "%";
//        }
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisHistoryAlarmAll2(page,pageSize,name1,grade);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
//
//
//    @Override
//    public PageInfo<AlarmAnalysis> AlarmAnalysisPolymerizationAll(String startTime, String endTime, String page, String pageSize) {
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
//        List<AlarmAnalysis> stationLunBos = alarmAnalysisMapper.AlarmAnalysisPolymerizationAll(page, pageSize,endTime, startTime);
//        PageInfo<AlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
//        return stationLunBoPageInfo;
//    }
    @Override
    public AjaxResult AlarmAnalysisByCondition(AlarmAnalysisConditionDTO alarmAnalysisConditionDTO) throws ClassNotFoundException, NoSuchFieldException {
        //获取前端传递的页码page和页面大小pageSize
        String page = alarmAnalysisConditionDTO.getPage();
        String pageSize = alarmAnalysisConditionDTO.getPageSize();
        //获取查询类型：0:列表查询 1:聚合查询 2:自检提示
        Integer type = alarmAnalysisConditionDTO.getType();
        //获取查询时间:   开始时间和结束时间 需要进行拼接
        String startTime = alarmAnalysisConditionDTO.getStartTime().concat(" 00:00:00");
        String endTime = alarmAnalysisConditionDTO.getEndTime().concat(" 23:59:59");
        //获取查询条件：电站UID数组、告警信息、告警级别、事件描述、告警状态
        String[] plantUidArray = alarmAnalysisConditionDTO.getPlantUid();
        String alarmMessage = alarmAnalysisConditionDTO.getAlarmMessage();
        String grade = alarmAnalysisConditionDTO.getGrade();
        String mean = alarmAnalysisConditionDTO.getMean();
        String status = alarmAnalysisConditionDTO.getStatus();
        String address = alarmAnalysisConditionDTO.getAddress();
        //聚合条件传字符串数组：原因是传字符串数组才能确定聚合顺序
        List<HYAlarmAnalysis> alarmAnalyseList = new ArrayList<>();
        if (type == 0) {
            PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(pageSize));
            alarmAnalyseList = hyalarmAnalysisMapper.AlarmByConditionOnListQuery(startTime, endTime, plantUidArray, alarmMessage, grade, mean, status, address,type);
            PageInfo<HYAlarmAnalysis> alarmAnalyseListInfo = new PageInfo<>(alarmAnalyseList);
            return AjaxResult.success("操作成功", alarmAnalyseListInfo);
        } else if (type == 1) {
            alarmAnalyseList = hyalarmAnalysisMapper.AlarmByConditionOnAggregation(startTime, endTime, plantUidArray);
            List<List<HYAlarmAnalysis>> alarmAnalyseLists = CollStreamUtil.groupByKey(alarmAnalyseList, HYAlarmAnalysis::getPlantUid).values().stream().collect(Collectors.toList());
            PageInfo pageInfo = pageForList(alarmAnalyseLists, page, pageSize);
            return AjaxResult.success("操作成功", pageInfo);
        } else if(type == 2){
            PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(pageSize));
            List<HYAlarmAnalysis> selfCheckList = hyalarmAnalysisMapper.AlarmByConditionOnListQuery(startTime, endTime, plantUidArray, alarmMessage, grade, mean, status, address,type);
//            List<HYAlarmAnalysis> resultList = selfCheckList.stream().filter(hyAlarmAnalysis -> {
//                String startTimeStr = hyAlarmAnalysis.getStartTime();
//                String updateTimeStr = hyAlarmAnalysis.getUpdateTime();
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                long timeMillis = 0L;
//                try {
//                    long startTimeMilli = sdf.parse(startTimeStr).getTime();
//                    long updateTimeMilli = sdf.parse(updateTimeStr).getTime();
//                    timeMillis = updateTimeMilli - startTimeMilli;
//                } catch (ParseException e) {
//                    e.printStackTrace();
//                }
//                return timeMillis > (20 * 60 * 1000);
//            }).collect(Collectors.toList());
            PageInfo<HYAlarmAnalysis> selfCheckListInfo = new PageInfo<>(selfCheckList);
            return AjaxResult.success("操作成功", selfCheckListInfo);
        }else {
            return AjaxResult.error("未知异常,请联系服务端工程师！");
        }
    }

    @Override
    public AjaxResult AlarmHistoryByCondition(AlarmHistoryConditionDTO alarmHistoryConditionDTO) {
        //获取前端传递的页码page和页面大小pageSize
        String page = alarmHistoryConditionDTO.getPage();
        String pageSize = alarmHistoryConditionDTO.getPageSize();
        //获取查询时间:   开始时间和结束时间 需要进行拼接
        String startTime = alarmHistoryConditionDTO.getStartTime().concat(" 00:00:00");
        String endTime = alarmHistoryConditionDTO.getEndTime().concat(" 23:59:59");
        //获取查询条件：电站名字符串
        String stationName = alarmHistoryConditionDTO.getStationName();
        String grade = alarmHistoryConditionDTO.getGrade();
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        List<HYAlarmAnalysis> alarmAnalyseList = hyalarmAnalysisMapper.AlarmHistoryByConditionOnListQuery(startTime, endTime, stationName, grade);
        PageInfo<HYAlarmAnalysis> alarmAnalyseListInfo = new PageInfo<>(alarmAnalyseList);

        return AjaxResult.success("操作成功", alarmAnalyseListInfo);
    }


    @Override
    public void alarmHandler(AlarmHandlerDTO alarmHandlerDTO) {
        //将remarks备注的内容拼接在电站名后,然后更新remark用来更新电站名
        String remarks = alarmHandlerDTO.getRemarks().trim();
        if (remarks != null && !"".equals(remarks)) {
            String concatName = alarmHandlerDTO.getStationName().concat("(" + remarks + ")");
            alarmHandlerDTO.setRemarks(concatName);
        } else {
            alarmHandlerDTO.setRemarks(alarmHandlerDTO.getStationName());
            return;
        }
        String msg = "";
        Integer count1 = hyalarmAnalysisMapper.alarmHandler(alarmHandlerDTO);
        if (count1 > 0) {
            msg = msg.concat("告警表更新成功！");
        }
        Integer count2 = hyalarmAnalysisMapper.alarmHandlerByList(alarmHandlerDTO);
        if (count1 > 0) {
            msg = msg.concat("电站列表表更新成功！");
        }
        Integer count3 = hyalarmAnalysisMapper.alarmHandlerByBase(alarmHandlerDTO);
        if (count1 > 0) {
            msg = msg.concat("电站基础表更新成功！");
        }
        System.out.println(msg);
    }

    @Override
    public PageInfo<HYAlarmAnalysis> OperatorData(Integer page, Integer pageSize, String startTime, String endTime, String stationName) {
        PageHelper.startPage(page, pageSize);
        if ("".equals(stationName)) {
            stationName = "%";
        }
        List<HYAlarmAnalysis> stationLunBos = hyalarmAnalysisMapper.OperatorData(endTime, startTime, stationName);
        PageInfo<HYAlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<OperatorEvents> OperatorEventData(Integer page, Integer pageSize, String alarmTime, String address, String endTime) {
        PageHelper.startPage(page, pageSize);
        List<OperatorEvents> stationLunBoList = hyalarmAnalysisMapper.OperatorEventData(alarmTime, address, endTime);
        Date curDate = new Date();
        for (int i = 0; i < stationLunBoList.size(); i++) {
            String alarmEndTime = stationLunBoList.get(i).getEndtime();
            long endMillis = DateUtil.parse(alarmEndTime, "yyyy-MM-dd HH:mm:ss").getTime();
            long curMillis = curDate.getTime();
            String alarmRecoveryStatus = curMillis - endMillis < (10 * 60 * 1000 ) ? "0":"1";
            stationLunBoList.get(i).setAlarmRecoveryStatus(alarmRecoveryStatus);
        }
        PageInfo<OperatorEvents> stationLunBoPageInfo = new PageInfo<>(stationLunBoList);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<HYAlarmAnalysis> OperatorDataMonth(Integer page, Integer pageSize, String startTime, String endTime, String stationName) {
        PageHelper.startPage(page, pageSize);
        List<HYAlarmAnalysis> stationLunBos = hyalarmAnalysisMapper.OperatorDataMonth(endTime, startTime, stationName);
        PageInfo<HYAlarmAnalysis> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public PageInfo<HyStationAlarmInfo> stationAlarmInfoByPlantUid(Integer page, Integer pageSize, String plantUid) {
        PageHelper.startPage(page, pageSize);
        List<HyStationAlarmInfo> stationAlarmInfos = hyalarmAnalysisMapper.stationAlarmInfoByPlantUid(plantUid);
        return new PageInfo<>(stationAlarmInfos);
    }

    @Override
    public HashMap<String, Integer> getCurDayAlarmNum(String plantId) {
        HashMap<String, Integer> hashMap = hyalarmAnalysisMapper.getCurDayAlarmNum(plantId);
        return hashMap;
    }

    /**
     * 封装对List集合进行分页的方法
     * @param alarmAnalyseLists
     * @param page
     * @param pageSize
     * @return
     */
    public PageInfo pageForList(List<List<HYAlarmAnalysis>> alarmAnalyseLists, String page, String pageSize) {
        //计算当前需要显示的数据下标起始值
        int  pageNum = Integer.parseInt(page);
        int pageSize2 = Integer.parseInt(pageSize);
        int startIndex = (pageNum - 1) * pageSize2;
        int endIndex = Math.min(startIndex + pageSize2, alarmAnalyseLists.size());
        //从链表中截取需要显示的子链表，并加入到Page
        PageInfo pageInfo = new PageInfo(alarmAnalyseLists.subList(startIndex, endIndex));
        pageInfo.setTotal(alarmAnalyseLists.size());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize2);
        return pageInfo;
    }
}
