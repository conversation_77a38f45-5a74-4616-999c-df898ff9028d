package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYVEventMeanMapper;
import com.botong.services.service.HYVEventMeanService;
import entity.VEventMean;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class HYVEventMeanServiceImpl extends ServiceImpl<HYVEventMeanMapper, VEventMean> implements HYVEventMeanService {

    @Resource
    private HYVEventMeanMapper hyvEventMeanMapper;

/*    @Override
    @Cacheable(cacheNames = "HY_WEB",key = " 'selectCountDay' ",cacheManager = "HY_Web_Cache")
    public int selectCountDay(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time = "%" + format + "%";
        int i = hyvEventMeanMapper.selectCountDay(time);
        return i;
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB",key = " 'selectCountSun' ",cacheManager = "HY_Web_Cache")
    public int selectCountSun(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time =  format + "%";
        int i = hyvEventMeanMapper.selectCountSun(time);
        return i;
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB",key = " 'selectCountInHand' ",cacheManager = "HY_Web_Cache")
    public int selectCountInHand(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time =   format + "%";
        int i = hyvEventMeanMapper.selectCountInHand(time);
        return i;
    }

    @Override
    @Cacheable(cacheNames = "HY_WEB",key = " 'selectCountHand' ",cacheManager = "HY_Web_Cache")
    public int selectCountHand(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time =  format + "%";
        int i = hyvEventMeanMapper.selectCountHand(time);
        return i;
    }*/

    @Override
    @Cacheable(cacheNames = "HY_WEB",key = " 'getAlertNum' ",cacheManager = "HY_Web_Cache")
    public Map<String, Integer> getAlertNum() {
        HashMap<String,Integer> alertMap = new HashMap<>();
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time =  format + "%";
        //获取告警电站总数
        Integer alertStationNum= hyvEventMeanMapper.selectCountSun(time);
        //获取告警总数
        Integer alertNum = hyvEventMeanMapper.selectCountDay(time);
        //获取已处理数量
        Integer processedNum = hyvEventMeanMapper.selectProcessedNum(time);
        //获取未处理数量
//        Integer unhandledNum= alertNum - processedNum;
        Integer unhandledNum=hyvEventMeanMapper.unhandledNum();
        alertMap.put("alertStationNum",alertStationNum);
        alertMap.put("alertNum",alertNum);
        alertMap.put("processedNum",processedNum);
        alertMap.put("unhandledNum",unhandledNum);
        return alertMap;
    }
}
