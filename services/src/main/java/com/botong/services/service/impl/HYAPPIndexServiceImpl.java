package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoStationListMapper;
import com.botong.services.service.HYAPPIndexService;
import com.botong.services.utils.PageHelp1;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.xml.crypto.Data;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.logging.SimpleFormatter;

@Service
public class HYAPPIndexServiceImpl implements HYAPPIndexService {
    @Autowired
    private BtoStationListMapper btoStationListMapper;


    @Override
    @Cacheable(cacheNames = "HY_Common",key = "'getEnergyAndPeakPower'",cacheManager = "HY_Common_Cache")
    public HashMap<String,Double> getEnergyAndPeakPower() {
//        HashMap<String,Double> energyMap= new HashMap<>();
//        Double dayElectricity = btoStationListMapper.getDayElectricity();
//        Double monthElectricity = btoStationListMapper.getMonthElectricity();
//        Double allElectricity = btoStationListMapper.getAllElectricity();
        HashMap<String,Double> energyMap = btoStationListMapper.getElectricity();
        Double allPeakPower = btoStationListMapper.getAllPeakPower();
        energyMap.put("allPeakPower",allPeakPower);
//        energyMap.put("dayElectricity",dayElectricity);
//        energyMap.put("monthElectricity",monthElectricity);
//        energyMap.put("yearElectricity",yearElectricity);
//        energyMap.put("allElectricity",allElectricity);
        return  energyMap;
    }

    @Override
    @Cacheable(cacheNames = "HY_APP3M",key = "'getStationNum'",cacheManager = "HY_APP_Cache_3M")
    public HashMap<String, Integer> getStationNum() {
        Date cur_date = new Date();
//        Date near_date = new Date(cur_date.getTime() - 10 * 60 * 1000);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String curTime = simpleDateFormat.format(cur_date).substring(0, 10).concat("%");
//        String nearTime = simpleDateFormat.format(near_date).substring(0, 15).concat("%");
        HashMap<String,Integer> stationNum = new HashMap<>();
        //获取表名后缀
//        String tableName = this.getTableName(nearTime);
        //为time追加'%':  例如"2022-10-24 10:1%"
//       String timeConcat = time.concat("%");
        //获取总电站数
        Integer allStationNum = btoStationListMapper.getAllStationNum();
        //获取在线电站数(待处理告警数)
        Integer onlineStationNum = btoStationListMapper.getOnlineStationNum();
        //获取告警电站数
        Integer warnStationNum = btoStationListMapper.getWarnStationNum(curTime);
        //获取告警运维器数量
        Integer warnOperatorNum =btoStationListMapper.getWarnOperatorNum();
        //获取告警记录数 = 告警运维器数量 + 告警逆变器数量
        Integer warnRecords = btoStationListMapper.getWarnRecords();
        warnRecords += warnOperatorNum;
        //获取正常电站数：在线电站数-告警电站数
        Integer normalStationNum = Math.max(onlineStationNum - warnStationNum,0);
        //获取离线电站数: 总电站数 - 在线电站数
        Integer offlineStationNum = allStationNum - onlineStationNum;
        stationNum.put("allStationNum",allStationNum);
        stationNum.put("onlineStationNum",onlineStationNum);
        stationNum.put("warnStationNum",warnStationNum);
        stationNum.put("warnRecords",warnRecords);
        stationNum.put("normalStationNum",normalStationNum);
        stationNum.put("offlineStationNum",offlineStationNum);
        return stationNum;
    }


    //将时间戳处理为表名后缀
    private String getTableName(String time){
        String[] split = time.split("-");
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            buffer.append(split[i]);
        }
        String tableName = buffer.toString().substring(0,8);
        return tableName;
    }

    @Override
    @Cacheable(cacheNames = "GY_APP",key = "'TotalInstalledCapacity'",cacheManager = "HY_APP_Cache")
    public Object TotalInstalledCapacity() {
        return btoStationListMapper.TotalInstalledCapacity();
    }

}
