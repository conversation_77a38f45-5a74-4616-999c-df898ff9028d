package vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 15:00
 */
@Data
@ApiModel("图表数据查询条件")
public class ChartConditionVO implements Serializable {
    private static final long serialVersionUID = 1856546184918787648L;
    @ApiModelProperty("查询条件类型：Y（年） M（月）D（日）A（总）")
    private String conditionType;
    @ApiModelProperty("查询的日期字符串: yyyy-MM-dd")
    private String dateTime;
    @ApiModelProperty("电站ID数组")
    private List<String> plantIds;
    @ApiModelProperty("项目专项：0:户用 1:整县-河源 3:工商业")
    private String special;
}
