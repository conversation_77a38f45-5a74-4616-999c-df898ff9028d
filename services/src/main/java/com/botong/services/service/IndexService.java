package com.botong.services.service;

import com.github.pagehelper.PageInfo;
import entity.Carousel;
import entityDTO.PeakPowerAndEnergy;
import entityDTO.StationDeviceNum;
import org.springframework.stereotype.Service;

/**
 *首页-站点概览&电站列表
 *       -综合大屏
 * <AUTHOR>
 * @date 2022-12-17
 */
public interface IndexService {
    /**
     * 首页-站点概览：
     * 总装机容量&日-月-总-近六月-近七日发电量
     * @return
     */
    PeakPowerAndEnergy peakPowerAndStationEnergy();

    /**
     * 首页-站点概览：
     * 电站&逆变器数量(总、正常、在线、离线、告警)、告警记录数
     * @return
     */
    StationDeviceNum stationAndDeviceNum();

    /**
     * --电站实时发电列表-轮播
     * --站点轮播
     * @param page
     * @param pageSize
     * @return
     */
    PageInfo<Carousel> realTimePowerCarousel(Integer page, Integer pageSize);
}
