package entityDTO;

import lombok.Data;

import java.io.Serializable;

/**
 * 电站列表--前端传递的页面参数实体
 *
 * <AUTHOR>
 * @date 2022-09-20 15:03:44
 */
@Data
public class RegisterStationForm implements Serializable {
    /**
     *  用户名
     */
    private String username;
    /**
     * 电站名
     */
    private String plantName;
    /**
     * 电站容量
     */
    private String peakPower;
    /**
     * 电站类型
     */
    private Integer planType;
    /**
     * 用户类型
     */
    private Integer businessType;
    /**
     * 井网电价
     */
    private Float electroValency;
    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String state;
    /**
     * 城市
     */
    private String city;
    /**
     * 县/区/镇
     */
    private String town;
    /**
     * 地址
     */
    private String addr;
    /**
     * 经度
     */
    private Float longitude;
    /**
     * 纬度
     */
    private Float latitude;

    private String special;

    private String hyUserUid;

    private String enginerId;
}
