package com.botong.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("btoywAlarmController")
@Api(tags = "运维告警")
public class btoywAlarmController {
    @Autowired
    private com.botong.services.service.btoywAlarmService btoywAlarmService;

    @ApiOperation("告警列表--包括电站告警与运维器告警信息")
    @GetMapping("AlarmInformationList")
    public AjaxResult pendingAlarmInformationList(String startTime,Integer a,Integer b){
        return AjaxResult.success("操作成功",btoywAlarmService.AlarmInformationList(startTime,a,b));
    }



}
