package com.botong.services.service;


import com.baomidou.mybatisplus.extension.service.IService;

import com.botong.services.vo.InverterVo;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entityDTO.BtoDeviceListDTO;
import entityDTO.TUBIAO;
import entityDTO.sheBeiJianCe;
import entityDTO.stationNum;
import vo.EfficiencyVo;

import java.util.HashMap;
import java.util.List;


/**
 * 逆变器列表
 *
 * <AUTHOR> @date 2022-08-11 15:37:05
 */
public interface BtoDeviceListService extends IService<BtoDeviceList> {


    Long DeviceAllNum();

    Long DeviceStatus0();

    Long DeviceStatus1();

    List<stationNum> DeviceStatus2();

    double Deviceuptime();

    Long DeviceStatus3();

    /**
     * 逆变器数量及状态列表
     * @return
     */
    HashMap<String,Integer> DeviceNum();

    List<BtoDeviceListDTO> AllStationEnergy();

    List<BtoDeviceList> DeviceInformation(String plantUid);

    List<BtoDeviceList> DeviceInformationSN(String plantUid);

    List<TUBIAO> DeviceInformationTU(String plantUid);

    List<HashMap<String,String>> DeviceMonitorUlist();

    List<sheBeiJianCe> DeviceActivePower(String snName, String tableNme,String tableName2);

    List<sheBeiJianCe> Devicecurrent(String snName,String tableName,String tableName2);

    List<sheBeiJianCe> DeviceVoltage(String snName,String tableName,String tableName2);

    List<sheBeiJianCe> DeviceFrequency(String snName,String tableName,String tableName2);

    List<sheBeiJianCe> Devicetemperature(String snName,String tableName,String tableName2);

    HashMap<String, Object> workEfficiencyRanking();

    List<BtoDeviceList> getUserInverterNum(String plantId);

    List<sheBeiJianCe> collectionMaxMin(String snName, String tablName ,String tabletime2);

    List<sheBeiJianCe> collectioAvg(String snName, String tableNme ,String tableName2);

    Object EquipmentMonitoring(String page, String pageSize);

    Object EquipmentMonitoringlike(String devicesn, String deviceId,String plantUid);

    List<stationNum> Deviceonline();

    Object EquipmentMonitoringplantUid(String page, String pageSize, String plantUid);

    List<String> getDeviceIdsByPlantId(String plantId);

    List<InverterVo> getInverterByPlantId(String plantId);

    Object getInverterDetails(String sn);

    Object GetBasicInverterInformation(String sn);

    PageInfo<BtoDeviceList> InverterOfflineList(String page, String pageSize, String time1, String time2);

    HashMap<String,Object> mergecollectionMaxMin(String snName, String tableName, String tableName2);
}

