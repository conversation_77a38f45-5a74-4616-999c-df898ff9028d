package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoDeviceModulesMapper;
import com.botong.services.service.BtoDeviceModulesService;
import entity.BtoDeviceModules;
import entityDTO.BtoDeviceBindingDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;


/**
 * 光伏组件 impl
 *
 * <AUTHOR> @date 2022-09-20 15:36:30
 */
@Service
public class BtoDeviceModulesServiceImpl extends ServiceImpl<BtoDeviceModulesMapper, BtoDeviceModules> implements BtoDeviceModulesService {

    @Autowired
    private BtoDeviceModulesMapper btoDeviceModulesMapper;

    @Override
    public Integer addDeviceModules(BtoDeviceBindingDTO btoDeviceBindingDTO) {
        HashMap<String, Object> hashMap = new HashMap<>();
        BtoDeviceModules btoDeviceModules = new BtoDeviceModules();
        btoDeviceModules.setPlantId(btoDeviceBindingDTO.getPlantId());
        btoDeviceModules.setSn(btoDeviceBindingDTO.getDeviceSn());
        btoDeviceModules.setCapacity(btoDeviceBindingDTO.getCapacity());
        btoDeviceModules.setCreateDate(btoDeviceBindingDTO.getCreateDate());
        btoDeviceModules.setUpdateTime(btoDeviceBindingDTO.getUpdateTime());
        btoDeviceModules.setK1(btoDeviceBindingDTO.getK1());
        btoDeviceModules.setK2(btoDeviceBindingDTO.getK2());
        btoDeviceModules.setK3(btoDeviceBindingDTO.getK3());
        btoDeviceModules.setK4(btoDeviceBindingDTO.getK4());
        btoDeviceModules.setK5(btoDeviceBindingDTO.getK5());
        btoDeviceModules.setK6(btoDeviceBindingDTO.getK6());
        btoDeviceModules.setPv1Dc1(btoDeviceBindingDTO.getPv1Dc1());
        btoDeviceModules.setPv1Dc2(btoDeviceBindingDTO.getPv1Dc2());
        btoDeviceModules.setPv1Dc3(btoDeviceBindingDTO.getPv1Dc3());
        btoDeviceModules.setPv1Dc4(btoDeviceBindingDTO.getPv1Dc4());
        btoDeviceModules.setPv2Dc1(btoDeviceBindingDTO.getPv2Dc1());
        btoDeviceModules.setPv2Dc2(btoDeviceBindingDTO.getPv2Dc2());
        btoDeviceModules.setPv2Dc3(btoDeviceBindingDTO.getPv2Dc3());
        btoDeviceModules.setPv2Dc4(btoDeviceBindingDTO.getPv2Dc4());
        btoDeviceModules.setPv3Dc1(btoDeviceBindingDTO.getPv3Dc1());
        btoDeviceModules.setPv3Dc2(btoDeviceBindingDTO.getPv3Dc2());
        btoDeviceModules.setPv3Dc3(btoDeviceBindingDTO.getPv3Dc3());
        btoDeviceModules.setPv3Dc4(btoDeviceBindingDTO.getPv3Dc4());
        btoDeviceModules.setPv4Dc1(btoDeviceBindingDTO.getPv4Dc1());
        btoDeviceModules.setPv4Dc2(btoDeviceBindingDTO.getPv4Dc2());
        btoDeviceModules.setPv4Dc3(btoDeviceBindingDTO.getPv4Dc3());
        btoDeviceModules.setPv4Dc4(btoDeviceBindingDTO.getPv4Dc4());
        //新增光伏组件内容（已写）
        btoDeviceModules.setPv5Dc1(btoDeviceBindingDTO.getPv5Dc1());
        btoDeviceModules.setPv5Dc2(btoDeviceBindingDTO.getPv5Dc2());
        btoDeviceModules.setPv5Dc3(btoDeviceBindingDTO.getPv5Dc3());
        btoDeviceModules.setPv5Dc4(btoDeviceBindingDTO.getPv5Dc4());
        btoDeviceModules.setPv6Dc1(btoDeviceBindingDTO.getPv6Dc1());
        btoDeviceModules.setPv6Dc2(btoDeviceBindingDTO.getPv6Dc2());
        btoDeviceModules.setPv6Dc3(btoDeviceBindingDTO.getPv6Dc3());
        btoDeviceModules.setPv6Dc4(btoDeviceBindingDTO.getPv6Dc4());
        btoDeviceModules.setPv7Dc1(btoDeviceBindingDTO.getPv7Dc1());
        btoDeviceModules.setPv7Dc2(btoDeviceBindingDTO.getPv7Dc2());
        btoDeviceModules.setPv7Dc3(btoDeviceBindingDTO.getPv7Dc3());
        btoDeviceModules.setPv7Dc4(btoDeviceBindingDTO.getPv7Dc4());
        btoDeviceModules.setPv8Dc1(btoDeviceBindingDTO.getPv8Dc1());
        btoDeviceModules.setPv8Dc2(btoDeviceBindingDTO.getPv8Dc2());
        btoDeviceModules.setPv8Dc3(btoDeviceBindingDTO.getPv8Dc3());
        btoDeviceModules.setPv8Dc4(btoDeviceBindingDTO.getPv8Dc4());
        btoDeviceModules.setPv9Dc1(btoDeviceBindingDTO.getPv9Dc1());
        btoDeviceModules.setPv9Dc2(btoDeviceBindingDTO.getPv9Dc2());
        btoDeviceModules.setPv9Dc3(btoDeviceBindingDTO.getPv9Dc3());
        btoDeviceModules.setPv9Dc4(btoDeviceBindingDTO.getPv9Dc4());
        btoDeviceModules.setPv10Dc1(btoDeviceBindingDTO.getPv10Dc1());
        btoDeviceModules.setPv10Dc2(btoDeviceBindingDTO.getPv10Dc2());
        btoDeviceModules.setPv10Dc3(btoDeviceBindingDTO.getPv10Dc3());
        btoDeviceModules.setPv10Dc4(btoDeviceBindingDTO.getPv10Dc4());
        btoDeviceModules.setPv11Dc1(btoDeviceBindingDTO.getPv11Dc1());
        btoDeviceModules.setPv11Dc2(btoDeviceBindingDTO.getPv11Dc2());
        btoDeviceModules.setPv11Dc3(btoDeviceBindingDTO.getPv11Dc3());
        btoDeviceModules.setPv11Dc4(btoDeviceBindingDTO.getPv11Dc4());
        btoDeviceModules.setPv12Dc1(btoDeviceBindingDTO.getPv12Dc1());
        btoDeviceModules.setPv12Dc2(btoDeviceBindingDTO.getPv12Dc2());
        btoDeviceModules.setPv12Dc3(btoDeviceBindingDTO.getPv12Dc3());
        btoDeviceModules.setPv12Dc4(btoDeviceBindingDTO.getPv12Dc4());
        btoDeviceModules.setK7(btoDeviceBindingDTO.getK7());
        btoDeviceModules.setK8(btoDeviceBindingDTO.getK8());
        btoDeviceModules.setK9(btoDeviceBindingDTO.getK9());
        btoDeviceModules.setK10(btoDeviceBindingDTO.getK10());
        btoDeviceModules.setK11(btoDeviceBindingDTO.getK11());
        btoDeviceModules.setK12(btoDeviceBindingDTO.getK12());
        btoDeviceModules.setK13(btoDeviceBindingDTO.getK13());
        btoDeviceModules.setK14(btoDeviceBindingDTO.getK14());
        btoDeviceModules.setK15(btoDeviceBindingDTO.getK15());
        btoDeviceModules.setK16(btoDeviceBindingDTO.getK16());
        btoDeviceModules.setK17(btoDeviceBindingDTO.getK17());
        btoDeviceModules.setK18(btoDeviceBindingDTO.getK18());
        btoDeviceModules.setK19(btoDeviceBindingDTO.getK19());
        btoDeviceModules.setK20(btoDeviceBindingDTO.getK20());
        btoDeviceModules.setK21(btoDeviceBindingDTO.getK21());
        btoDeviceModules.setK22(btoDeviceBindingDTO.getK22());

        int flag = baseMapper.insert(btoDeviceModules);
//        int insert = 0;
//        try {
//            insert = baseMapper.insert(btoDeviceModules);
//            if (insert == 1) {
//                hashMap.put("msg", "添加成功");
//            }
//        } catch (Exception e) {
//            hashMap.put("msg", "添加失败，该电站已存在");
//        }
        return flag;
    }
}
