<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.VDeviceListMapper">
    <resultMap type="entity.VDeviceList" id="vStationBaseMap">
        <result property="deviceId" column="device_id"/>
        <result property="deviceSn" column="device_sn"/>
        <result property="type" column="type"/>
    </resultMap>

    <select id="Liskselect" resultType="entity.VDeviceList">

        select * from v_device_list
        where 1=1
       <if test="a != null or a != ''">
           and (device_id like #{a}) or (device_sn like #{a})
       </if>

    </select>



</mapper>