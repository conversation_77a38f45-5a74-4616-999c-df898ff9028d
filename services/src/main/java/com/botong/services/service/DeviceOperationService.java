package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.ModifyStationForm;
import entityDTO.deviceInformationDTO;

public interface DeviceOperationService extends IService<deviceInformationDTO> {
    PageInfo<deviceInformationDTO> selectDeviceByCtrl(Integer page, Integer pageSize);
    Boolean collectDeviceOperation(String plantId);
    Boolean cancelCollectDeviceOperation(String plantId);
    Object selectDeviceInformation(String plantId);

    /**
     * 修改电站信息--河源APP
     * @param modifyStationForm
     * @return
     */
    Boolean modifyDeviceOperation(ModifyStationForm modifyStationForm);
    Boolean deleteDeviceOperation(String plantId,String password,String userUid);
}
