package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entityDTO.*;
import org.springframework.stereotype.Service;
import vo.ChartConditionVO;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

@Service
public interface userlogionService extends IService<userListLogin> {


    int userlogin(String username, String password);

    List<userListLogin> frontPage(String userUid);

    int TotalNumberOfAlarms(String userUid);

    List<userListLogin> TotalNumberOfAlarmsDetails(String userUid);

    List<userListLogin> AlarmNumberClassification(String userUid);

    List<userListLogin> OfflineNumberClassification(String userUid);

    List<userListLogin> frontPageplantid(String userUid);

    List<userListLogin> PowerStationList(String plantId);

    List<userListLogin> ifSmartOM(String plantId);


    List<userListLogin> ifSmartOMYueShang(String plantUid,String time);
    List<userListLogin> ifSmartOMYueXia(String plantUid,String time);
    List<userListLogin> ifSmartOMNianShang(String plantUid,String time);
    List<userListLogin> ifSmartOMNianXia(String plantUid,String time);
    List<userListLogin> ifSmartOMALL(String plantUid);

    int updatePassword(String userUid, String password);


    int registeredUser(String cUserName, String cUsertel, String cUserEmail, String updateTime, String delFlag);

    Object versionView();

}
