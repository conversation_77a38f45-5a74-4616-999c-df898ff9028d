package com.botong.controller;

import com.botong.services.service.HYIncomeStatisticService;
import entityDTO.IncomeCondDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

@CrossOrigin
@RestController
@Api(tags = "收益统计")
@RequestMapping("IncomeStatistic")
public class HYIncomeStatisticController {
    @Autowired
    private HYIncomeStatisticService hyIncomeStatisticService;

    @ApiOperation("收益统计——日月年单个&多个汇总接口")
    @PostMapping("IncomeStatisticsByCondition")
    public AjaxResult IncomeStatisticsByCondition(@RequestBody IncomeCondDTO incomeCondDTO){
        return AjaxResult.success("操作成功",hyIncomeStatisticService.IncomeStatisticsByCondition(incomeCondDTO));
    }


    @ApiOperation("收益统计——日（单个或多个）")
    @GetMapping("IncomeStatisticsDay")
    public AjaxResult IncomeStatisticsDay(String plant_id, String time1, String time2, Integer page, Integer pageSize){
        return AjaxResult.success("操作成功",hyIncomeStatisticService.IncomeStatisticsDay(plant_id,time1,time2,page,pageSize));
    }

    @ApiOperation("收益统计——月（单个或多个）")
    @GetMapping("IncomeStatisticsMonth")
    public AjaxResult IncomeStatisticsMonth( String plant_id, String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",hyIncomeStatisticService.IncomeStatisticsMonth(plant_id,time,page,pageSize));
    }

    @ApiOperation("收益统计——年（单个或多个）")
    @GetMapping("IncomeStatisticsYear")
    public AjaxResult IncomeStatisticsYear( String plant_id, String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",hyIncomeStatisticService.IncomeStatisticsYear(plant_id,time,page,pageSize));
    }
    @ApiOperation("收益统计——日（全部）")
    @GetMapping("IncomeStatisticsAllDay")
    public AjaxResult IncomeStatisticsAllDay( String time1,String time2,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",hyIncomeStatisticService.IncomeStatisticsAllDay(time1,time2,page,pageSize));
    }

    @ApiOperation("收益统计——月（全部）")
    @GetMapping("IncomeStatisticsAllMonth")
    public AjaxResult IncomeStatisticsAllMonth(  String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",hyIncomeStatisticService.IncomeStatisticsAllMonth(time,page,pageSize));
    }

    @ApiOperation("收益统计——年（全部）")
    @GetMapping("IncomeStatisticsAllYear")
    public AjaxResult IncomeStatisticsAllYear(  String time,Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",hyIncomeStatisticService.IncomeStatisticsAllYear(time,page,pageSize));
    }
}
