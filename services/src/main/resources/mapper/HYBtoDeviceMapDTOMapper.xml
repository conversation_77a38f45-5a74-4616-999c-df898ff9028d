<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.HYBtoDeviceMapDTOMapper">
    <resultMap type="entityDTO.BtoDeviceMapDTO" id="btoDeviceMap">
        <result property="sn" column="sn"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="time" column="time"/>
        <result property="plantId" column="plant_id"/>
        <result property="stationName" column="name1"/>
    </resultMap>
    <resultMap id="powerStatistic" type="java.util.HashMap">
        <result column="stationName" property="stationName" jdbcType="VARCHAR" />
        <result column="count" property="count" jdbcType="VARCHAR"/>
        <collection property="stationPowerList" ofType="java.util.HashMap" javaType="java.util.ArrayList">
            <result column="sn" property="sn" jdbcType="VARCHAR" />
            <result column="energy" property="energy" jdbcType="VARCHAR"/>
            <result column="time" property="time" jdbcType="VARCHAR"/>
            <result column="plantId" property="plantId" jdbcType="VARCHAR"/>
            <result column="stationName" property="stationName" jdbcType="VARCHAR"/>
            <result column="power" property="power" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>


    <select id="selectBtoDeviceMap" resultMap="btoDeviceMap">
        SELECT a.sn,
               today_energy,
               a.total_energy,
               time,
               b.plant_id,
               b.name1,
               a.power
        FROM bto_device_${table} a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE time LIKE #{b}
          AND plant_id = #{plantId}
          AND a.sn = #{sn}
        order by time desc
        limit 1;
    </select>


    <select id="selectDistinct" resultType="String">
        SELECT distinct a.sn
        FROM bto_device_${table} a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE time LIKE '${b}'
          AND plant_id = '${plantId}';
    </select>

    <select id="selectPowerStatisticByDay" resultMap="powerStatistic">
        SELECT
        a.plant_id plantId,
        a.name1 stationName,
        sum(b.today_energy) energy,
        b.sn,
        b.time
        FROM
        v1_station_device a
        LEFT JOIN bto_device_${tableName} b ON a.device_sn = b.sn
        WHERE
        a.special = '1'
        AND a.plant_id IN
        <foreach collection="plantIdArray" item="plantId" separator="," open="(" close=")">
            #{plantId}
        </foreach>
        GROUP BY plantId,time
        ORDER BY time
    </select>


    <select id="selectPowerStatisticByMonth" resultMap="powerStatistic">
        SELECT
        a.plant_id plantId,
        a.date time,
        a.energy as energy,
        b.name1 stationName
        FROM
        v_day a LEFT JOIN v1_station_device b
        on a.plant_id = b.plant_id
        WHERE
              special = '1'
              AND a.plant_id IN
        <foreach collection="plantIdArray" item="plantId" open="(" separator="," close=")">
            #{plantId}
        </foreach>
        AND LEFT(a.date,7) = #{time}
        GROUP BY stationName,a.date
        ORDER BY time
    </select>


    <select id="selectPowerStatisticByYear" resultMap="powerStatistic">
        SELECT a.plant_id plantId,
        left(a.date, 7) time,
        sum(a.energy) energy,
        COUNT(a.date) count,
        b.name1 stationName
        FROM v_day a
        LEFT JOIN v1_station_device b
        on a.plant_id = b.plant_id
        WHERE a.plant_id IN
        <foreach collection="plantIdArray" item="plantId" open="(" separator="," close=")">
            #{plantId}
        </foreach>
        AND LEFT(a.date,4) =   #{time}
        GROUP BY stationName,a.date
        ORDER BY time
    </select>

    <select id="selectBtoDeviceMap2" resultType="entityDTO.BtoDeviceMapDTO">
        SELECT a.sn           sn,
               a.today_energy todayEnergy,
               a.total_energy totalEnergy,
               ipv1,
               ipv2,
               ipv3,
               ipv4,
               ipv5,
               ipv6,
               ipv7,
               ipv8,
               ipv9,
               vpv1,
               vpv2,
               vpv3,
               vpv4,
               vpv5,
               vpv6,
               vpv7,
               vpv8,
               vpv9,
               ipv1 * vpv1    power1,
               ipv2 * vpv2    power2,
               ipv3 * vpv3    power3,
               ipv4 * vpv4    power4,
               ipv5 * vpv5    power5,
               ipv6 * vpv6    power6,
               ipv7 * vpv7    power7,
               ipv8 * vpv8    power8,
               ipv9 * vpv9    power9,
               iac1,
               iac2,
               iac3,
               vac1,
               vac2,
               vac3,
               a.time         time,
               b.plant_id     plantId,
               b.name1        stationName,
               a.power        power
        FROM bto_device_${tableName} a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE a.time LIKE #{date}
          AND b.plant_id = #{plantId}
          AND a.sn = #{sn}
        ORDER BY a.time
    </select>
    <select id="selectDevice" resultType="entityDTO.BtoDeviceMapDTO">
        SELECT a.device_sn                             sn,
               a.`device_status`                    `status`,
               b.power                                 power,
               b.today_energy                          todayEnergy,
               b.total_energy                          totalEnergy,
               b.Rssi                                  Rssi,
               concat(LEFT(b.update_time, 15), '0:00') time
        FROM bto_device_list a
                 LEFT JOIN bto_device_total b ON a.device_sn = b.device_sn
        WHERE a.plant_id = #{plantId}
          AND a.special = '1'
        GROUP BY a.device_sn;
    </select>

</mapper>
