package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.services.vo.MapVo;
import com.botong.services.vo.UserElectricityVo;
import entity.BtoDevice;
import entity.BtoStationBase;
import utils.AjaxResult;
import vo.StationBaseVo;

import java.util.ArrayList;
import java.util.HashMap;


/**
 * 电站基本信息
 *
 * <AUTHOR> @date 2022-08-15 15:35:23
 */
public interface BtoStationBaseService extends IService<BtoStationBase> {


    ArrayList<StationBaseVo> provincialNumber();


    ArrayList<StationBaseVo> municipalNumber();

    Object stationBaseDetail();

    Object getProvincialStation(String type, String areaName);

    Object getAllStation();

    HashMap<String, Object> getDeforestation();

    BtoStationBase getLatitudeAndLongitude(String plantId);

    HashMap<String, Object> userStationInfo(String userUid);

    HashMap<String, Object> userElectricityInfo(String plantId);

    HashMap<String, Object> userChartInfo(String plantId, String timeType, String time, String deviceSns,String tableName);

    MapVo userMapInfo(String plantId);
}

