<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.EquipmentMonitoringMapper">
    <resultMap type="entityDTO.sheBeiJianCe" id="sheBeiJianCeMap">
        <result property="time" column="time"/>
        <result property="snName" column="snName"/>
        <result property="ActivePower" column="ActivePower"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="fac" column="fac"/>
        <result property="temperature" column="temperature"/>
        <result property="couser" column="couser"/>
        <result property="pp" column="pp"/>
        <result property="deviceId" column="deviceId"/>
        <result property="devicesn" column="devicesn"/>
    </resultMap>
    <select id="EquipmentMonitoringplantUid" resultType="entityDTO.sheBeiJianCe">
        SELECT
        a.name name
        ,a.plant_id plantId
        ,a.plant_uid
        ,a.create_date createDate
        ,a.device_id deviceId
        ,a.device_sn deviceSn
        ,b.*
        from v1_base_device a
        left join (
        SELECT sn,time
        FROM bto_device_${tableNme}
        WHERE time LIKE '${Nowtime10}'
        OR
        time LIKE '${Nowtime20}'
        ) b on a.device_sn=b.sn where plant_uid='${plantUid}'
    </select>
</mapper>