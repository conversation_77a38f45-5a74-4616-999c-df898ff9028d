package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.BtoDeviceMapDTO;

import java.util.List;

public interface BtoDeviceMapDTOPlusService extends IService<BtoDeviceMapDTO> {

    PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlus(String sns, String day, Integer page, Integer pageSize);

    PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusMonth(String plantId,String month, Integer page, Integer pageSize);

    PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusYear(String plantId,String year, Integer page, Integer pageSize);
}
