package com.botong.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("userlogion")
@Api(tags = "app接口")
public class HYAaPPfrontPageController {

    @Autowired
    private com.botong.services.service.HYAuserlogionService hyAuserlogionService;


//    @ApiOperation("用户登录")
//    @PostMapping("Login")
//    public AjaxResult userlogin(String username,String password){
//
//        if (userlogionService.userlogin(username,password)==1){
//            return AjaxResult.success("操作成功",userlogionService.userlogin(username,password));
//        }else {
//            return AjaxResult.success("操作失败,用户或者密码错误");
//        }
//    }


    @ApiOperation("首页")
    @GetMapping("frontPage")
    public AjaxResult frontPage(String userUid){
        return AjaxResult.success("操作成功",hyAuserlogionService.frontPage(userUid));
    }

    @ApiOperation("根据userid查询plantid")
    @GetMapping("frontPageplantid")
    public AjaxResult frontPageplantid(String userUid){
        return AjaxResult.success("操作成功",hyAuserlogionService.frontPageplantid(userUid));
    }

    @ApiOperation("告警总数")
    @GetMapping("TotalNumberOfAlarms")
    public AjaxResult TotalNumberOfAlarms(String userUid){
        return AjaxResult.success("操作成功",hyAuserlogionService.TotalNumberOfAlarms(userUid));
    }

    @ApiOperation("告警详情列表")
    @GetMapping("TotalNumberOfAlarmsDetails")
    public AjaxResult TotalNumberOfAlarmsDetails(String userUid){
        return AjaxResult.success("操作成功",hyAuserlogionService.TotalNumberOfAlarmsDetails(userUid));
    }

    @ApiOperation("告警数分类")
    @GetMapping("AlarmNumberClassification")
    public AjaxResult AlarmNumberClassification(String userUid){
        return AjaxResult.success("操作成功",hyAuserlogionService.AlarmNumberClassification(userUid));
    }


    @ApiOperation("离线数分类")
    @GetMapping("OfflineNumberClassification")
    public AjaxResult OfflineNumberClassification(String userUid){
        return AjaxResult.success("操作成功",hyAuserlogionService.OfflineNumberClassification(userUid));
    }


    @ApiOperation("电站列表")
    @GetMapping("PowerStationList")
    public AjaxResult PowerStationList(Integer a,Integer b){
        return AjaxResult.success("操作成功",hyAuserlogionService.PowerStationList(a,b));
    }

    @ApiOperation("电站列表_查询名字")
    @GetMapping("PowerStationListName")
    public AjaxResult PowerStationListName(Integer a,Integer b,String Name){
        if (hyAuserlogionService.PowerStationListName(a,b,Name)==null){
            return AjaxResult.error("数据为空",0);
        }else {
            return AjaxResult.success("操作成功",hyAuserlogionService.PowerStationListName(a,b,Name));
        }
    }


    @ApiOperation("判断是否有智慧运维器")
    @GetMapping("ifSmartOM")
    public AjaxResult ifSmartOM(String plantId){
            return AjaxResult.success("操作成功",hyAuserlogionService.ifSmartOM(plantId));
    }

    @ApiOperation(" 智慧运维器月——上")
    @GetMapping("ifSmartOMYueShang")
    public AjaxResult ifSmartOMYueShang(String plantId,String time){
        return AjaxResult.success("操作成功",hyAuserlogionService.ifSmartOMYueShang(plantId,time));
    }
    @ApiOperation("智慧运维器月——下")
    @GetMapping("ifSmartOMYueXia")
    public AjaxResult ifSmartOMYueXia(String plantId,String time){
        return AjaxResult.success("操作成功",hyAuserlogionService.ifSmartOMYueXia(plantId,time));
    }

    @ApiOperation("智慧运维器年——上")
    @GetMapping("ifSmartOMNianShang")
    public AjaxResult ifSmartOMNianShang(String plantId,String time){
        return AjaxResult.success("操作成功",hyAuserlogionService.ifSmartOMNianShang(plantId,time));
    }
    @ApiOperation("智慧运维器年——下")
    @GetMapping("ifSmartOMNianXia")
    public AjaxResult ifSmartOMNianXia(String plantId,String time){
        return AjaxResult.success("操作成功",hyAuserlogionService.ifSmartOMNianXia(plantId,time));
    }

    @ApiOperation("智慧运维器总_上")
    @GetMapping("ifSmartOMALLShang")
    public AjaxResult ifSmartOMALLShang(String plantId){
        return AjaxResult.success("操作成功",hyAuserlogionService.ifSmartOMALLShang(plantId));
    }

    @ApiOperation("智慧运维器总_下")
    @GetMapping("ifSmartOMALLXia")
    public AjaxResult ifSmartOMALLXia(String plantId){
        return AjaxResult.success("操作成功",hyAuserlogionService.ifSmartOMALLXia(plantId));
    }


    @ApiOperation("修改密码")
    @PostMapping("updatePassword")
    public AjaxResult updatePassword(String userUid,String password){
        if (hyAuserlogionService.updatePassword(userUid,password)==1){
            return AjaxResult.success("修改成功",hyAuserlogionService.updatePassword(userUid,password));
        }else {
            return AjaxResult.success("修改失败，用户可能不存在！",hyAuserlogionService.updatePassword(userUid,password));
        }
    }

    @ApiOperation("注册用户")
    @PostMapping("registeredUser")
    public AjaxResult registeredUser(String cUserName,String cUsertel,String cUserEmail,String updateTime,String delFlag){
        if (hyAuserlogionService.registeredUser(cUserName,cUsertel,cUserEmail,updateTime,delFlag)==1){
            return AjaxResult.success("操作成功",hyAuserlogionService.registeredUser(cUserName,cUsertel,cUserEmail,updateTime,delFlag));
        }else {
            return AjaxResult.success("创建失败",hyAuserlogionService.registeredUser(cUserName,cUsertel,cUserEmail,updateTime,delFlag));
        }
    }

    @ApiOperation("版本查看")
    @GetMapping("versionView")
    public AjaxResult versionView(){
        return AjaxResult.success("版本查询成功",hyAuserlogionService.versionView());
    }



    @ApiOperation("根据时间与发电量查询_列表排序")
    @GetMapping("accordingToTimeAndEnergy")
    public AjaxResult accordingToTimeAndEnergy(int sort, int a,int b){
        return AjaxResult.success("查询成功",hyAuserlogionService.accordingToTiemenergy(sort,a,b));
    }





}
