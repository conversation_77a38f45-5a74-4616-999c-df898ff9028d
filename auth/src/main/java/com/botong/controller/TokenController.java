package com.botong.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.form.LoginBody;
import com.botong.services.mapper.UserListMapper;
import com.botong.services.service.UserListService;
import com.botong.services.service.userlogionService;
import entity.BtoUserList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

import java.util.HashMap;

@RestController
@RequestMapping("oauth")
public class TokenController {

    @Autowired
    private UserListService userListService;

//    @GetMapping("login")
//    public AjaxResult login(LoginBody loginBody) {
//
//        String url = "http://localhost:8804/oauth/token";
//        HashMap<String, String> headerMap = new HashMap<>();
//        headerMap.put("Authorization", "Basic dXNlci1jbGllbnQ6dXNlci1zZWNyZXQtODg4OA==");
//        HashMap<String, Object> paramsMap = new HashMap<>();
//        paramsMap.put("grant_type", "password");
//
//        paramsMap.put("username", loginBody.getUsername());
//        paramsMap.put("password", loginBody.getPassword());
//        paramsMap.put("scope", "all");
//
//        HashMap<String, Object> hashMap = new HashMap<>();
//
//        BaseMapper<BtoUserList> baseMapper = userListService.getBaseMapper();
//        QueryWrapper<BtoUserList> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("c_user_name", loginBody.getUsername());
//        BtoUserList btoUserList = baseMapper.selectOne(queryWrapper);
//        if (btoUserList == null) {
//            hashMap.clear();
//            hashMap.put("info", "用户名不存在");
//            return AjaxResult.error("登录失败");
//        } else if (!loginBody.getPassword().equals(btoUserList.getPassword())) {
//            hashMap.clear();
//            hashMap.put("info", "用户名或密码不正确");
//            return AjaxResult.error("登录失败");
//        } else {
//            String body = HttpRequest.post(url).addHeaders(headerMap).form(paramsMap).execute().body();
//            JSONObject jsonObject = JSONUtil.parseObj(body);
//            hashMap.put("access_token", jsonObject.get("access_token"));
//            hashMap.put("token_type", jsonObject.get("token_type"));
//            hashMap.put("refresh_token", jsonObject.get("refresh_token"));
//            hashMap.put("expires_in", jsonObject.get("expires_in"));
//            hashMap.put("userUid", btoUserList.getCUserUid());
//        }
//        return AjaxResult.success("请求成功", hashMap);
//    }

    @GetMapping("login")
    public AjaxResult login(LoginBody loginBody) {

        String url = "http://localhost:8804/oauth/token";
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Basic dXNlci1jbGllbnQ6dXNlci1zZWNyZXQtODg4OA==");
        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("grant_type", "password");

        paramsMap.put("username", loginBody.getUsername());
        paramsMap.put("password", loginBody.getPassword());
        paramsMap.put("scope", "all");

        HashMap<String, Object> hashMap = new HashMap<>();

        BaseMapper<BtoUserList> baseMapper = userListService.getBaseMapper();
        QueryWrapper<BtoUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("c_user_name", loginBody.getUsername());
        BtoUserList btoUserList = baseMapper.selectOne(queryWrapper);
        if (btoUserList == null) {
            hashMap.clear();
            hashMap.put("info", "抱歉,用户名不存在");
            return AjaxResult.error("登录失败");
        } else if (!loginBody.getPassword().equals(btoUserList.getPassword())) {
            hashMap.clear();
            hashMap.put("info", "用户名或密码不正确");
            return AjaxResult.error("登录失败");
        } else {
            String body = HttpRequest.post(url).addHeaders(headerMap).form(paramsMap).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            hashMap.put("access_token", jsonObject.get("access_token"));
            hashMap.put("token_type", jsonObject.get("token_type"));
            hashMap.put("refresh_token", jsonObject.get("refresh_token"));
            hashMap.put("expires_in", jsonObject.get("expires_in"));
            hashMap.put("userUid", btoUserList.getCUserUid());
        }
        return AjaxResult.success("请求成功", hashMap);
    }



    @GetMapping("loginEnginer")
    public AjaxResult loginEnginer(LoginBody loginBody) {

        String url = "http://localhost:8804/oauth/token";
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Basic dXNlci1jbGllbnQ6dXNlci1zZWNyZXQtODg4OA==");
        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("grant_type", "password");

        paramsMap.put("username", loginBody.getUsername());
        paramsMap.put("password", loginBody.getPassword());
        paramsMap.put("scope", "all");

        HashMap<String, Object> hashMap = new HashMap<>();

        BaseMapper<BtoUserList> baseMapper = userListService.getBaseMapper();
        QueryWrapper<BtoUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("c_user_name", loginBody.getUsername());
        BtoUserList btoUserList = baseMapper.selectOne(queryWrapper);
        if (btoUserList == null) {
            hashMap.clear();
            hashMap.put("info", "抱歉,用户名不存在，或者您不是工程师");
            return AjaxResult.error("登录失败");
        } else if (!loginBody.getPassword().equals(btoUserList.getPassword())) {
            hashMap.clear();
            hashMap.put("info", "用户名或密码不正确");
            return AjaxResult.error("登录失败");
        } else {
                String body = HttpRequest.post(url).addHeaders(headerMap).form(paramsMap).execute().body();
                JSONObject jsonObject = JSONUtil.parseObj(body);
                hashMap.put("access_token", jsonObject.get("access_token"));
                hashMap.put("token_type", jsonObject.get("token_type"));
                hashMap.put("refresh_token", jsonObject.get("refresh_token"));
                hashMap.put("expires_in", jsonObject.get("expires_in"));
                hashMap.put("userUid", btoUserList.getCUserUid());
        }
        return AjaxResult.success("请求成功", hashMap);
    }

    @GetMapping("loginHyAdmin")
    public AjaxResult loginHyAdmin(LoginBody loginBody) {

        String url = "http://localhost:8804/oauth/token";
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Basic dXNlci1jbGllbnQ6dXNlci1zZWNyZXQtODg4OA==");
        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("grant_type", "password");

        paramsMap.put("username", loginBody.getUsername());
        paramsMap.put("password", loginBody.getPassword());
        paramsMap.put("scope", "all");

        HashMap<String, Object> hashMap = new HashMap<>();

        BaseMapper<BtoUserList> baseMapper = userListService.getBaseMapper();
        QueryWrapper<BtoUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("c_user_name", loginBody.getUsername());
        BtoUserList btoUserList = baseMapper.selectOne(queryWrapper);
        if (btoUserList == null) {
            hashMap.clear();
            hashMap.put("info", "抱歉,用户名不存在，或者您不是河源管理员");
            return AjaxResult.error("登录失败");
        } else if (!loginBody.getPassword().equals(btoUserList.getPassword())) {
            hashMap.clear();
            hashMap.put("info", "用户名或密码不正确");
            return AjaxResult.error("登录失败");
        } else {
            String body = HttpRequest.post(url).addHeaders(headerMap).form(paramsMap).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            hashMap.put("access_token", jsonObject.get("access_token"));
            hashMap.put("token_type", jsonObject.get("token_type"));
            hashMap.put("refresh_token", jsonObject.get("refresh_token"));
            hashMap.put("expires_in", jsonObject.get("expires_in"));
            hashMap.put("userUid", btoUserList.getCUserUid());
        }
        return AjaxResult.success("请求成功", hashMap);
    }

    @GetMapping("loginHyAPPAdmin")
    public AjaxResult loginHyAPPAdmin(LoginBody loginBody) {

        String url = "http://localhost:8804/oauth/token";
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Basic dXNlci1jbGllbnQ6dXNlci1zZWNyZXQtODg4OA==");
        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("grant_type", "password");

        paramsMap.put("username", loginBody.getUsername());
        paramsMap.put("password", loginBody.getPassword());
        paramsMap.put("scope", "all");

        HashMap<String, Object> hashMap = new HashMap<>();

        BaseMapper<BtoUserList> baseMapper = userListService.getBaseMapper();
        QueryWrapper<BtoUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("c_user_name", loginBody.getUsername());
        BtoUserList btoUserList = baseMapper.selectOne(queryWrapper);
        if (btoUserList == null) {
            hashMap.clear();
            hashMap.put("info", "抱歉,用户名不存在，或者您不是河源管理员");
            return AjaxResult.error("登录失败");
        } else if (!loginBody.getPassword().equals(btoUserList.getPassword())) {
            hashMap.clear();
            hashMap.put("info", "用户名或密码不正确");
            return AjaxResult.error("登录失败");
        } else {
            String body = HttpRequest.post(url).addHeaders(headerMap).form(paramsMap).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            hashMap.put("access_token", jsonObject.get("access_token"));
            hashMap.put("token_type", jsonObject.get("token_type"));
            hashMap.put("refresh_token", jsonObject.get("refresh_token"));
            hashMap.put("expires_in", jsonObject.get("expires_in"));
            hashMap.put("userUid", btoUserList.getCUserUid());
        }
        return AjaxResult.success("请求成功", hashMap);
    }
}
