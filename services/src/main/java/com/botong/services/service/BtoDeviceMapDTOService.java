package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entity.VStationBase;
import entityDTO.BtoDeviceMapDTO;

import java.util.HashMap;
import java.util.List;

public interface BtoDeviceMapDTOService extends IService<BtoDeviceMapDTO> {

    List<BtoDeviceMapDTO> selectBtoDeviceMap(String plantId );

    List<BtoDeviceMapDTO> selectBtoDeviceMapDay(String plantId ,String sn, String day);

    List<BtoDeviceMapDTO> selectBtoDeviceMapDay1(String plantId ,String sn, String day);
}
