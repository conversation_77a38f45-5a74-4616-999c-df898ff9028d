<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.BtoIndexMapper">
    <resultMap type="entity.BtoIndex" id="btoIndexMap">
        <result property="indexId" column="index_id"/>
        <result property="indexName" column="index_name"/>
        <result property="createDate" column="create_date"/>
    </resultMap>
    <update id="update1" parameterType="entity.BtoIndex">
            update bto_index set index_name = #{indexName} where index_id = #{indexId};
    </update>

</mapper>