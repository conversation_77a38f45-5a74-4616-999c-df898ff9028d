<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYABtoStationBaseMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoStationBase" id="btoStationBaseMap">
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="plantId" column="plant_id"/>
        <result property="plantNo" column="plant_no"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userUid" column="user_uid"/>
        <result property="description" column="description"/>
        <result property="notes" column="notes"/>
        <result property="status" column="status"/>
        <result property="country" column="country"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="postal" column="postal"/>
        <result property="elevation" column="elevation"/>
        <result property="plantType" column="plant_type"/>
        <result property="gridType" column="grid_type"/>
        <result property="installedDc" column="installed_dc"/>
        <result property="installedAc" column="installed_ac"/>
        <result property="installedPanel" column="installed_panel"/>
        <result property="createDate" column="create_date"/>
        <result property="imageUrl" column="image_url"/>
        <result property="peakPower" column="peak_power"/>
        <result property="currency" column="currency"/>
        <result property="timezone" column="timezone"/>
        <result property="ownerorganization" column="ownerorganization"/>
        <result property="ownercontact" column="ownercontact"/>
        <result property="designerorganization" column="designerorganization"/>
        <result property="designercontact" column="designercontact"/>
        <result property="installerorganization" column="installerorganization"/>
        <result property="installercontact" column="installercontact"/>
        <result property="installer" column="installer"/>
        <result property="operatororganization" column="operatororganization"/>
        <result property="operatorcontact" column="operatorcontact"/>
        <result property="operator" column="operator"/>
        <result property="financierorganizaton" column="financierorganizaton"/>
        <result property="financierconcact" column="financierconcact"/>
        <result property="offtakerorganization" column="offtakerorganization"/>
        <result property="offtakercontact" column="offtakercontact"/>
        <result property="jurisdictionorganization" column="jurisdictionorganization"/>
        <result property="jurisdictioncontact" column="jurisdictioncontact"/>
        <result property="locale" column="locale"/>
        <result property="arrays" column="arrays"/>
        <result property="inverters" column="inverters"/>
        <result property="trackerType" column="tracker_type"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <resultMap id="userElectricityInfoMap" type="com.botong.services.vo.UserElectricityVo">
        <result property="name" column="name"/>
        <result property="updateTime" column="time"/>
        <result property="currentPower" column="sum(power)"/>
        <result property="todayElectricity" column="sum(today_energy)"/>
        <result property="allElectricity" column="sum(total_energy)"/>
    </resultMap>

    <select id="userElectricityInfo" resultMap="userElectricityInfoMap">
        SELECT
            `name` ,
            time,
            sum(power) ,
            sum(today_energy) ,
            sum(total_energy)

        FROM
            `v_station_base` a
            LEFT JOIN (
            SELECT
            plant_id,
            sn,
            power,
            today_energy,
            total_energy,
            time
            FROM
            v_device_list aa
            LEFT JOIN (
            SELECT sn,power,today_energy,total_energy,time
            FROM ${tableName}
            WHERE
            time LIKE #{time}

            GROUP BY SN,time
            ORDER BY time desc
            ) bb
            ON aa.device_sn = bb.sn
            GROUP BY
            sn
            ) b ON a.plant_id = b.plant_id
        where
            a.plant_id=#{plantId}
    </select>
    <select id="userMapInfo" resultType="com.botong.services.vo.MapVo">
        SELECT
            plant_id,
            longitude,
            latitude
        FROM
            v_station_base
        WHERE
            plant_id = #{plantId}
    </select>


</mapper>