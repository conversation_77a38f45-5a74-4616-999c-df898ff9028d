package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoDeviceList;
import entityDTO.TUBIAO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceInformationMapper extends BaseMapper<BtoDeviceList> {
    List<BtoDeviceList> DeviceInformation(@Param("plantUid") String plantUid, @Param("tableNme") String tableNme, @Param("nowtime10") String nowtime10, @Param("nowtime20") String nowtime20);

    List<BtoDeviceList> DeviceInformationSN(String plantUid);

    List<TUBIAO> DeviceInformationTU(@Param("plantUid") String plantUid, @Param("tableName") String tableName);
}
