package entityDTO;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 河源管理员app用户表
 * @date 2022/10/15 10:22
 */
@Data
public class HYABtoUserListDTO implements Serializable {

    private String UserUid;

    private String name;

    private String PlantName;

    private String username;

    private Integer id;

    /**
     * 用户id
     */
    private Integer cUserId;

    /**
     * 用户uid
     */
    private String cUserUid;

    /**
     * 用户姓名
     */
    private String cUserName;

    /**
     * 用户电话
     */
    private String cUserTel;

    /**
     * 帐号状态（0：正常，1：已删除）
     */
    private Integer delFlag;

    /**
     * 用户注册日期
     */
    private Date cUserRegtime;

    /**
     * 用户档案更新时间
     */
    private Date updateDate;

    /**
     * 用户注册邮箱
     */
    private String cUserEmail;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private String createTime;

    //密码
    private String password;

    private Integer special;

    private String enginerId;

    private String subAcc;

    private String plantId;

    private String RespondentId;

    private Integer author;

}
