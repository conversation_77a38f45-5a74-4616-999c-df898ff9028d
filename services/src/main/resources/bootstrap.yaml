# 应用服务 WEB 访问端口
server:
  port: 8803
spring:
  cache:
    type: redis
    cache-names: GF_web,HY_web,HY_APP
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    timeout: 5000
    password: botongredis666
  application:
    name: services # 应用名称
  # mysql 配置
  datasource:
    username: user
    password: 1234
    driverClassName: com.mysql.jdbc.Driver
    #url: **********************************************************************************************************************************
    url: **********************************************************************************************************************************
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 50
      auto-commit: true
      pool-name: DatebookHikariCP
      idle-timeout: 600000
      max-lifetime: 600000
      connection-timeout: 60000
      connection-test-query: SELECT 1
# 控制台打印SQL
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
#config:
#  init-caches:
#    StationNum: 10s
