package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYBtoDeviceListMapper;
import com.botong.services.mapper.HYBtoDeviceMapDTOMapper;
import com.botong.services.service.HYBtoDeviceMapDTOPlusService;
import com.botong.services.utils.PageHelp1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.BtoDeviceMapDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import vo.PowerStatisticVo;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class HYbtoDeviceMapDTOPlusServiceImpl extends ServiceImpl<HYBtoDeviceMapDTOMapper, BtoDeviceMapDTO> implements HYBtoDeviceMapDTOPlusService {
    @Autowired
    private HYBtoDeviceMapDTOMapper hyBtoDeviceMapDTOMapper;
    @Autowired
    private HYBtoDeviceListMapper hyBtoDeviceListMapper;

    //    @Override
//    public PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlus(PowerStatisticVo powerStatisticVo) {
//        PageHelp1 pageHelp = new PageHelp1();
//        String time = powerStatisticVo.getDate().concat("%");
//        String tableName = powerStatisticVo.getDate().replace("-", "");
//        ArrayList<String> SnList = new ArrayList<>();
//        List<BtoDeviceMapDTO> todayEnergyList = new ArrayList<>();
//        if (powerStatisticVo.getPlantIdArray().length > 0) {
//            //遍历所有plantId，并查询每个plantId所属的逆变器SN
//            for (String plantId : powerStatisticVo.getPlantIdArray()) {
//                List<String> SNs = hyBtoDeviceListMapper.selectSnByPlantId(plantId);
//                SnList.addAll(SNs);
//            }
//            //遍历所有的SN，进行数据查询
//            for (String sn : SnList) {
//                todayEnergyList.addAll(hyBtoDeviceMapDTOMapper.selectBtoDeviceMapPlus(tableName, time, sn));
//            }
//        }
//        PageInfo info = pageHelp.pageHelper(todayEnergyList, powerStatisticVo.getPage(), powerStatisticVo.getPageSize());
//        return info;
//    }
//    @Override
//    public PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusMonth(PowerStatisticVo powerStatisticVo) {
//        PageHelp1 pageHelp = new PageHelp1();
//        String time = powerStatisticVo.getDate().concat("%");
//        ArrayList<String> SnList = new ArrayList<>();
//        List<BtoDeviceMapDTO> monthEnergyList = new ArrayList<>();
//        if (powerStatisticVo.getPlantIdArray().length > 0) {
//            //遍历所有plantId，并查询每个plantId所属的逆变器SN
//            for (String plantId : powerStatisticVo.getPlantIdArray()) {
//                List<String> SNs = hyBtoDeviceListMapper.selectSnByPlantId(plantId);
//                SnList.addAll(SNs);
//            }
//            //遍历所有的SN，进行数据查询
//            for (String sn : SnList) {
//                monthEnergyList.addAll(hyBtoDeviceMapDTOMapper.selectBtoDeviceMapPlusMonth(time, sn));
//            }
//        }
//        PageInfo info = pageHelp.pageHelper(monthEnergyList, powerStatisticVo.getPage(), powerStatisticVo.getPageSize());
//        return info;
//    }
//    @Override
//    public PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusYear(PowerStatisticVo powerStatisticVo) {
//        PageHelp1 pageHelp = new PageHelp1();
//        String time = powerStatisticVo.getDate().concat("%");
//        ArrayList<String> SnList = new ArrayList<>();
//        List<BtoDeviceMapDTO> yearEnergyList = new ArrayList<>();
//        if (powerStatisticVo.getPlantIdArray().length > 0) {
//            //遍历所有plantId，并查询每个plantId所属的逆变器SN
//            for (String plantId : powerStatisticVo.getPlantIdArray()) {
//                List<String> SNs = hyBtoDeviceListMapper.selectSnByPlantId(plantId);
//                SnList.addAll(SNs);
//            }
//            //遍历所有的SN，进行数据查询
//            for (String sn : SnList) {
//                yearEnergyList.addAll(hyBtoDeviceMapDTOMapper.selectBtoDeviceMapPlusMonth(time, sn));
//            }
//        }
//        PageInfo info = pageHelp.pageHelper(yearEnergyList, powerStatisticVo.getPage(), powerStatisticVo.getPageSize());
//        return info;
//    }
    @Override
    public List<HashMap<String, Object>> selectPowerStatistic(PowerStatisticVo powerStatisticVo) {
//        PageHelper.startPage(powerStatisticVo.getPage(), powerStatisticVo.getPageSize());
        String date = powerStatisticVo.getDate();
        String tableName = powerStatisticVo.getDate().replace("-", "");
        List<HashMap<String, Object>> powerStatisticList = new ArrayList<>();
        if (powerStatisticVo.getPlantIdArray().length > 0) {
            //遍历所有plantId，并查询每个plantId所属的逆变器SN
            switch (powerStatisticVo.getTimeType()) {
                case "D":
//                    ArrayList<String> SnList = hyBtoDeviceListMapper.selectSnByPlantId(powerStatisticVo.getPlantIdArray());
                    //遍历所有的SN，进行数据查询
                    powerStatisticList = hyBtoDeviceMapDTOMapper.selectPowerStatisticByDay(tableName, date, powerStatisticVo.getPlantIdArray());
                    break;
                case "M":
                    powerStatisticList = hyBtoDeviceMapDTOMapper.selectPowerStatisticByMonth(powerStatisticVo.getPlantIdArray(), date);
                    break;
                case "Y":
                    //遍历所有的SN，进行数据查询
                    powerStatisticList = hyBtoDeviceMapDTOMapper.selectPowerStatisticByYear(powerStatisticVo.getPlantIdArray(), date);
                    break;
                default:
            }
        }
//        PageInfo<HashMap<String, Object>> info = new PageInfo<>(powerStatisticList);
//        info.setTotal(powerStatisticVo.getPlantIdArray().length);
        return powerStatisticList;
    }
}
