package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoIndexMapper;
import com.botong.services.service.BtoIndexService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoIndex;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BtoIndexServiceImpl extends ServiceImpl<BtoIndexMapper, BtoIndex> implements BtoIndexService {

    @Autowired
    private BtoIndexMapper btoIndexMapper;

    @Override
    public PageInfo<BtoIndex> SelectBtoIndex(Integer page,Integer pageSize){
        PageHelper.startPage(page,pageSize);
        List<BtoIndex> list = btoIndexMapper.selectList(null);
        PageInfo<BtoIndex> info = new PageInfo<>(list);
        return info;
    }

    @Override
    public PageInfo<BtoIndex> LikeSelectBtoIndex(String IndexName, Integer page,Integer pageSize){
            PageHelper.startPage(page,pageSize);
            QueryWrapper<BtoIndex> wrapper = new QueryWrapper<>();
            wrapper.like(StringUtils.isNotBlank(IndexName), "index_name", IndexName);
            List<BtoIndex> list = btoIndexMapper.selectList(wrapper);
            PageInfo<BtoIndex> info = new PageInfo<>(list);
            return info;
    }


    @Override
    public int InsertBtoIndex(BtoIndex btoIndex){

        QueryWrapper<BtoIndex> wrapper = new QueryWrapper<>();
        wrapper.eq("index_id",btoIndex.getIndexId());
        if (btoIndexMapper.selectOne(wrapper) == null) {
            return btoIndexMapper.insert(btoIndex);
        }else {
            return -1;
        }
    }

    @Override
    public int UpdateBtoIndex(String indexId  ,String indexName) {
        QueryWrapper<BtoIndex> wrapper = new QueryWrapper<>();
        wrapper.eq("index_id", indexId);
        btoIndexMapper.update1(indexId, indexName);
        return 1;
    }

    @Override
    public int DeleteBtoIndex(String indexId) {
        QueryWrapper<BtoIndex> wrapper = new QueryWrapper<>();
        wrapper.eq("index_id" , indexId);
        return btoIndexMapper.delete(wrapper);
    }
}
