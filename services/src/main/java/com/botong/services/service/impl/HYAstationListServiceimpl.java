package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYAstationListMapper;
import com.botong.services.service.HYAstationListService;
import com.botong.services.utils.ReadApkUtil;
import entity.DevicePvInfo;
import entityDTO.*;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.LonLatVo;
import vo.StationInfoVo;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static utils.ListPageUtils.pageBySubList;

@Service
public class HYAstationListServiceimpl extends ServiceImpl<HYAstationListMapper, HYAstationList> implements HYAstationListService {

    @Autowired
    private HYAstationListMapper hyAstationListMapper;

    @Override
    public List<HYAstationList> HYadminStationList() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String time = simpleDateFormat.format(date);
        String time2 = simpleDateFormat2.format(date);
        return hyAstationListMapper.selectListt(time, time2);
    }

    @Override
    public Map<String, Object> versionView() throws Exception {
        HashMap<String, Object> objectObjectHashMap = new HashMap<String, Object>();
        File file = new File("/etc/nginx/conf.d/gf/APP/gfHeYuan.apk");//安卓
//        File file2 = new File("/etc/nginx/conf.d/gf/APP/gfHeYuan.ipa");//苹果
//        File file = new File("D:/aaa/gfHeYuan.apk");//安卓
        String test = null;

        if (file.exists() && file.isFile()) {
            try {
                ApkFile apkFile = new ApkFile(file);
                ApkMeta apkMeta = apkFile.getApkMeta();
                test = apkMeta.getVersionName();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String ipaUrl = "/etc/nginx/conf.d/gf/APP/gfHeYuan.ipa";

        String mapIpa = String.valueOf(ReadApkUtil.readIPA(ipaUrl));
        objectObjectHashMap.put("ios", mapIpa);
        objectObjectHashMap.put("data", test);
        return objectObjectHashMap;
    }

    @Override
    public StationInfoVo selectStationInfo(String stationId) {
        return baseMapper.selectStationInfo(stationId);
    }

    @Override
    public LonLatVo map(String stationId) {
        return baseMapper.map(stationId);
    }

    @Override
    public Object getDeviceIdByStationId(String stationId) {
        return baseMapper.getDeviceIdByStationId(stationId);
    }

    @Override
    public List<HYAstationList> getStationsByCondition(StationConditionDTO stationCondition) {
        Date cur_date = new Date();
        Date near_date = new Date(cur_date.getTime() - 10 * 60 * 1000);
        //当前时间-20分钟：用作判断是否在线离线
        long endTime = cur_date.getTime() - 20 * 60 * 1000;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String tableName = simpleDateFormat.format(cur_date);
        String nearTime = simpleDateFormat2.format(near_date).substring(0, 15).concat("%");
        //接收的参数
        Integer plantType = stationCondition.getType();
        String stationName = stationCondition.getStationName();
        String minPeakPower = stationCondition.getMinPeakPower();
        String maxPeakPower = stationCondition.getMaxPeakPower();
        String status = stationCondition.getStatus();
        String town = stationCondition.getTown();
        int pageNo = stationCondition.getA();
        int pageSize = stationCondition.getB();
        List<HYAstationList> hyAstationLists = hyAstationListMapper.selectStaionByCondition(tableName, plantType, stationName, minPeakPower, maxPeakPower, town, status);
        List<HYAstationList> list = pageBySubList(hyAstationLists, pageNo, pageSize);
        return list;
//        List<HYAstationList> onLineStations = null;
//        if (status == null || "".equals(status)) {
//            List<HYAstationList> list = pageBySubList(hyAstationLists, pageNo, pageSize);
//            System.out.println(list);
//            return list;
//        } else if ("在线".equals(status)) {
//            hyAstationLists.forEach(real -> {
//                if (real.getTime() == null || "".equals(real.getTime())) {
//                    real.setTime(real.getCreateDate());
//                }
//            });
//            onLineStations = hyAstationLists.stream().filter(real -> real.getRealPower() != null && !real.getRealPower().equals("0.0") && ).collect(Collectors.toList());
//            onLineStations = hyAstationLists.stream().filter(real -> {
//                try {
//                    if (real.getTime() == null || "".equals(real.getTime())) {
//                        real.setTime(real.getCreateDate());
//                    }
//                    return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(real.getTime()).getTime() > endTime;
//                } catch (ParseException e) {
//                    e.printStackTrace();
//                    System.out.println(e);
//                    return false;
//                }
//            }).collect(Collectors.toList());
//            List<HYAstationList> list = pageBySubList(onLineStations, pageNo, pageSize);
//            System.out.println(list);
//            return list;
//        } else {
//            List<HYAstationList> offlineStations = hyAstationLists.stream().filter(real -> {
//                try {
//                    if (real.getTime() == null || "".equals(real.getTime())) {
//                        real.setTime(real.getCreateDate());
//                    }
//                    return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(real.getTime()).getTime() < endTime;
//                } catch (ParseException e) {
//                    e.printStackTrace();
//                    System.out.println(e);
//                    return false;
//                }
//            }).collect(Collectors.toList());
//            List<HYAstationList> list = pageBySubList(offlineStations, pageNo, pageSize);
//            System.out.println(list);
//            return list;
    }

    @Override
    public Object versionViewipa() {
        File file = new File("/etc/nginx/conf.d/gf/APP/gfHeYuan.ipa");
        String test = null;
        if (file.exists() && file.isFile()) {
            try {
                ApkFile apkFile = new ApkFile(file);
                ApkMeta apkMeta = apkFile.getApkMeta();
                test = apkMeta.getVersionName();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return test;
    }

    @Override
    public DevicePvInfo getDevicePvInfoBySN(String sn) {
        DevicePvInfo devicePvInfo = hyAstationListMapper.getDevicePvInfoBySN(sn);
        return devicePvInfo;
    }

    @Override
    public String updateDevicePvInfo(DevicePvInfo devicePvInfo) {
        if (devicePvInfo != null) {
            int i = hyAstationListMapper.updateDevicePvInfo(devicePvInfo);
            if (i > 0) {
                return "更新数据成功!";
            } else {
                return "数据未更新或更新数据无变化！";
            }
        } else {
            return "取消了修改！";
        }

    }

    @Override
    public String addDeviceInfo(AddDeviceForm addDeviceForm) {
        String deviceId = UUID.randomUUID().toString().toUpperCase();
        addDeviceForm.setDeviceId(deviceId);
        int listFlag = hyAstationListMapper.isdeviceListExist(addDeviceForm.getSN());
        int moudleFlag = hyAstationListMapper.isdeviceModulesExist(addDeviceForm.getSN());
        if (listFlag <= 0 && moudleFlag <= 0) {
            int devicePvInfoflag = hyAstationListMapper.addDevicePvInfo(addDeviceForm);
            int deviceListInfoflag = hyAstationListMapper.addDeviceListInfo(addDeviceForm);
            String msg = "";
            if (devicePvInfoflag > 0 && deviceListInfoflag > 0) {
                msg += "新增逆变器成功！";
            } else if (devicePvInfoflag <= 0) {
                msg += "device_moudle表插入失败！";
            } else {
                msg += "bto_device_list表插入失败！";
            }
            return msg;
        } else {
            System.out.println("请联系服务端工程师: moudle表或list表 中SN已存在!");
            return "0";
        }
    }

    @Override
    public HashMap<String, String> getDeviceInfoByPlantId(String plantId) {
        HashMap<String, String> deviceInfo = hyAstationListMapper.getDeviceInfoByPlantId(plantId);
        return deviceInfo;
    }

    @Override
    public HashMap<String, Object> selectCompanyIdAndDeviceMode() {
        List<HashMap<String, String>> deviceModelList = hyAstationListMapper.deviceModel();
        List<HashMap<String, String>> companyIdlList = hyAstationListMapper.selectCompanyId();
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("deviceModelList", deviceModelList);
        resultMap.put("companyIdlList", companyIdlList);
        return resultMap;
    }
    @Override
    public List<HashMap<String, String>> deviceList(String plantId) {
        List<HashMap<String, String>> deviceList = hyAstationListMapper.deviceList(plantId);
        return deviceList;
    }
}
