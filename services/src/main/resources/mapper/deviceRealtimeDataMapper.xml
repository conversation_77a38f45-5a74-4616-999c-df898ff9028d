<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.deviceRealtimeDataMapper">

    <resultMap id="BtoDeviceRealtimeDate" type="entityDTO.BtoDeviceRealTimeDTO">
        <result property="sn" column="sn"/>
        <result property="time" column="time"/>
        <result property="ipv1" column="ipv1"/>
        <result property="ipv2" column="ipv2"/>
        <result property="ipv3" column="ipv3"/>
        <result property="vpv1" column="vpv1"/>
        <result property="vpv2" column="vpv2"/>
        <result property="vpv3" column="vpv3"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="facc1" column="fac"/>
        <result property="facc1" column="fac1"/>
        <result property="facc1" column="fac2"/>
        <result property="power" column="power"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
    </resultMap>

    <select id="selectdeviceRealtimeData" resultMap="BtoDeviceRealtimeDate">
        SELECT
            sn,
            time,
            ipv1,
            ipv2,
            ipv3,
            vpv1,
            vpv2,
            vpv3,
            iac1,
            iac2,
            iac3,
            vac1,
            vac2,
            vac3,
            fac,
            fac1,
            fac2,
            power,
            today_energy,
            total_energy,
            update_time
        FROM
            bto_device_${time}
        WHERE
            sn = '${sn}'

    </select>
</mapper>