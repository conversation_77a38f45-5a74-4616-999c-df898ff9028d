package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.PageInfo;
import entityDTO.IncomeStatisticDTO;
import entityDTO.IncomeStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface HYIncomeStatisticMapper extends BaseMapper<IncomeStatisticDTO> {

    /*
     * 收益统计：根据plant_id实现单个或多个查询日发电量
     * */
    List<IncomeStatisticDTO> IncomeStatisticsDay(@Param("plant_id") List<String> plant_id, @Param("emm") String emm, @Param("time2") String time2, @Param("page") Integer page, @Param("pagesize") Integer pageSize);

    /*
     * 收益统计：根据plant_id查询全部电站的日发电量
     * */
    List<IncomeStatisticDTO> IncomeStatisticsAllDay(@Param("emm") String emm, @Param("time2") String time2, @Param("page") Integer page, @Param("pagesize") Integer pageSize);

    /*
     * 收益统计：根据plant_id实现单个或多个查询月发电量
     * */
    List<IncomeStatisticDTO> IncomeStatisticsMonth(@Param("plant_id") List<String> plant_id, @Param("time") String time, @Param("page") Integer page, @Param("pagesize") Integer pageSize);

    /*
     * 收益统计：根据plant_id查询全部电站的月发电量
     * */
    List<IncomeStatisticDTO> IncomeStatisticsAllMonth(@Param("time") String time, @Param("page") Integer page, @Param("pagesize") Integer pageSize);

    /*
     * 收益统计：根据plant_id实现单个或多个查询年发电量
     * */
    List<IncomeStatisticDTO> IncomeStatisticsYear(@Param("plant_id") List<String> plant_id, @Param("time") String time, @Param("page") Integer page, @Param("pagesize") Integer pageSize);

    /*
     * 收益统计：根据plant_id查询全部电站的年发电量
     * */
    List<IncomeStatisticDTO> IncomeStatisticsAllYear(@Param("time") String time, @Param("page") Integer page, @Param("pagesize") Integer pageSize);

    /**
     *查询日-月-年的收益统计数据
     */
    ArrayList<IncomeStatisticsDTO> IncomeStatisticsByCondition(@Param("plantIds") String[] plantIds, @Param("date") String date, @Param("length") Integer length);
}
