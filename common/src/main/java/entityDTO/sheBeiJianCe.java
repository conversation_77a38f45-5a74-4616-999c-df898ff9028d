package entityDTO;

import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.stream.DoubleStream;

@Data
public class sheBeiJianCe implements Serializable {

    private String snName;

    private String sn;

    private double activePower;
    private double power1;
    private double power2;
    private double power3;
    private double power4;
    private double power5;
    private double power6;
    private double power7;
    private double power8;
    private double power9;

    private double iac1;
    private double iac2;
    private double iac3;
    private double iac4;
    private double iac5;
    private double iac6;
    private double iac7;
    private double iac8;
    private double iac9;

    private double vac1;
    private double vac2;
    private double vac3;
    private double vac4;
    private double vac5;
    private double vac6;
    private double vac7;
    private double vac8;
    private double vac9;

    private double fac1;
    private double fac2;
    private double fac3;
    private double fac4;
    private double fac5;
    private double fac6;
    private double fac7;
    private double fac8;
    private double fac9;

    private double temperature;

    private String time;

    private String couser;

    private double pp;

    private String name;

    private String plantId;

    private String plantUid;

    private String createDate;

    private String deviceId;

    private String devicesn;

    private String plant_uid;

}
