<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.IndexMapper">

    <!--总装机容量-->
    <select id="getAllPeakPower" resultType="String">
        SELECT SUM(peak_power) peakPower
        FROM bto_station_list
    </select>
    <!--日月年总发电量-->
    <select id="getEnergy" resultType="java.util.HashMap">
        SELECT SUM(today_energy) todayEnergy,
               SUM(month_energy) monthEnergy,
               SUM(year_energy)  yearEnergy,
               SUM(total_energy) totalEnergy
        FROM bto_device_energy
    </select>
    <!--近七日发电量-->
    <select id="getSevenDayEnergy" resultType="java.util.HashMap">
        SELECT q.date1 as date,totalEnergy
        FROM (
                 SELECT  cast(date as CHAR) date1,
                         SUM(energy) totalEnergy
                 FROM bto_station_day
                 GROUP BY DATE
                 ORDER BY DATE DESC
                 LIMIT 7) q
        ORDER BY date
    </select>
    <!--近六月发电量-->
    <select id="getSixMonthEnergy" resultType="java.util.HashMap">
        SELECT date1 date,
                    totalEnergy
        FROM (
                 SELECT LEFT(DATE, 7) date1,
                        SUM(energy)   totalEnergy
                 FROM bto_station_day
                 GROUP BY date1
                 ORDER BY date1 DESC
                 LIMIT 7) q
        ORDER BY date
    </select>
    <!--总电站数量-->
    <select id="getAllStationNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
    </select>
    <!--离线电站数量-->
    <select id="getOfflineStationNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE `status` = '0'
    </select>
    <!--告警电站数量-->
    <select id="getAlterStationNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE `status` = '2'
    </select>
    <!--正常电站数量-->
    <select id="getNormalStationNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE `status` IN (1, 3)
    </select>
    <!--总逆变器数量-->
    <select id="getAllDeviceNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (device_sn))
        FROM bto_device_list
    </select>
    <!--告警逆变器数量-->
    <select id="getAlertDeviceNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (device_sn))
        FROM bto_device_list
        WHERE device_status = '2'
    </select>
    <!--正常逆变器数量-->
    <select id="getNormalDeviceNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (device_sn))
        FROM bto_device_list
        WHERE device_status IN (1, 3)
    </select>
    <!--离线逆变器数量-->
    <select id="getOfflineDeviceNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (device_sn))
        FROM bto_device_list
        WHERE device_status = '0'
    </select>
    <!--告警记录数量-->
    <select id="alertRecordNum" resultType="java.lang.Integer">
        SELECT
            COUNT( 1 )
        FROM
            bto_device_event
        WHERE
            LEFT ( start_time, 10 ) = CURDATE()
          AND `status` = '0'
          AND alarm_message NOT LIKE 'PV%exception'
    </select>
    <!--电站实时发电列表-轮播 && 站点轮播-->
    <select id="realTimePowerCarousel" resultType="entity.Carousel">
      SELECT
          a.plant_id plantId,
          a.`name` stationName,
          a.peak_power peakPower,
          a.create_date createTime,
          a.city city,
          a.`status` `status`,
          CASE WHEN MAX(b.date_time) > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                   THEN SUM(DISTINCT (b.power)) ELSE 0 END realTimePower,
          CASE WHEN
                   LEFT (MAX(b.date_time), 10) = CURDATE()
                   THEN SUM(DISTINCT(b.today_energy)) ELSE 0 END todayEnergy,
          CASE WHEN b.total_energy !=0
                   THEN SUM(DISTINCT (b.total_energy)) ELSE 0 END totalEnergy,
          CASE WHEN MAX(b.date_time) > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                   THEN (SUM(DISTINCT (b.power)) /(a.peak_power*1000)) ELSE 0 END powerEfficiency,
          CONCAT( LEFT (b.date_time, 15), '0:00') updateTime
      FROM bto_station_list a
               LEFT JOIN v_device_energy b ON a.plant_id=b.plant_id
      GROUP BY a.plant_id
      ORDER BY a.create_date DESC
    </select>


</mapper>