package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/7 11:03
 */
@Data
@ApiModel("运维器图表数据信息实体-用户APP")
public class OperatorChartInfoDTO implements Serializable {
    private static final long serialVersionUID = 6526525477167664203L;
    @ApiModelProperty("电站UID")
    private String plantUID;
    @ApiModelProperty("运维器IMEI码")
    private String IMEI;
    @ApiModelProperty("总发电量")
    private String pvEnergy = "0";
    @ApiModelProperty("总用电量")
    private String useEnergy = "0";
    @ApiModelProperty("自发自用电量(自用光伏)")
    private String selfEnergy = "0";
    @ApiModelProperty("自用电量(总发电量-卖电量)")
    private String selfUseEnergy = "0";
    @ApiModelProperty("卖电量")
    private String sellEnergy = "0";
    @ApiModelProperty("市电电量")
    private String buyEnergy = "0";
    @ApiModelProperty("数据日期")
    private String date;

}
