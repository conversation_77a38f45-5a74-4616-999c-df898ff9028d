package com.botong.controller;

import com.botong.services.service.HYABtoDeviceService;
import com.botong.services.service.HYAstationListService;
import entity.DevicePvInfo;
import entityDTO.AddDeviceForm;
import entityDTO.AppDeviceChartForm;
import entityDTO.StationConditionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

@RestController
@RequestMapping("btodevice")
@Api(tags = "逆变器数据")
@CrossOrigin
public class HYBtoDeviceController {
    @Autowired
    private HYABtoDeviceService hybtoDeviceService;

    @Autowired
    private HYAstationListService hyAstationList;

    @ApiOperation("获取所有逆变器日月年总发电信息")
    @GetMapping("inverterElectricityInfo")
    public AjaxResult inverterElectricityInfo() {
        return AjaxResult.success("操作成功", hybtoDeviceService.inverterElectricityInfo());
    }

    @ApiOperation("河源管理员电站列表查询")
    @GetMapping("HYadminStationList")
    public AjaxResult HYadminStationList() {
        //a: 代表（页数-1）*条目数  b:代表条目数
        return AjaxResult.success("操作成功", hyAstationList.HYadminStationList());
    }

    @ApiOperation("版本查看")
    @GetMapping("versionView")
    public AjaxResult versionView() throws Exception {
        return AjaxResult.success("版本查询成功", hyAstationList.versionView());
    }


    @ApiOperation("查询当前电站所拥有的逆变器")
    @GetMapping("selectDeviceByStation")
    public AjaxResult selectDeviceByStation(String stationId) {
        return AjaxResult.success("操作成功", hybtoDeviceService.selectDeviceByStation(stationId));
    }


    @ApiOperation("查询河源逆变器列表")
    @GetMapping("selectDeviceList")
    public AjaxResult selectDeviceList(String plantId) {
        return AjaxResult.success("操作成功", hybtoDeviceService.selectDeviceList(plantId));
    }

    @ApiOperation("查询电站相关信息")
    @GetMapping("selectStationInfo")
    public AjaxResult selectStationInfo(String stationId) {
        return AjaxResult.success("操作成功", hyAstationList.selectStationInfo(stationId));
    }

    @ApiOperation("app图表")
    @GetMapping("chart")
    public AjaxResult chart(String timeType, String time, String stationId, String deviceSns) {
        return AjaxResult.success("操作成功", hybtoDeviceService.chart(timeType, time, stationId, deviceSns));
    }

    @ApiOperation("app图表")
    @PostMapping("chart1")
    public AjaxResult chart1(@RequestBody AppDeviceChartForm appDeviceChartForm) {
        return AjaxResult.success("操作成功", hybtoDeviceService.chart1(appDeviceChartForm));
    }

    @ApiOperation("app地图")
    @GetMapping("map")
    public AjaxResult map(String stationId) {
        return AjaxResult.success("操作成功", hyAstationList.map(stationId));
    }

    @ApiOperation("根据电站id获取逆变器id")
    @GetMapping("getDeviceIdByStationId")
    public AjaxResult getDeviceIdByStationId(String stationId) {
        return AjaxResult.success("操作成功", hyAstationList.getDeviceIdByStationId(stationId));
    }

    @ApiOperation("查询电站详情主页信息")
    @GetMapping("getStationDetailsHome")
    public AjaxResult getStationDetailsHome(String stationId) {
        return AjaxResult.success("操作成功", hybtoDeviceService.getStationDetailsHome(stationId));
    }

    @ApiOperation("获取逆变器详情信息")
    @GetMapping("getDeviceDetails")
    public AjaxResult getDeviceDetails(String sn) {
        return AjaxResult.success("操作成功", hybtoDeviceService.getDeviceDetails(sn));
    }


    @ApiOperation("获取逆变器版本信息")
    @GetMapping("getDeviceVersionInfo")
    public AjaxResult getDeviceVersionInfo(String sn) {
        return AjaxResult.success("操作成功", hybtoDeviceService.getDeviceVersionInfo(sn));
    }

    @ApiOperation("获取逆变器告警信息")
    @GetMapping("getDeviceAlertInfo")
    public AjaxResult getDeviceAlertInfo(String plantUid,String stationId,Integer pageNum,Integer pageSize) {
        return AjaxResult.success("操作成功", hybtoDeviceService.getDeviceAlertInfo(plantUid,stationId,pageNum,pageSize));
    }

    @ApiOperation("获取电站列表-多条件查询")
    @PostMapping("getStationsByCondition")
    public AjaxResult getStationsByCondition(@RequestBody StationConditionDTO stationCondition) {
        return AjaxResult.success("查询成功",
                hyAstationList.getStationsByCondition(stationCondition));
    }

    @ApiOperation("查询逆变器光伏板详细信息--根据逆变器SN查询")
    @GetMapping("getDevicePvInfo")
    public AjaxResult getDevicePvInfoBySN(String SN) {
        return AjaxResult.success("查询成功", hyAstationList.getDevicePvInfoBySN(SN));
    }

    @ApiOperation("修改逆变器光伏板详细信息")
    @PostMapping("updateStationsByCondition")
    public AjaxResult updateDevicePvInfo(@RequestBody DevicePvInfo devicePvInfo) {
        return AjaxResult.success("查询成功", hyAstationList.updateDevicePvInfo(devicePvInfo));
    }

    @ApiOperation("查询逆变器信息--根据电站ID查询")
    @GetMapping("getDeviceInfoByPlantId")
    public AjaxResult getDeviceInfoByPlantId(String plantId) {
        return AjaxResult.success("查询成功", hyAstationList.getDeviceInfoByPlantId(plantId));
    }

    @ApiOperation("新增逆变器光伏板详细信息")
    @PostMapping("addDeviceInfo")
    public AjaxResult addDeviceInfo(@RequestBody AddDeviceForm addDeviceForm) {
        return AjaxResult.success("查询成功", hyAstationList.addDeviceInfo(addDeviceForm));
    }

    @ApiOperation("查询所属公司id & 逆变器型号")
    @GetMapping("selectCompanyIdAndDeviceMode")
    public AjaxResult selectCompanyIdAndDeviceMode(){
        return AjaxResult.success("查询成功",hyAstationList.selectCompanyIdAndDeviceMode());
    }
    @ApiOperation("逆变器信息列表")
    @GetMapping("deviceList")
    public AjaxResult deviceList(String plantId) {
        return AjaxResult.success("操作成功", hyAstationList.deviceList(plantId));
    }


}
