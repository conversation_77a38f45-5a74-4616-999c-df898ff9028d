package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.ModifyStationForm;
import entityDTO.deviceInformationDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceOperationMapper extends BaseMapper<deviceInformationDTO> {

    List<deviceInformationDTO>  selectDeviceByCtrl(@Param("dateTime")String dateTime,@Param("nearTime") String nearTime);
    Boolean collectDeviceOperation(@Param("plantId") String plantId);

    Boolean cancelCollectDeviceOperation(@Param("plantId") String plantId);

    Boolean deleteDeviceOperation(@Param("plantId") String plantId,@Param("password") String password,@Param("userUid") String userUid);

    Object selectDeviceInformation(@Param("plantId") String plantId);

    Boolean modifyDeviceOperation(@Param("modifyStationForm") ModifyStationForm modifyStationForm);
}
