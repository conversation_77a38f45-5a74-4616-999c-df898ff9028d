package com.botong.controller;

import com.botong.services.service.HYBtoDeviceMapDTOPlusService;
import com.botong.services.service.HYEfficiencyService;
import com.botong.services.service.HYVBaseDeviceDTOService;
import entityDTO.AreaStatisticsForm;
import entityDTO.StationInfoConditionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;
import vo.PowerStatisticVo;

@RestController
@RequestMapping("/BtoDeviceMapDTOPlus")
@Api(tags = "统计报表")
@CrossOrigin
public class HYBtoDeviceMapDTOPlusController {
    @Autowired
    private HYBtoDeviceMapDTOPlusService hybtoDeviceMapDTOPlusService;
    @Autowired
    private HYVBaseDeviceDTOService hyvBaseDeviceDTOService;
    @Autowired
    private HYEfficiencyService hyEfficiencyService;

    @ApiOperation("发电统计——查询日月年数据-三合一")
    @PostMapping("/selectPowerStatistic")
    public AjaxResult selectPowerStatistic(@RequestBody PowerStatisticVo powerStatisticVo){
        return AjaxResult.success("查询成功", hybtoDeviceMapDTOPlusService.selectPowerStatistic(powerStatisticVo));
    }

    @ApiOperation("区域统计——查询日月年数据--三合一")
    @PostMapping("/areaStatistics")
    public AjaxResult areaStatistics(@RequestBody AreaStatisticsForm areaStatisticsForm){
        return AjaxResult.success("查询成功",hyvBaseDeviceDTOService.areaStatistics(areaStatisticsForm));
    }

//    @ApiOperation("区域统计——查询天数据")
//    @GetMapping("/selectDay")
//    public AjaxResult selectDay(String time, String town, String address ,Integer page,Integer pageSize){
//        return AjaxResult.success("查询成功",hyvBaseDeviceDTOService.selectDay(time,town,address,page,pageSize));
//    }
//
//    @ApiOperation("区域统计——查询月数据")
//    @GetMapping("/selectMonth")
//    public AjaxResult selectMonth(String month, String town, Integer page,Integer pageSize){
//        return AjaxResult.success("查询成功",hyvBaseDeviceDTOService.selectMonth(month,town,page,pageSize));
//    }
//
//    @ApiOperation("区域统计——查询年数据")
//    @GetMapping("/selectYear")
//    public AjaxResult selectYear(String year, String town,Integer page,Integer pageSize){
//        return AjaxResult.success("查询成功",hyvBaseDeviceDTOService.selectYear(year,town,page,pageSize));
//    }


    @ApiOperation("发电效率——月发电量-发电效率-环比-同比数据")
    @GetMapping("/EfficiencyDTOSelectMonth")
    public AjaxResult EfficiencyDTOSelectMonth(String plantId,String month,Integer page,Integer pageSize){
        return hyEfficiencyService.PowerGenerationMonth(plantId,month,page,pageSize);
    }

    @ApiOperation("发电效率——年发电量-发电效率-环比-同比数据")
    @GetMapping("/EfficiencyDTOSelectYear")
    public AjaxResult EfficiencyDTOSelectYear(String plantId,String year,Integer page,Integer pageSize){
        return hyEfficiencyService.PowerGenerationYear(plantId,year,page,pageSize);
    }

    @ApiOperation("电站统计——统计电站信息列表--带筛选")
    @PostMapping("/StatisticsStationInfo")
    public AjaxResult StatisticsStationInfoByCondition(@RequestBody StationInfoConditionDTO stationInfoConditionDTO){
        return hyEfficiencyService.StatisticsStationInfoByCondition(stationInfoConditionDTO);
    }

    @ApiOperation("设备统计——列表--带筛选")
    @GetMapping("/operatorDetails")
    public AjaxResult operatorDetails(String userName,String plantId,Integer page,Integer pageSize){
        return AjaxResult.success("查询成功",hyEfficiencyService.operatorDetails(userName,plantId,page,pageSize));
    }
}
