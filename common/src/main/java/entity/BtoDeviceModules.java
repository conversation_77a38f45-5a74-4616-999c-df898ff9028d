package entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 光伏组件
 *
 * <AUTHOR> @date 2022-09-20 15:36:30
 */
@TableName("bto_device_modules")
@Data
public class BtoDeviceModules  {


    /**
     * 电站ID
     */
    //@TableId(type = IdType.ASSIGN_UUID)
    private String plantId;

    /**
     * 电站UID
     */
    //@TableId(type = IdType.ASSIGN_UUID)
//    private String plantUid;

    /**
     * 逆变器sn
     */
    @TableField(value = "sn")
    private String sn;

    /**
     * 品牌
     */
//    private String brand;


    /**
     * 单块光伏板容量
     */
    private Integer capacity;

    /**
     * 总数
     */
//    private Integer total;

    /**
     * DC1组件数量
     */
    private Integer pv1Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv1Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv1Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv1Dc4;

    /**
     * DC1组件数量
     */
    private Integer pv2Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv2Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv2Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv2Dc4;

    /**
     * DC1组件数量
     */
    private Integer pv3Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv3Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv3Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv3Dc4;

    /**
     * DC1组件数量
     */
    private Integer pv4Dc1;

    /**
     * DC2组件数量
     */
    private Integer pv4Dc2;

    /**
     * DC3组件数量
     */
    private Integer pv4Dc3;

    /**
     * DC4组件数量
     */
    private Integer pv4Dc4;

    /**
     * 创建时间
     */
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 系数1
     */
    private Float k1;

    /**
     * 系数2
     */
    private Float k2;

    /**
     * 系数3
     */
    private Float k3;

    /**
     * 系数4
     */
    private Float k4;

    /**
     * 系数5
     */
    private Float k5;

    /**
     * 系数6
     */
    private Float k6;

    /**
     *
     */
    private Integer pv5Dc1;

    /**
     *
     */
    private Integer pv5Dc2;

    /**
     *
     */
    private Integer pv5Dc3;

    /**
     *
     */
    private Integer pv5Dc4;

    /**
     *
     */
    private Float k7;

    /**
     *
     */
    private Float k8;

    /**
     *
     */
    private Integer pv6Dc1;

    /**
     *
     */
    private Integer pv6Dc2;

    /**
     *
     */
    private Integer pv6Dc3;

    /**
     *
     */
    private Integer pv6Dc4;

    /**
     *
     */
    private Float k9;

    /**
     *
     */
    private Float k10;

    /**
     *
     */
    private Integer pv7Dc1;

    /**
     *
     */
    private Integer pv7Dc2;

    /**
     *
     */
    private Integer pv7Dc3;

    /**
     *
     */
    private Integer pv7Dc4;

    /**
     *
     */
    private Float k11;

    /**
     *
     */
    private Float k12;

    /**
     *
     */
    private Integer pv8Dc1;

    /**
     *
     */
    private Integer pv8Dc2;

    /**
     *
     */
    private Integer pv8Dc3;

    /**
     *
     */
    private Integer pv8Dc4;

    /**
     *
     */
    private Float k13;

    /**
     *
     */
    private Float k14;

    /**
     *
     */
    private Integer pv9Dc1;

    /**
     *
     */
    private Integer pv9Dc2;

    /**
     *
     */
    private Integer pv9Dc3;

    /**
     *
     */
    private Integer pv9Dc4;

    /**
     *
     */
    private Float k15;

    /**
     *
     */
    private Float k16;

    /**
     *
     */
    private Integer pv10Dc1;

    /**
     *
     */
    private Integer pv10Dc2;

    /**
     *
     */
    private Integer pv10Dc3;

    /**
     *
     */
    private Integer pv10Dc4;

    /**
     *
     */
    private Float k17;

    /**
     *
     */
    private Float k18;

    /**
     *
     */
    private Integer pv11Dc1;

    /**
     *
     */
    private Integer pv11Dc2;

    /**
     *
     */
    private Integer pv11Dc3;

    /**
     *
     */
    private Integer pv11Dc4;

    /**
     *
     */
    private Float k19;

    /**
     *
     */
    private Float k20;

    /**
     *
     */
    private Integer pv12Dc1;

    /**
     *
     */
    private Integer pv12Dc2;

    /**
     *
     */
    private Integer pv12Dc3;

    /**
     *
     */
    private Integer pv12Dc4;

    /**
     *
     */
    private Float k21;

    /**
     *
     */
    private Float k22;

}
