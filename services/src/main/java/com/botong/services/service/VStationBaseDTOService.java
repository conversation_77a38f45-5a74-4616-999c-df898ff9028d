package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.VStationBaseDTO;

import java.util.List;

public interface VStationBaseDTOService extends IService<VStationBaseDTO> {
    PageInfo<VStationBaseDTO> selectVStationBaseDTO(Integer page, Integer pageSize, String time);

    List<VStationBaseDTO> selectStationBaseSign(String time);

    List<VStationBaseDTO> selectStationBasePlantId(String time,String[] plantIds);

    PageInfo<VStationBaseDTO> selectVStationBaseDTOSign(Integer page, Integer pageSize, String time ,  String  Param ,String sort);

    String updateVStationBase(String plantId);

    PageInfo<VStationBaseDTO> VStationBaseDTOPx2(Integer page, Integer pageSize, String time, String param, String sort);

    Object selectVStationBaseDTOPx2XcCount();
}
