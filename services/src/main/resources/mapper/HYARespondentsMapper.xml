<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYARespondentsMapper">

    <resultMap type="entityDTO.HYARespondentsDTO" id="EfficiencyDTOMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="userName" column="userName"/>
        <result property="userUid" column="userUid"/>
        <result property="plantId" column="plantId"/>
        <result property="plantName" column="plantName"/>
        <result property="createDate" column="createTime"/>
        <result property="dele" column="dele"/>
        <result property="author" column="author"/>
        <result property="plantname" column="plantname"/>
    </resultMap>
    <resultMap type="entityDTO.stationListName" id="stationListNameMap">
        <result property="dayElectricity" column="dayElectricity"/>
        <result property="monthElectricity" column="monthElectricity"/>
        <result property="yearElectricity" column="yearElectricity"/>
        <result property="allElectricity" column="allElectricity"/>
        <result property="totalenergy" column="totalenergy"/>
        <result property="todayenergy" column="todayenergy"/>
        <result property="monthenergy" column="monthenergy"/>
        <result property="yearenergy" column="yearenergy"/>
        <result property="CountPlantId" column="CountPlantId"/>
        <result property="ONLINE" column="ONLINE"/>
        <result property="alert" column="alert"/>
        <result property="countSn" column="countSn"/>
        <result property="sumpeakPower" column="sumpeakPower"/>
        <result property="sn" column="sn"/>
        <result property="mean" column="mean"/>
        <result property="alarmcode" column="alarmcode"/>
        <result property="startTime" column="startTime"/>
        <result property="grade" column="grade"/>
        <result property="name1" column="name1"/>
    </resultMap>


    <resultMap type="entityDTO.HYABtoUserListDTO" id="HYABtoUserListDTOMap">
        <result property="cUserId" column="cUserId"/>
        <result property="cUserUid" column="cUserUid"/>
        <result property="cUserName" column="cUserName"/>
        <result property="cUserTel" column="cUserTel"/>
        <result property="delFlag" column="delFlag"/>
        <result property="cUserRegtime" column="cUserRegtime"/>
        <result property="updateDate" column="updateDate"/>
        <result property="cUserEmail" column="cUserEmail"/>
        <result property="updateTime" column="updateTime"/>
        <result property="createTime" column="createTime"/>
        <result property="password" column="password"/>
        <result property="special" column="special"/>
        <result property="enginerId" column="enginerId"/>
        <result property="subAcc" column="subAcc"/>
    </resultMap>


    <resultMap type="entityDTO.HYABtoSonPlantDTO" id="HYABtoSonPlantDTOMap">
        <result property="plantId" column="plantId"/>
        <result property="respondentId" column="respondentId"/>
        <result property="createTime" column="createTime"/>
    </resultMap>

    <resultMap type="vo.StationListVo" id="selectSubStationListMap">
        <result property="name" column="name"/>
        <result property="realPower" column="realpower"/>
        <result property="todayenergy" column="todayenergy"/>
        <result property="totalenergy" column="totalenergy"/>
        <result property="address1" column="address1"/>
        <result property="createDate" column="create_date"/>
        <result property="plantname" column="plantname"/>
        <result property="createDate" column="createDate"/>
        <result property="plantId" column="plantId"/>
        <result property="time" column="time"/>
        <result property="status" column="status"/>
    </resultMap>

    <insert id="AddUserPondents">
        insert into bto_respondents_list (respondent_id, name, user_name, user_uid, create_time, author)
        values ('${RespondentId}', '${name}', '河源管理员heyuanapp', '${CUserUid}', '${createTime}', ${author})
    </insert>

    <select id="SelEctRespondentsList" resultType="entityDTO.HYARespondentsDTO">
        select name, plant_name, author
        from bto_respondents_list
    </select>

    <insert id="AddUserList">
        insert into bto_user_list (c_user_uid, c_user_name, c_user_regtime)
        values ('${uuid}', '${username}', '${cuserRegtime}')
    </insert>

    <select id="SelUserList" resultType="entityDTO.HYABtoUserListDTO">
        SELECT *
        FROM bto_station_list
        WHERE special = 1
          and bto_station_list.plant_id = '${plantId}';
    </select>

    <select id="selheyuanapp" resultType="entityDTO.HYABtoUserListDTO">
        select c_user_name
        from bto_user_list
        where bto_user_list.c_user_uid = '${userUid}';
    </select>

    <select id="loginSelsubAccount" resultMap="stationListNameMap">
        select name
        from bto_respondents_list
        where name = '${userName}'
    </select>

    <select id="visitorList" resultType="entityDTO.HYARespondentsDTO">
        SELECT a.NAME   username,
               c.NAME   plantname,
               a.author author
        FROM bto_respondents_list a
                 LEFT JOIN bto_son_plant b ON a.respondent_id = b.respondent_id
                 LEFT JOIN v_station_list c ON b.plant_id = c.plant_id
        WHERE b.plant_id = '${plantId}'
    </select>

    <select id="checkSubAccount" resultType="java.lang.Object">
        SELECT bto_son_plant.respondent_id, bto_son_plant.plant_id
        from bto_respondents_list
                 left join bto_son_plant on bto_son_plant.respondent_id = bto_respondents_list.respondent_id
        where name = '${userName}'
          AND bto_son_plant.plant_id = '${plantId}'
    </select>


    <select id="removePowerStation" resultType="java.lang.Integer">
        delete
        from bto_son_plant
        where plant_id = '${plantId}'
          and respondent_id = '${userUid}'
    </select>
    <select id="selectSubStationList" resultMap="selectSubStationListMap">
        SELECT
        a.`name`,
        a.plant_id,
        a.address1,
        a.create_date,
        sum(
        DISTINCT ( power )) real_power,
        sum(
        DISTINCT ( today_energy )) todayenergy,
        sum( DISTINCT ( total_energy ) ) totalenergy
        FROM
        `v2_hy_station` a
        LEFT JOIN (
        SELECT
        q.plant_id,
        w.power,
        w.today_energy,
        w.total_energy,
        w.time
        FROM
        v_device_list q
        LEFT JOIN ( SELECT sn, power, today_energy, total_energy, time FROM bto_device_${time} GROUP BY SN, time ORDER
        BY time DESC ) w ON q.device_sn = w.sn
        WHERE
        sn IS NOT NULL
        GROUP BY
        sn
        ) b ON a.plant_id = b.plant_id
        WHERE
        a.respondent_id = #{userUid}
        GROUP BY
        a.plant_id
        ORDER BY
        <choose>
            <when test="sortWith == 0">
                create_date ASC
            </when>
            <when test="sortWith == 1">
                create_date desc
            </when>
            <when test="sortWith == 2">
                totalenergy asc
            </when>
            <when test="sortWith == 3">
                totalenergy desc
            </when>
            <otherwise>
                create_date desc
            </otherwise>
        </choose>
    </select>
    <select id="filterSubStationList" resultType="entityDTO.NewHYAstationList">
        SELECT * FROM(
        SELECT
        a.`name` plantname,
        CASE
        WHEN sum(
        DISTINCT ( power ))> 10 THEN
        1 ELSE 0
        END stated,
        a.plant_id,
        a.plant_type,
        a.peak_power,
        a.town,
        a.address1,
        a.create_date,
        sum(
        DISTINCT ( power )) real_power,
        sum(
        DISTINCT ( today_energy )) todayenergy,
        sum( DISTINCT ( total_energy ) ) totalenergy
        FROM
        `v2_hy_station` a
        LEFT JOIN (
        SELECT
        q.plant_id,
        w.power,
        w.today_energy,
        w.total_energy,
        w.time
        FROM
        v_device_list q
        LEFT JOIN ( SELECT sn, power, today_energy, total_energy, time FROM bto_device_${time}
        GROUP BY SN, time ORDER BY time DESC ) w ON q.device_sn = w.sn
        WHERE
        sn IS NOT NULL
        GROUP BY
        sn
        ) b ON a.plant_id = b.plant_id
        WHERE
        a.respondent_id = #{userUid}
        GROUP BY
        a.plant_id)p
        <where>
            stated = #{stationConditionDTO.status}
            <if test="stationConditionDTO.status != ''">
                and plant_type = #{stationConditionDTO.type}
            </if>
            <if test="stationConditionDTO.stationName != ''">
                and plantname like '%${stationConditionDTO.stationName}%'
            </if>
            <if test="stationConditionDTO.town != ''">
                and town = #{stationConditionDTO.town}
            </if>
            <if test="stationConditionDTO.minPeakPower != '' and stationConditionDTO.maxPeakPower != ''">
                AND peak_power BETWEEN ${stationConditionDTO.minPeakPower} AND ${stationConditionDTO.maxPeakPower}
            </if>
        </where>
    </select>

    <select id="selUser" resultType="entity.BtoUserList">
        SELECT c_user_name
        from bto_user_list
        where c_user_name = '${userName}'
    </select>

    <select id="SelEctRespondentsList2" resultType="entityDTO.HYABtoUserListDTO">
        select name
        from bto_respondents_list
        where name = '${username}'
    </select>
    <select id="seltest" resultType="entityDTO.HYABtoUserListDTO">
        select c_user_name, c_user_uid
        from bto_user_list
        where c_user_name = '${username}'
    </select>
    <select id="hYSubAccDayMonthAll" resultType="java.lang.String">
        SELECT sum(today_energy) todayenergy,
               sum(month_energy) monthenergy,
               sum(year_energy)  yearenergy,
               sum(total_energy) totalenergy
        FROM (
                 SELECT today_energy,
                        month_energy,
                        year_energy,
                        total_energy
                 FROM v2_hy_son aa
                          LEFT JOIN (
                     SELECT sn,
                            today_energy,
                            month_energy,
                            year_energy,
                            total_energy
                     FROM ${day}
                     WHERE time like '${day2}'
                     GROUP BY SN,
                              time
                     ORDER BY time DESC
                 ) bb ON aa.device_sn = bb.sn
                 WHERE aa.respondent_id = '${userUid}'
                   AND sn IS NOT NULL
                 GROUP BY sn
             ) q
    </select>
    <select id="StationNum" resultType="java.lang.String">
        SELECT count(plant_id) CountPlantId
        FROM v2_hy_son
        WHERE respondent_id = '${userUid}'
    </select>
    <select id="powerStationOnline" resultType="java.lang.String">
        SELECT count(
                       DISTINCT (a.plant_id))
        FROM bto_station_list a
                 LEFT JOIN v2_hy_son b ON a.plant_Id = b.plant_id
        WHERE b.respondent_id = #{userUid}
          AND a.special = '1'
          AND a.status in (1, 2)
    </select>

    <select id="AlarmPowerStation" resultType="java.lang.String">
        SELECT count(
                       DISTINCT (a.plant_id))
        FROM bto_station_list a
                 LEFT JOIN v2_hy_son b ON a.plant_Id = b.plant_id
        WHERE b.respondent_id = #{userUid}
          AND a.special = '1'
          AND a.status = '2'
    </select>

    <select id="AlarmInverter" resultType="java.lang.Integer">
        SELECT count(a.sn) countSn
        FROM V_device_event a
                 LEFT JOIN v2_hy_son b ON a.sn = b.device_sn
        WHERE b.respondent_id = '${userUid}'
          AND start_time LIKE '${curTime}'
          AND end_time &gt; DATE_SUB(now(), INTERVAL 30 MINUTE)
    </select>

    <select id="TotalInstalledCapacity" resultType="java.lang.String">
        SELECT sum(peak_power) sumpeakPower
        FROM v_station_list a
                 LEFT JOIN v2_hy_son b ON a.plant_id = b.plant_id
        WHERE b.respondent_id = '${userUid}'
    </select>

    <select id="stationListAccsucess" resultType="entityDTO.stationListName">
        SELECT a.`name`                         name,
               a.plant_id                       plantid,
               a.address1                       address1,
               a.create_date                    createdate,
               sum(
                       DISTINCT (power))        realpower,
               sum(
                       DISTINCT (today_energy)) todayenergy,
               sum(DISTINCT (total_energy))     totalenergy
        FROM `v2_hy_station` a
                 LEFT JOIN (
            SELECT q.plant_id,
                   w.power,
                   w.today_energy,
                   w.total_energy,
                   w.time
            FROM v_device_list q
                     LEFT JOIN (SELECT sn, power, today_energy, total_energy, time
                                FROM '${day}'
                                GROUP BY SN, time
                                ORDER BY time DESC) w ON q.device_sn = w.sn
            WHERE sn IS NOT NULL
            GROUP BY sn
        ) b ON a.plant_id = b.plant_id
        WHERE a.respondent_id = '${userUid}'
        GROUP BY a.plant_id
    </select>
    <select id="selPassword" resultType="entityDTO.HYABtoUserListDTO">
        select password
        from bto_user_list
        where c_user_uid = '${userUid}'
          and password = '${password}'
    </select>
    <select id="selUser2" resultType="entity.BtoUserList">
        select plant_id, respondent_id
        from bto_son_plant
        where respondent_id = '${userUid}'
          and plant_id = '${plantId}'
    </select>
    <select id="hYSubAccDayMonthAll2" resultMap="stationListNameMap">
        SELECT sum(today_energy) todayenergy,
               sum(month_energy) monthenergy,
               sum(year_energy)  yearenergy,
               sum(total_energy) totalenergy
        FROM (
                 SELECT today_energy,
                        month_energy,
                        year_energy,
                        total_energy
                 FROM v2_hy_son aa
                          LEFT JOIN (
                     SELECT sn,
                            today_energy,
                            month_energy,
                            year_energy,
                            total_energy
                     FROM ${day}
                     WHERE time like '${day2}'
                     GROUP BY SN,
                              time
                     ORDER BY time DESC
                 ) bb ON aa.device_sn = bb.sn
                 WHERE aa.respondent_id = '${userUid}'
                   AND sn IS NOT NULL
                 GROUP BY sn
             ) q
    </select>


    <insert id="AddUsersonPondents">
        insert into bto_son_plant (plant_id, respondent_id, create_time)
        values ('${plantId}', '${respondentId}', '${createTime}')
    </insert>


    <select id="selectSubStationList2" resultMap="selectSubStationListMap">
        SELECT
        a.`name` plantname,
        a.plant_id plantId,
        a.plant_uid plantUid,
        a.address1,
        a.create_date createDate,
        b.time,
        sum(
        DISTINCT ( power )) realpower,
        sum(
        DISTINCT ( today_energy )) todayenergy,
        sum( DISTINCT ( total_energy ) ) totalenergy,
        a.status status
        FROM
        `v2_hy_station` a
        LEFT JOIN (
        SELECT
        q.plant_id,
        w.power,
        w.today_energy,
        w.total_energy,
        w.time,
        q.device_status
        FROM
        v_device_list q
        LEFT JOIN ( SELECT sn, power, today_energy, total_energy, time FROM bto_device_${time} GROUP BY SN, time ORDER
        BY time DESC ) w ON q.device_sn = w.sn
        WHERE
        sn IS NOT NULL
        GROUP BY
        sn
        ) b ON a.plant_id = b.plant_id
        WHERE
        a.respondent_id = #{userUid}
        GROUP BY
        a.plant_id
        ORDER BY
        <choose>
            <when test="sortWith == 1">
                create_date ASC
            </when>
            <when test="sortWith == 2">
                create_date desc
            </when>
            <when test="sortWith == 3">
                totalenergy asc
            </when>
            <when test="sortWith == 4">
                totalenergy desc
            </when>
            <otherwise>
                create_date desc
            </otherwise>
        </choose>
    </select>


    <select id="filterSubStationList2" resultType="entityDTO.NewHYAstationList">
        SELECT * FROM(
        SELECT
        a.`name` plantname,
        CASE
        WHEN b.time>(SELECT DATE_SUB( now(), INTERVAL 20 MINUTE )) THEN
        1 ELSE 0
        END stated,
        a.plant_id,
        a.plant_uid plantUid,
        a.plant_type,
        a.peak_power,
        a.town,
        a.address1,
        a.create_date,
        a.status status,
        sum(
        DISTINCT ( power )) real_power,
        sum(
        DISTINCT ( today_energy )) todayenergy,
        sum( DISTINCT ( total_energy ) ) totalenergy
        FROM
        `v2_hy_station` a
        LEFT JOIN (
        SELECT
        q.plant_id,
        q.device_status,
        w.power,
        w.today_energy,
        w.total_energy,
        w.time
        FROM
        v_device_list q
        LEFT JOIN ( SELECT sn, power, today_energy, total_energy, time FROM bto_device_${time}
        GROUP BY SN, time ORDER BY time DESC ) w ON q.device_sn = w.sn
        WHERE
        sn IS NOT NULL
        GROUP BY
        sn
        ) b ON a.plant_id = b.plant_id
        WHERE
        a.respondent_id = #{userUid}
        GROUP BY
        a.plant_id)p
        <where>
            1=1
            <if test="stationConditionDTO.status != '' and stationConditionDTO.status != null">
                AND status = #{stationConditionDTO.status}
            </if>
            <if test="stationConditionDTO.type != '' and stationConditionDTO.type != null">
                AND plant_type = #{stationConditionDTO.type}
            </if>
            <if test="stationConditionDTO.stationName != ''and stationConditionDTO.stationName != null">
                AND plantname LIKE '%${stationConditionDTO.stationName}%'
            </if>
            <if test="stationConditionDTO.town != '' and stationConditionDTO.town != null">
                AND town = #{stationConditionDTO.town}
            </if>
            <if test="stationConditionDTO.minPeakPower != '' and stationConditionDTO.maxPeakPower != ''
and stationConditionDTO.minPeakPower != null and stationConditionDTO.maxPeakPower != null">
                AND peak_power BETWEEN ${stationConditionDTO.minPeakPower} AND ${stationConditionDTO.maxPeakPower}
            </if>
        </where>
    </select>

    <select id="selUser3" resultType="entity.BtoUserList">
        SELECT *
        FROM bto_user_list
                 LEFT JOIN bto_son_plant on bto_user_list.c_user_uid = bto_son_plant.respondent_id
        WHERE bto_son_plant.respondent_id = '${userUid}'
    </select>
    <select id="seluserName" resultType="entity.BtoUserList">
        select *
        from bto_respondents_list
        where bto_respondents_list.name = '${username}'
    </select>
    <select id="SubaccountPowerPlantAlarm" resultType="entityDTO.stationListName">
        SELECT a.sn         sn,
               a.mean       mean,
               a.alarm_code alarmcode,
               a.start_time startTime,
               a.grade      grade,
               b.plant_name name1
        FROM v_event_mean a
                 LEFT JOIN v2_hy_son b ON a.sn = b.device_sn
        WHERE b.respondent_id = '${userUid}'
          AND a.status = '0'
          AND a.start_time LIKE '${day2}'
    </select>
    <select id="checkSubAccountNum2" resultMap="HYABtoUserListDTOMap">
        SELECT user_name
        FROM bto_respondents_list
        where user_name = '${userName}'
    </select>
    <select id="checkSubAccountNum3" resultType="entity.BtoUserList">
        select name
        from bto_respondents_list
        where name = '${accountName}'
    </select>
    <select id="checkSubAccountNum4" resultType="entity.BtoUserList">
        select respondent_id
        from bto_respondents_list
        where name = '${accountName}'
    </select>
    <select id="checkSubAccountNum5" resultType="entity.BtoUserList">
        SELECT bto_son_plant.respondent_id
        FROM bto_respondents_list
                 LEFT JOIN bto_son_plant ON bto_respondents_list.respondent_id = bto_son_plant.respondent_id
        WHERE bto_son_plant.respondent_id = '${getid}'
    </select>
    <select id="checkSubAccountNum" resultType="entity.BtoUserList">
        select count(user_name)
        FROM bto_respondents_list
        WHERE user_name = '${userName}'
    </select>

    <select id="selectCountAccountByUserId" resultType="java.lang.Integer">
        select count(#{useruid})
        from bto_respondents_list;
    </select>
    <select id="selectRepeatHyAdmin" resultType="java.lang.Integer">
        select count(1)
        from bto_respondents_list
        where name like '%河源%';
    </select>
    <select id="selectRepeat" resultType="Integer">
        select count(1)
        from bto_respondents_list
        where name like '%${username}%';
    </select>
    <select id="selectplantIdRepeat" resultType="java.lang.Integer">
        select count(1)
        from bto_son_plant
        where plant_id = '${plantId}'
          and respondent_id = (select respondent_id from bto_respondents_list where name = '${username}');

    </select>

    <select id="getDayElectricity" resultType="java.lang.Double">
        SELECT sum(today_energy)
        FROM v2_hy_son a
                 LEFT JOIN bto_device_total b on a.plant_id = b.plant_id
        WHERE b.special = '1'
          AND a.respondent_id = #{userUid}
          AND LEFT(update_time, 10) = curdate();#日
    </select>
    <select id="getMonthElectricity" resultType="java.lang.Double">
        SELECT sum(month_energy)
        FROM v2_hy_son a
                 LEFT JOIN bto_device_total b on a.plant_id = b.plant_id
        WHERE b.special = '1'
          AND a.respondent_id = #{userUid}
          AND LEFT(update_time, 7) = left(curdate(), 7);#月
    </select>
    <select id="getAllElectricity" resultType="java.lang.Double">
        SELECT sum(total_energy)
        FROM v2_hy_son a
                 LEFT JOIN bto_device_total b on a.plant_id = b.plant_id
        WHERE b.special = '1'
          AND a.respondent_id = #{userUid};#总
    </select>
    <select id="OperatorAlarmInformationList" resultType="vo.OperatorInfoVO">
        SELECT b.name             stationName,
               a.plant_uid        plantUid,
               a.wisdom_device_sn imei,
               a.alarm_name       alarmName,
               a.alarm_time       alarmTime,
               a.a_point_voltage  aPointVoltage,
               a.b_point_voltage  bPointVoltage,
               a.c_point_voltage  cPointVoltage
        FROM bto_operation_fault a
                 LEFT JOIN v2_hy_station b ON a.plant_uid = b.plant_uid
        WHERE b.special = '1'
          AND b.respondent_id = #{userUid}
          AND LEFT(a.alarm_time, 10) = CURDATE()
          AND a.end_time IS NOT NULL
          AND a.end_time &lt; DATE_SUB(now(), INTERVAL 10 MINUTE)
    </select>
    <select id="getWarnOperatorNum" resultType="java.lang.Integer">
        SELECT count(*)
        FROM bto_operation_fault a
                 LEFT JOIN v2_hy_station b ON a.plant_uid = b.plant_uid
        WHERE b.special = '1'
          AND b.respondent_id = #{userUid}
          AND LEFT(a.alarm_time, 10) = CURDATE()
          AND a.end_time > DATE_SUB(now(), INTERVAL 1 MINUTE);
    </select>

</mapper>