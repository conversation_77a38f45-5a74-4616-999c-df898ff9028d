package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/7 10:06
 */
@Data
@ApiModel("运维器告警信息实体-用户APP")
public class OperatorAlarmInfoDTO implements Serializable {
   @ApiModelProperty(value = "运维器IMEI",notes="wisdom_device_sn")
    private String IMEI;
    @ApiModelProperty("告警名称")
    private String alarmName;
    @ApiModelProperty("告警时间")
    private String alarmTime;
    @ApiModelProperty("A点电压")
    private String aPointVoltage;
    @ApiModelProperty("B点电压")
    private String bPointVoltage;
    @ApiModelProperty("C点电压")
    private String cPointVoltage;
}
