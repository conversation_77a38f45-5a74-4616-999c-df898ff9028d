package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HYAPPIndexService{

    /**
     * 获取所有、正常、告警、在线电站数量、告警记录数
     * @return
     */
    HashMap<String,Integer> getStationNum();

    /**
     * 获取年-月-日-总发电量和总装机容量
     * @return
     */
    HashMap<String,Double> getEnergyAndPeakPower();


    Object TotalInstalledCapacity();
}
