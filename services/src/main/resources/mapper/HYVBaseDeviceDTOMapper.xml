<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.HYVBaseDeviceDTOMapper">
    <resultMap type="entityDTO.VBaseDeviceDTO" id="vBaseDeviceDTOMap">
        <result property="sn" column="sn"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="time" column="time"/>
        <result property="name" column="name"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="address" column="address"/>
    </resultMap>

    <select id="selectAllSn" resultType="String">
        SELECT distinct a.sn sn
        FROM bto_device_${table} a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
    </select>

    <select id="selectSn" resultType="String">
        SELECT distinct a.sn sn
        FROM bto_device_${table} a
                 LEFT JOIN v_base_device b ON a.sn = b.sn
        WHERE state LIKE #{state}
          AND city LIKE #{city}
          AND address1 LIKE #{address}
    </select>

    <select id="selectMonthSn" resultType="String">
        SELECT distinct b.sn sn
        FROM v_day a
                 LEFT JOIN v_base_device b ON a.plant_id = b.plant_id
        WHERE state LIKE #{state}
          AND city LIKE #{city}
          AND b.address1 LIKE #{address}
    </select>

    <select id="areaStatisticsByDay" resultType="entityDTO.AreaDeviceDTO">
        SELECT a.sn        sn,
               today_energy energy,
               time,
               b.address1 address,
               b.`name`    stationName,
               b.state     state,
               b.city      city,
               b.town      town,
               b.plant_uid plantUid
        FROM bto_device_${tableName} a
                 RIGHT JOIN v_base_device b
                            ON a.sn = b.sn
        WHERE b.special = '1'
          AND LEFT(time , 10) = #{time}
          AND town = #{town}
        ORDER BY time
    </select>

    <select id="areaStatisticsByMonth" resultType="entityDTO.AreaDeviceDTO">
        SELECT b.sn,
               a.date      time,
               a.energy    energy,
               b.address1 address,
               b.`name`    stationName,
               b.state     state,
               b.city      city,
               b.town      town,
               b.plant_uid plantUid
        FROM v_day a
                 RIGHT JOIN v_base_device b
                            ON a.plant_id = b.plant_id
        WHERE b.special = '1'
          AND LEFT(a.date
                  , 7) = #{time}
          AND town = #{town}
        ORDER BY time
    </select>

    <select id="areaStatisticsByYear" resultType="entityDTO.AreaDeviceDTO">
        SELECT b.sn,
               LEFT(a.date, 7) time,
               sum(a.energy)   energy,
               b.address1 address,
               b.`name`        stationName,
               b.state         state,
               b.city          city,
               b.town          town,
               b.plant_uid     plantUid
        FROM v_day a
                 RIGHT JOIN v_base_device b
                            ON a.plant_id = b.plant_id
        WHERE b.special = '1'
          AND LEFT(a.date, 4) = #{time}
          AND town = #{town}
        GROUP BY time, b.sn
    </select>


</mapper>