package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.services.vo.InverterVo;
import entity.BtoDeviceList;
import entityDTO.BtoDeviceListDTO;
import entityDTO.TUBIAO;
import entityDTO.sheBeiJianCe;
import entityDTO.stationNum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.EfficiencyVo;

import java.util.List;

@Mapper
public interface HYABtoDeviceListMapper extends BaseMapper<BtoDeviceList> {

    List<BtoDeviceListDTO> AllStationEnergy(@Param("dateTime") String dateTime, @Param("time2") String time2);

    List<BtoDeviceList> DeviceInformation(@Param("plantUid") String plantUid,@Param("tableNme") String tableNme,@Param("nowtime10") String nowtime10,@Param("nowtime20") String nowtime20);

    List<BtoDeviceList> DeviceInformationSN(String plantUid);

    List<TUBIAO> DeviceInformationTU(@Param("plantUid") String plantUid, @Param("tableNme") String tableNme, @Param("tabletime2")String tabletime2);

    List<BtoDeviceList> DeviceMonitorUlist();

    List<sheBeiJianCe> DeviceActivePower(@Param("snName") String snName, @Param("tableNme") String tableNme, @Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> Devicecurrent(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> DeviceVoltage(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> DeviceFrequency(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> Devicetemperature(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);

    List<EfficiencyVo> workEfficiencyRanking(@Param("tableName") String tableName, @Param("time") String time);

    List<sheBeiJianCe> collectionMaxMin(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> collectioAvg(@Param("snName") String snName,@Param("tableNme") String tableNme,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> selectListt(@Param("page") String page,@Param("pageSize") String pageSize,@Param("tableNme") String tableNme,@Param("Nowtime20")String Nowtime20,@Param("Nowtime10")String Nowtime10);

    List<sheBeiJianCe> EquipmentMonitoringlike(@Param("devicesn") String devicesn,@Param("deviceId") String deviceId,@Param("tableNme")String tableNme,@Param("Nowtime20")String Nowtime20,@Param("Nowtime10")String Nowtime10,@Param("plantUid")String plantUid);

    List<stationNum> devicealert(@Param("tableName")String tableName);

    List<stationNum> Deviceonline(@Param("tableName")String tableName,@Param("tableNme2") String tableNme2);

    List<sheBeiJianCe> EquipmentMonitoringplantUid(@Param("page") String page,@Param("pageSize") String pageSize, @Param("tableNme")String tableNme,@Param("Nowtime20")String Nowtime20,@Param("Nowtime10")String Nowtime10,@Param("plantUid") String plantUid);

    List<InverterVo> getInverterByPlantId(@Param("plantId")String plantId);

    Object GetBasicInverterInformation(@Param("sn") String sn);

    List<BtoDeviceList> InverterOfflineList(@Param("page") String page,@Param("pageSize") String pageSize,@Param("time1") String time1,@Param("time3") String time3);
}

