package entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel("轮播实体类--光伏Web")
@NoArgsConstructor
@AllArgsConstructor
public class Carousel implements Serializable,Comparable<Carousel> {
    @ApiModelProperty(value = "电站序号",notes = "电站序号")
    private Integer stationNo;

    @ApiModelProperty(value = "电站ID",notes = "电站ID")
    private String plantId;

    @ApiModelProperty(value = "电站名称",notes = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "装机容量",notes = "装机容量")
    private String peakPower;

    @ApiModelProperty(value = "实时功率",notes = "实时功率")
    private String realTimePower;

    @ApiModelProperty(value = "发电效率",notes = "发电效率")
    private String powerEfficiency;

    @ApiModelProperty(value = "电站状态",notes = "电站状态")
    private String status;

    @ApiModelProperty(value = "电站所在城市",notes = "电站所在城市")
    private String city;

    @ApiModelProperty(value = "日发电量",notes = "日发电量")
    private String todayEnergy;

    @ApiModelProperty(value = "总发电量",notes = "总发电量")
    private String totalEnergy;

    @ApiModelProperty(value = "Co2减排",notes = "Co2减排")
    private String co2;

    @ApiModelProperty(value = "建站时间",notes = "建站时间")
    private String createTime;

    @ApiModelProperty(value = "数据更新时间",notes = "数据更新时间")
    private String updateTime;

    //覆写compareto()的方法
    @Override
    public int compareTo(Carousel carousel) {
        return this.stationName.compareTo(carousel.getStationName());
    }

}
