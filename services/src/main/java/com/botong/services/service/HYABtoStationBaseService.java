package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.services.vo.MapVo;
import entity.BtoStationBase;
import vo.StationBaseVo;

import java.util.ArrayList;
import java.util.HashMap;

public interface HYABtoStationBaseService extends IService<BtoStationBase> {


    ArrayList<StationBaseVo> provincialNumber();


    ArrayList<StationBaseVo> municipalNumber();

    Object stationBaseDetail();

    Object getProvincialStation(String type, String areaName);

    Object getAllStation();

    HashMap<String, Object> getDeforestation();


    BtoStationBase getLatitudeAndLongitude(String plantId);

    HashMap<String, Object> userStationInfo(String userUid);

    HashMap<String, Object> userElectricityInfo(String plantId);

    HashMap<String, Object> userChartInfo(String plantId, String timeType, String time, String deviceSns,String tableName);

    MapVo userMapInfo(String plantId);
}

