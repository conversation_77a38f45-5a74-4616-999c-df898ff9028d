package vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 18:18
 */
@Data
@ApiModel("告警信息查询条件-用户APP")
public class AlarmInfoOnAppConditionVO implements Serializable {
    @ApiModelProperty("当前页")
    private Integer currentPage;
    @ApiModelProperty("页面大小")
    private Integer pageSize;
    @ApiModelProperty("用户UID")
    private String userUID;
    @ApiModelProperty("电站Id数组")
    private List<String> plantIds;
    @ApiModelProperty("查询类型：0:(逆变器实时) 1:(逆变器历史) 2:(运维器实时) 3:(运维器报警)")
    private String type;
}
