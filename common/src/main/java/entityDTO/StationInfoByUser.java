package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/3 14:26
 */
@Data
@ApiModel("用户电站信息-APP")
public class StationInfoByUser implements Serializable {
    @ApiModelProperty(value = "电站Id")
    private String plantId;
//    @ApiModelProperty(value = "电站UID")
//    private String plantUID;
    @ApiModelProperty(value = "电站名称")
    private String plantName;
    @ApiModelProperty(value = "建站时间")
    private String createTime;
    @ApiModelProperty(value = "装机容量")
    private String peakPower;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "IMEI码")
    private String imei;
    @ApiModelProperty(value = "当前功率")
    private BigDecimal currentPower;
    @ApiModelProperty(value = "日发电量")
    private BigDecimal todayEnergy;
    @ApiModelProperty(value = "月发电量")
    private BigDecimal monthEnergy;
    @ApiModelProperty(value = "年发电量")
    private BigDecimal yearEnergy;
    @ApiModelProperty(value = "总发电量")
    private BigDecimal totalEnergy;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;
    @ApiModelProperty(value = "数据时间")
    private String dateTime;

    @ApiModelProperty(value = "用户电话")
    private String phone;
    @ApiModelProperty(value = "电站地址")
    private String address;
    @ApiModelProperty(value = "项目专项")
    private String special;

}
