package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.AlarmAnalysis;
import entityDTO.AlarmAnalysisengnier;
import entityDTO.BTOstationListDTO;
import entityDTO.BtoStationEnginerDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 工程师注册电站
 * <AUTHOR>
 */
@Mapper
public interface BtoStationEnginerMapper extends BaseMapper<BtoStationEnginerDTO> {
    /**
     *
     * @param name
     * @return
     */
    String selectPlantUidByName(String name);

    List<BtoStationEnginerDTO> SelStationAll();

    List<BtoStationEnginerDTO> CheckStationName(@Param("stationName") String stationName);

    String selectPlantIdBySpecial(String plantId);

    Integer insertBase(BtoStationEnginerDTO btoStationEnginerDTO);

    void ModifyAlreadyregistered(@Param("hyUserUid") String hyUserUid);

    List<AlarmAnalysisengnier> RequirementsPowerStationAlarms(@Param("time") String time);
}
