package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.VDeviceListMapper;
import com.botong.services.service.VDeviceListService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.VDeviceList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VDeviceListServiceImpl extends  ServiceImpl<VDeviceListMapper, VDeviceList> implements VDeviceListService {

    @Autowired
    private VDeviceListMapper vDeviceListMapper;

    @Override
    public PageInfo<VDeviceList> selectAll(Integer page,Integer pageSize){
        PageHelper.startPage(page,pageSize);
        List<VDeviceList> vDeviceLists = vDeviceListMapper.selectList(null);
        PageInfo<VDeviceList> info = new PageInfo<>(vDeviceLists);
        return info;
    }

    @Override
    public PageInfo<VDeviceList> likeSelect(Integer page, Integer pageSize, String b) {
        PageHelper.startPage(page,pageSize);
        String a = "%"+ b + "%";

        List<VDeviceList> lists = vDeviceListMapper.Liskselect(a);
        PageInfo<VDeviceList> info = new PageInfo<>(lists);
        return info;
    }

    @Override
    public int insert(VDeviceList vDeviceList) {
        QueryWrapper<VDeviceList> wrapper = new QueryWrapper<>();
        wrapper.eq("device_sn",vDeviceList.getDeviceSn());
        if ( vDeviceListMapper.selectOne(wrapper) == null) {
            vDeviceListMapper.insert(vDeviceList);
            return 1;
        } else {
            return -1;
        }
    }
}
