package entityDTO;

import lombok.Data;

@Data
public class BtoDeviceRealTimeDTO {
    public String sn;
    public String time;
    private String ppv1;
    private String ppv2;
    private String ppv3;
    private String ppv4;
    private String ppv5;
    private String ppv6;
    private String ppv7;
    private String ppv8;
    private String ppv9;

    public float ipv1;
    public float ipv2;
    public float ipv3;
    public float ipv4;
    public float ipv5;
    public float ipv6;
    public float ipv7;
    public float ipv8;
    public float ipv9;

    public float vpv1;
    public float vpv2;
    public float vpv3;
    public float vpv4;
    public float vpv5;
    public float vpv6;
    public float vpv7;
    public float vpv8;
    public float vpv9;

    public float iac1;
    public float iac2;
    public float iac3;
    public float iac4;
    public float iac5;
    public float iac6;
    public float iac7;
    public float iac8;
    public float iac9;

    public float vac1;
    public float vac2;
    public float vac3;
    public float vac4;
    public float vac5;
    public float vac6;
    public float vac7;
    public float vac8;
    public float vac9;

    public float facc1;
    public float facc2;
    public float facc3;
    public float facc4;
    public float facc5;
    public float facc6;
    public float facc7;
    public float facc8;
    public float facc9;

    public float power;

    public float todayEnergy;

    public float totalEnergy;

    public String updateTime;


}
