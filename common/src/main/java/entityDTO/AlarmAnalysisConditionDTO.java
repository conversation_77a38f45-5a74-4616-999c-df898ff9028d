package entityDTO;

import lombok.Data;

@Data
public class AlarmAnalysisConditionDTO {
    /**
     * 需要查询的电站UID数组
     */
    private String[] plantUid;
    /**
     * 查询条件：开始时间
     */
    private String startTime;
    /**
     * 查询条件：结束时间
     */
    private String endTime;
    /**
     * 查询类型：0:列表  1:聚合
     */
    private Integer type;
    /**
     * 页码
     */
    private String page;
    /**
     * 页面大小
     */
    private String pageSize;
    /**
     * 筛选条件1：告警信息
     */
    private String alarmMessage;
    /**
     * 筛选条件1：事件描述
     */
    private String mean;
    /**
     * 筛选条件1：级别
     */
    private String grade;
    /**
     * 筛选条件1：状态
     */
    private String status;

    /**
     *
     * 筛选条件的字符串数组
     */
    private String[] conditions;
    /**
     *
     * 筛选条件的乡镇
     */
    private String address;

}
