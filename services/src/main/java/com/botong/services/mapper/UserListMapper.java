package com.botong.services.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoUserList;
import entityDTO.Resource;
import entityDTO.Role;
import entityDTO.returnPrimaryId;
import entityDTO.roleresoure;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserListMapper extends BaseMapper<BtoUserList> {

    List<BtoUserList> selectListt(@Param("page") Integer page,@Param("pagesize") Integer pagesize);

    Object UserRoleQuery(@Param("userUid") String userUid);

    List<roleresoure> getPermissionId(@Param("userUid") String userUid);

    List<roleresoure> getroleResourceId(@Param("roleId") int roleId);

    List<Resource> listResource();

    List<Role> listRole();

    List<returnPrimaryId> returnPrimaryId(@Param("plantId") String plantId,@Param("plantUid") String plantUid);

    int updatePassword(@Param("userUid") String userUid,@Param("password") String password);

    int registeredUser(@Param("uuid") String uuid, @Param("cUserName") String cUserName,@Param("cUsertel")  String cUsertel, @Param("cUserEmail") String cUserEmail,@Param("regtiTime")  String regtiTime,@Param("engnierId") String engnierId,@Param("special")Integer special,@Param("")String password);
}