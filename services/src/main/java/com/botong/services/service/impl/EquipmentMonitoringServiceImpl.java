package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.EquipmentMonitoringMapper;
import com.botong.services.service.EquipmentMonitoringService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entityDTO.sheBeiJianCe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class EquipmentMonitoringServiceImpl extends ServiceImpl<EquipmentMonitoringMapper, BtoDeviceList> implements EquipmentMonitoringService {

    @Autowired
    private EquipmentMonitoringMapper equipmentMonitoringMapper;
    @Override
    public Object EquipmentMonitoringplantUid(String page, String pageSize, String plantUid) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableNme = simpleDateFormat.format(date);
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat dateFormattime10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormattime20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //截取当前时间前10分钟
        String Nowtime10 = (dateFormattime10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (dateFormattime20.format(now20)).substring(0, 15) + "%";
        List<sheBeiJianCe> stationLunBos = equipmentMonitoringMapper.EquipmentMonitoringplantUid(page, pageSize, tableNme, Nowtime10, Nowtime20, plantUid);
        PageInfo<sheBeiJianCe> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }
}
