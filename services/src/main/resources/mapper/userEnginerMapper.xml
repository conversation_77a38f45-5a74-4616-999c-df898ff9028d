<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.userEnginerMapper">

    <resultMap type="entityDTO.userEnginerDTO" id="userEnginerMap">
        <result property="delFlag" column="delFlag"/>
        <result property="updateDate" column="updateDate"/>
        <result property="updateTime" column="updateTime"/>
        <result property="createTime" column="createTime"/>
        <result property="cUserId" column="cUserId"/>
        <result property="cUserUid" column="cUserUid"/>
        <result property="cUserName" column="cUserName"/>
        <result property="cUserTel" column="cUserTel"/>
        <result property="delFlag" column="delFlag"/>
        <result property="cUserRegtime" column="cUserRegtime"/>
        <result property="updateDate" column="updateDate"/>
        <result property="cUserEmail" column="cUserEmail"/>
        <result property="password" column="password"/>
        <result property="special" column="special"/>
    </resultMap>

    <resultMap type="entityDTO.btoProjectSign" id="btoProjectSignMap">
        <result property="project" column="project"/>
        <result property="sign" column="sign"/>
        <result property="no" column="no"/>
        <result property="userUid" column="userUid"/>
        <result property="userName" column="userName"/>
    </resultMap>


    <resultMap type="entityDTO.BtoDeviceTypeDTO" id="BtoDeviceTypeMap">
        <result property="model" column="model"/>
        <result property="Manufacturer" column="Manufacturer"/>
        <result property="type" column="type"/>
        <result property="No" column="No"/>
        <result property="user_uid" column="userUid"/>
        <result property="user_name" column="userName"/>
        <result property="user_tel" column="userTel"/>
        <result property="special" column="special"/>
        <result property="peak_power" column="peakPower"/>
        <result property="plant_name" column="plantName"/>
        <result property="address" column="address"/>
        <result property="update_time" column="updateTime"/>
        <result property="enrol" column="enrol"/>
    </resultMap>
    <insert id="registeredUser">
        INSERT INTO bto_user_list (c_user_id, c_user_uid, c_user_name, c_user_tel, del_flag, c_user_regtime, update_date, c_user_email, update_time, create_time, special,enginer_id)
        VALUES (1, #{uuid}, #{cUserName}, #{cUsertel}, 0, #{regtiTime}, #{regtiTime}, #{cUserEmail}, #{regtiTime}, #{regtiTime},#{special},#{engnierId})
    </insert>
    <update id="updatePassword">
        UPDATE bto_user_list
        SET password=#{password}
        WHERE c_user_uid=#{userUid};
    </update>
    <select id="selectUid" resultType="entityDTO.userEnginerDTO">
        select c_user_id, c_user_uid, c_user_name, c_user_tel, del_flag, c_user_regtime, update_date, c_user_email, update_time, create_time, password, special,enginer_id from bto_user_list where c_user_name= #{cUserName}
    </select>
    <select id="QueryAreaUsers" resultType="entityDTO.btoProjectSign">
        SELECT
            project,
            sign
        FROM
            bto_project_sign
    </select>
    <select id="SelectcompanyId" resultType="entityDTO.btoProjectSign">
        SELECT
            c_user_uid userUid,
            c_user_name userName
        FROM
            v_company
    </select>
    <select id="SelUserAll" resultType="entityDTO.userEnginerDTO">
        select c_user_uid,c_user_name from bto_user_list
    </select>
    <select id="CheckUsername" resultType="entityDTO.userEnginerDTO">
        select c_user_name from bto_user_list where c_user_name=#{userName}
    </select>
    <select id="InverterModel" resultType="entityDTO.BtoDeviceTypeDTO">
        SELECT
            Manufacturer,
            type,
            model
        FROM
            bto_device_type
    </select>
    <select id="QueryIMEIcode" resultType="java.lang.Integer">
        SELECT count(wisdom_device_sn) from bto_station_list where wisdom_device_sn='${imei}'
    </select>
    <select id="getUseroPhone" resultType="entityDTO.BtoDeviceTypeDTO">
        select user_uid,user_name,user_tel,special,peak_power,plant_name,address,update_time,enrol from bto_user_hy where user_tel=#{cUserPhone}
    </select>


    <update id="bindSmartOperator">
        update bto_station_list set  wisdom_device_sn=#{smartOperatorName},wisdom_sn_type=${num} where plant_uid=#{test}
    </update>

</mapper>