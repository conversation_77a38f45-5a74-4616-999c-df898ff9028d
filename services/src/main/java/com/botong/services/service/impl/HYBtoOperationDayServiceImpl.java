package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoOperationDayMapper;
import com.botong.services.service.BtoOperationDayService;
import com.botong.services.service.HYBtoOperationDayService;
import entity.BtoOperation;
import entity.BtoOperationDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;


/**
 * 运维器数据 impl
 *
 * <AUTHOR> @date 2022-08-19 14:24:48
 */
@Service
public class HYBtoOperationDayServiceImpl extends ServiceImpl<BtoOperationDayMapper, BtoOperationDay> implements HYBtoOperationDayService {

    @Autowired
    private BtoOperationDayMapper btoOperationDayMapper;
    @Override
    public List<BtoOperationDay> getUserBuySellInfoByMonth(String plantUid, String month) {
//        QueryWrapper<BtoOperationDay> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("plant_uid", plantUid);
        //暂时先只显示七月的，八月份数据不全
//        queryWrapper.like("time", month);
        List<BtoOperationDay> userBuySellInfoByMonth = btoOperationDayMapper.getUserBuySellInfoByMonth(plantUid, month);
        return userBuySellInfoByMonth;
    }

    @Override
    public List<BtoOperationDay> getUserBuySellInfoByYear(String plantUid, String year) {
        List<BtoOperationDay> getUserBuySellInfoByYear = btoOperationDayMapper.getUserBuySellInfoByYear(plantUid, year);
        return getUserBuySellInfoByYear;
    }
    @Override
    public List<BtoOperationDay> getUserBuySellALLInfo(String plantUid) {
        List<BtoOperationDay> getUserBuySellALLInfo = btoOperationDayMapper.getUserBuySellALLInfo(plantUid);
        return getUserBuySellALLInfo;
    }
    @Override
    public HashMap<String, String> WisdomDeviceInformation(String plantId) {
        HashMap<String, String> WisdomDeviceInformation = btoOperationDayMapper.WisdomDeviceInformation(plantId);
        return WisdomDeviceInformation;
    }

}
