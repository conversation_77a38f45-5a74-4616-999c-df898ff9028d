package entity;


import lombok.Data;

import java.io.Serializable;

/**
 * 运维器数据
 * 
 * <AUTHOR> @date 2022-08-19 14:24:48
 */
@Data
public class BtoOperationDay implements Serializable {
	/**
	 * 电站UID
	 */
	private String plantUid;

	/**
	 * 运维器SN
	 */
	private String wisdomDeviceSn;

	/**
	 * PV发电
	 */
	private String pvTel;

	/**
	 * 负载用电
	 */
	private String loadTel;

	/**
	 * 自发自用
	 */
	private String autoTel;

	/**
	 * 卖电
	 */
	private String sellTel;

	/**
	 * 买电
	 */
	private String buyTel;

	/**
	 * 时间
	 */
//	private String day;
		private String time;

	/**
	 * 图表时间类型（2：月，3：年，4：总）
	 */
	private String chartDateType;

	/**
	 * 月类型查询年月（格式：yyyy-MM）
	 */
//	private String chartMonth;


}
