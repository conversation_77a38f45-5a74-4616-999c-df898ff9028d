package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.VStationBaseDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface VStationBaseDTOMapper extends BaseMapper<VStationBaseDTO> {
    List<VStationBaseDTO> selectStationBase(@Param("time") String time , @Param("table") String table );

    List<VStationBaseDTO> selectStationBaseSign(@Param("time") String time , @Param("table") String table );

    VStationBaseDTO selectStationBasePlantId(@Param("time") String time , @Param("table") String table, @Param("plantId") String plantId);

    List<VStationBaseDTO> selectStationBasePx(@Param("time") String time , @Param("table") String table , @Param("px") String px , @Param("sort") String sort);

    int updateStation0();

    int updateStation1(@Param("plantId") String PlantId);

    List<VStationBaseDTO> VStationBaseDTOPx2(@Param("page") Integer page,@Param("pageSize") Integer pageSize,@Param("time") String time,@Param("param") String param,@Param("sort") String sort);

    Object selectVStationBaseDTOPx2XcCount();
}
