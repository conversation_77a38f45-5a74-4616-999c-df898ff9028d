<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.DeviceOperationMapper">

    <resultMap id="deviceInformation" type="entityDTO.deviceInformationDTO">
        <result property="name" column="name"/>
        <result property="address" column="address1"/>
        <result property="city" column="city"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="state" column="state"/>
        <result property="ctrl" column="ctrl"/>
        <result property="electrovalency" column="electrovalency"/>
        <result property="peakPower" column="peak_power"/>
        <result property="planType" column="plan_type"/>
        <result property="plantId" column="plant_id"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <!--        <result property="totalEnergy" column="total_energy"/>-->
        <!--        <result property="todayEnergy" column="today_energy"/>-->
        <result property="wisdomSn" column="wisdomSn"/>
        <result property="createDate" column="createDate"/>
        <result property="currentPower" column="currentPower"/>
        <result property="password" column="password"/>
        <result property="userUid" column="c_user_uid"/>
        <result property="plantId" column="plantId"/>
        <result property="plantUID" column="plantUID"/>
        <result property="address" column="address"/>
        <result property="ctrl" column="ctrl"/>
        <result property="time" column="time"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="totalEnergy" column="totalEnergy"/>
        <result property="meterId" column="meterId"/>
    </resultMap>
    <update id="deleteDeviceOperation">
        UPDATE bto_station_list
        SET deleted = 1,
            ctrl=0
        WHERE plant_id = '${plantId}'
          and (
            SELECT `password` = '${password}'
            FROM bto_user_list
            WHERE c_user_uid = '${userUid}')
    </update>
    <select id="selectDeviceByCtrl" resultMap="deviceInformation">
        SELECT a.plant_id                              plantId,
               a.`name1`                               `name`,
               a.create_date                           createDate,
               a.ctrl                                  ctrl,
               a.address1                              address,
               a.wisdom_device_sn                      wisdomSn,
               a.plant_uid plantUID,
               CASE
                   WHEN b.update_time > DATE_SUB(now(), INTERVAL 30 MINUTE) THEN
                       SUM(DISTINCT (b.power))
                   ELSE 0 END                          currentPower,
               CASE
                   WHEN LEFT(b.update_time, 10) = CURDATE() THEN
                       SUM(DISTINCT (b.today_energy))
                   ELSE 0 END                          todayEnergy,
               SUM(DISTINCT (b.total_energy))          totalEnergy,
               CONCAT(LEFT(b.update_time, 15), '0:00') time
        FROM v1_station_device a
                 LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
          AND a.deleted = '0'
          AND ctrl = '1'
        GROUP BY a.plant_id
    </select>
    <update id="collectDeviceOperation">
        UPDATE bto_station_list
        SET ctrl = 1
        WHERE plant_id = '${plantId}'
    </update>
    <update id="cancelCollectDeviceOperation">
        UPDATE bto_station_list
        SET ctrl = 0
        WHERE plant_id = '${plantId}'
    </update>
    <select id="selectDeviceInformation" resultMap="deviceInformation">
        SELECT name,
               country,
               state,
               city,
               town,
               address1,
               longitude,
               latitude,
               peak_power,
               plan_type,
               electrovalency,
               update_time,
               meter_id meterId
        FROM bto_station_list
        WHERE plant_id = '${plantId}'
    </select>
    <update id="modifyDeviceOperation" parameterType="entityDTO.ModifyStationForm">
        UPDATE bto_station_list,bto_station_base SET
                     bto_station_base.NAME = #{modifyStationForm.stationName},
                     bto_station_base.country = #{modifyStationForm.country},
                     bto_station_base.state = #{modifyStationForm.state},
                     bto_station_base.city = #{modifyStationForm.city},
                     bto_station_base.town = #{modifyStationForm.town},
                     bto_station_base.address1 = #{modifyStationForm.address},
                     bto_station_base.peak_power = #{modifyStationForm.peakPower},
                     bto_station_base.latitude = #{modifyStationForm.latitude},
                     bto_station_base.longitude =#{modifyStationForm.longitude},
                     bto_station_list.electrovalency = #{modifyStationForm.electrovalency},
                     bto_station_list.NAME = #{modifyStationForm.stationName},
                     bto_station_list.country = #{modifyStationForm.country},
                     bto_station_list.state = #{modifyStationForm.state},
                     bto_station_list.city =#{modifyStationForm.city},
                     bto_station_list.town =#{modifyStationForm.town},
                     bto_station_list.address1 =#{modifyStationForm.address},
                     bto_station_list.latitude=#{modifyStationForm.latitude},
                     bto_station_list.longitude=#{modifyStationForm.longitude},
                     bto_station_list.peak_power= #{modifyStationForm.peakPower},
                     bto_station_base.update_time =#{modifyStationForm.updateTime},
                     bto_station_list.update_time =#{modifyStationForm.updateTime},
                     bto_station_list.meter_id =#{modifyStationForm.meterId}
        WHERE bto_station_list.plant_id = #{modifyStationForm.plantId}
          AND bto_station_base.plant_id = #{modifyStationForm.plantId}
    </update>
</mapper>