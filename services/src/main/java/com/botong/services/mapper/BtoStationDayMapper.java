package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoStationDay;
import entityDTO.BtoStationDayDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 电站日发电量
 * 
 * <AUTHOR> @date 2022-08-15 09:45:17
 */
@Mapper
public interface BtoStationDayMapper extends BaseMapper<BtoStationDay> {

    List<BtoStationDayDTO> StationMothEnergy(@Param("jie") String jie);

    List<BtoStationDayDTO> powerStationDay(@Param("time") String time);
}
