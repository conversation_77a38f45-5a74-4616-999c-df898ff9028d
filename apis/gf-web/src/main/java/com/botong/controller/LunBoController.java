package com.botong.controller;

import entity.Carousel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

import java.util.HashMap;

@CrossOrigin
@RestController
@Api(tags = "光伏监控中心")
@RequestMapping("LunBoController")
public class LunBoController {

    @Autowired
   private com.botong.services.service.lunboService lunboService;

    @ApiOperation(value = "站点轮播--列表",response = Carousel.class)
    @GetMapping("stationCarousel")
    public AjaxResult stationCarousel(Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",lunboService.stationCarousel(page,pageSize));
    }
    @ApiOperation(value = "近6月&近7日发电量统计",response = HashMap.class)
    @GetMapping("sevenAndSixEnergy")
    public AjaxResult sevenAndSixEnergy(){
        return AjaxResult.success("操作成功",lunboService.sevenAndSixEnergy());
    }
}
