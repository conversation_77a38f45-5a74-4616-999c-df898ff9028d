<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.lunboMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.StationLunBo" id="btoDeviceListMap">
        <result property="name" column="name"/>
        <result property="createTime" column="createTime"/>
        <result property="DangQian" column="DangQian"/>
        <result property="DianZhanRongLiang" column="DianZhanRongLiang"/>
        <result property="DateEnergy" column="DateEnergy"/>
        <result property="AllEnergy" column="AllEnergy"/>
        <result property="status" column="status"/>
        <result property="address1" column="address1"/>
        <result property="OutputPower" column="OutputPower"/>
        <result property="CO2" column="CO2"/>
        <result property="sumEnergy" column="sumEnergy"/>
        <result property="time" column="time"/>
    </resultMap>

    <select id="stationCarousel" resultType="entity.Carousel">
        SELECT (@i := @i + 1)  stationNo,
               q.`status` status,
               q.stationName,
               q.createTime,
               ((q.totalEnergy*0.997)/1000) co2,
               q.realTimePower,
               q.todayEnergy,
               q.totalEnergy,
               q.updateTime,
               q.peakPower,
               q.city
        FROM (SELECT a.status1 `status`,
                                a.`name1` stationName,
                                    SUM(DISTINCT (a.peak_power)) peakPower,
                                LEFT(a.create_date, 10) createTime,
                     CASE WHEN a.status1!=0 AND b.date_time > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                         THEN SUM(DISTINCT (b.power)) ELSE 0 END realTimePower,
                     CASE WHEN LEFT(b.date_time, 10) = CURDATE()
                         THEN SUM(DISTINCT (b.today_energy)) ELSE 0 END todayEnergy,
                                  SUM(DISTINCT (b.total_energy)) totalEnergy,
                                  b.date_time updateTime,
                                  a.city city
              FROM v1_station_device a
                       LEFT JOIN bto_device_energy b ON a.sn = b.sn
              GROUP BY a.plant_id
              ORDER BY a.create_date DESC,a.name1) q,(SELECT @i := 0) w
    </select>
    <select id="powerSevenEnergy" resultType="java.util.HashMap">
        SELECT date,
               sum(energy) sumEnergy
        FROM v_day
        WHERE left(date, 7) = '${time}'
        GROUP BY date
        ORDER BY date
        LIMIT 7;
    </select>
    <select id="powerSIXEnergy" resultType="java.util.HashMap">
        SELECT substr(date, 1, 7) time,
               sum(energy)        sumEnergy
        FROM v_day
        GROUP BY time
        ORDER BY time
        LIMIT 6
    </select>


</mapper>