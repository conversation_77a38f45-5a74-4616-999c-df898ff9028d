package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoStationListMapper;
import com.botong.services.service.BtoStationListService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoStationList;
import entityDTO.AllStationList;
import entityDTO.BTOstationListDTO;
import entityDTO.StationLunBo;
import entityDTO.stationNum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import vo.StationListVo;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 电站列表 impl
 *
 * <AUTHOR> @date 2022-08-11 15:36:33
 */
@Service
public class BtoStationListServiceImpl extends ServiceImpl<BtoStationListMapper, BtoStationList> implements BtoStationListService {

    @Autowired
    BtoStationListMapper btoStationListMapper;

    @Override
    public Long StaionAllNum() {
        return baseMapper.selectCount(null);
    }

    @Override
    public Long StationStatus0() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "0");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Long StationStatus1() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "1");
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'StationAllpeakPower' ", cacheManager = "GF_Web_Cache")
    public BtoStationList StationAllpeakPower() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("SUM(peak_power) as peakPower ");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'StationAllenergy' ", cacheManager = "GF_Web_Cache")
    public List<BTOstationListDTO> StationAllenergy(String tableNme, String nowtime10, String nowtime20) {
        return btoStationListMapper.StationAllenergy(tableNme, nowtime10, nowtime20);
    }


    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'GFmonitoringCenter' ", cacheManager = "GF_Web_Cache")
    public List<BtoStationList> GFmonitoringCenter() {
        return baseMapper.selectList(null);
    }


    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'getAllStationName' ", cacheManager = "GF_Web_Cache")
    public List<StationListVo> getAllStationName() {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("plant_id", "name");
        ArrayList<StationListVo> stationListVos = new ArrayList<>();
        List<BtoStationList> btoStationLists = baseMapper.selectList(queryWrapper);
        for (BtoStationList btoStationList : btoStationLists) {
            StationListVo stationListVo = new StationListVo();
            stationListVo.setName(btoStationList.getName());
            stationListVo.setPlantId(btoStationList.getPlantId());
            stationListVos.add(stationListVo);
        }
        return stationListVos;
    }

    @Override
    public String getInstalledCapacityById(String plantId) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("peak_power");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper).getPeakPower();
    }

    @Override
    @Cacheable(cacheNames = "Stationonline" ,key = " 'Stationonline' " )//或者这样写key = "#id"
    public Map<String,Integer> Stationonline() {
        HashMap<String,Integer> map = new HashMap<>();
        Date date = new Date();
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String tableName = time.format(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String tableName2 = simpleDateFormat.format(date).concat("%");
        Integer stationonline = btoStationListMapper.Stationonline(tableName, tableName2);
        map.put("stationonline",stationonline);
        return map;
    }

    @Override
    public String getIdByUId(String plantId) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("plant_uid");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper).getPlantUid();
    }

    @Override
    @Cacheable(cacheNames = "GY_web" ,key = " 'ListOfTotalPowerStations' ")
    public PageInfo<AllStationList> ListOfTotalPowerStations(String status, String stationName, String page, String pageSize) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String time = simpleDateFormat.format(date);
        String time2 = "%" + simpleDateFormat2.format(date) + "%";
        List<AllStationList> stationLunBos = btoStationListMapper.selectListt(time, time2);
//        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        if (status == null || "".equals(status)) {
            if (stationName != null && !"".equals(stationName)) {
                List<AllStationList> stationLunBosByName = btoStationListMapper.ListOfTotalPowerStationsstationName(time, time2, stationName);
                PageInfo<AllStationList> stationLunBoPageInfo = new PageInfo<>(stationLunBosByName);
                return stationLunBoPageInfo;
            }
            PageInfo<AllStationList> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
            return stationLunBoPageInfo;
        } else if ("在线".equals(status)) {
            List<AllStationList> onLineStations = stationLunBos.stream().filter(real -> real.getRealTimePower() != null
                            && !real.getRealTimePower().equals("0.0"))
                    .collect(Collectors.toList());
            if (stationName != null && !"".equals(stationName)) {
                List<AllStationList> onLineStationsByName = onLineStations.stream().filter(allStationList -> allStationList.getName().contains(stationName)).collect(Collectors.toList());
                PageInfo<AllStationList> onLineStationsPageInfo = new PageInfo<>(onLineStationsByName);
                return onLineStationsPageInfo;
            }
            PageInfo<AllStationList> onLineStationsPageInfo = new PageInfo<>(onLineStations);
            return onLineStationsPageInfo;
        } else {
            List<AllStationList> outLineStations = stationLunBos.stream().filter(real -> StringUtils.isEmpty(real.getRealTimePower()) || real.getRealTimePower().equals("0.0"))
                    .collect(Collectors.toList());
            if (stationName != null && !"".equals(stationName)) {
                List<AllStationList> outLineStationsByName = outLineStations.stream().filter(allStationList -> allStationList.getName().contains(stationName)).collect(Collectors.toList());
                PageInfo<AllStationList> outLineStationsPageInfo = new PageInfo<>(outLineStationsByName);
                return outLineStationsPageInfo;
            }
            PageInfo<AllStationList> outLineStationsPageInfo = new PageInfo<>(outLineStations);
            return outLineStationsPageInfo;
        }

    }

    @Override
    public PageInfo<AllStationList> ListOfTotalPowerStationsstationName(String page, String pageSize, String stationName) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pageSize));
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String time = simpleDateFormat.format(date);
        String time2 = simpleDateFormat2.format(date) + "%";
        List<AllStationList> stationLunBos = btoStationListMapper.ListOfTotalPowerStationsstationName(time, time2, stationName);
        PageInfo<AllStationList> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public Object ListOfTotalPowerStationsPhone(String plantUid) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String time = simpleDateFormat.format(date);
        return btoStationListMapper.ListOfTotalPowerStationsPhone(time,plantUid);
    }

    @Override
    public String getPhoneNumByUid(String userUid) {
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("owner_phone");
        queryWrapper.eq("plant_id", userUid);
        return baseMapper.selectOne(queryWrapper).getOwnerPhone();
    }

    @Override
    @Cacheable(cacheNames = "getStationEnergy" ,key = " 'getStationEnergy' ",cacheManager = "GF_Web_Cache")
    public Map<String,Double> getStationEnergy() {
        Date date = new Date();
        Map<String,Double> stationEnergyMap = new HashMap<>();
        //查询日发电量
        SimpleDateFormat sdf_Day= new SimpleDateFormat("yyyy-MM-dd");
        String day = sdf_Day.format(date).concat("%");
        HashMap<String,Double> stationEnergyByDay = btoStationListMapper.getStationEnergy(day);
        //查询月发电量
        SimpleDateFormat sdf_Month= new SimpleDateFormat("yyyy-MM");
        String month = sdf_Month.format(date).concat("%");
        HashMap<String,Double> stationEnergyByMonth = btoStationListMapper.getStationEnergy(month);
        //查询总发电量
        HashMap<String,Double> stationEnergyByAll= btoStationListMapper.getStationEnergy("");

        stationEnergyMap.put("dayElectricity",stationEnergyByDay.get("todayEnergy"));
        stationEnergyMap.put("monthElectricity",stationEnergyByMonth.get("monthEnergy"));
        stationEnergyMap.put("allElectricity",stationEnergyByAll.get("totalEnergy"));
        return stationEnergyMap;
    }

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'StationNum' ", cacheManager = "GF_Web_Cache")
    public HashMap<String, Integer> StationNum() {
        HashMap<String, Integer> stationNumMap = new HashMap<>();
        //查询总电站数量
        QueryWrapper<BtoStationList> queryWrapper = new QueryWrapper<>();
        //Long类型的数据需要转换为Integer
        Long staionAllNumL = baseMapper.selectCount(queryWrapper);
        Integer staionAllNum = staionAllNumL.intValue();
        //查询在线电站数量
        Date date = new Date();
        Date near_date = new Date(date.getTime() - 60 * 60 * 1000);
        SimpleDateFormat time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = time.format(near_date);
        String nearTime = str.substring(0, 13);
        String curTime = time.format(date).substring(0, 10).concat("%");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableName = simpleDateFormat.format(near_date);
        Integer onlineStationNum = btoStationListMapper.Stationonline(nearTime, tableName);
        //查询告警电站数量
        Integer alterStationNum = btoStationListMapper.StationStatus2(curTime);
        //正常电站
        Integer normalStation = onlineStationNum - alterStationNum;
        //离线电站
        Integer offlineStation = staionAllNum - onlineStationNum;
        stationNumMap.put("staionAllNum", staionAllNum);
        stationNumMap.put("onlineStationNum", onlineStationNum);
        stationNumMap.put("alterStationNum", alterStationNum);
        stationNumMap.put("normalStation", normalStation);
        stationNumMap.put("offlineStation", offlineStation);
        return stationNumMap;
    }

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'peakPowerAndstationEnergy' ", cacheManager = "GF_Web_Cache")
    public HashMap<String, Integer> peakPowerAndstationEnergy() {
        //总装机容量

        //日月总发电量

        //近六月发电量

        //近七日发电量



        return null;
    }
}
