package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.Carousel;
import entityDTO.StationLunBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface HYlunboMapper extends BaseMapper<StationLunBo> {

    List<Carousel> stationCarousel();

    List<HashMap<String,String>> powerSevenEnergy();

    List<HashMap<String,String>> powerSIXEnergy();
}
