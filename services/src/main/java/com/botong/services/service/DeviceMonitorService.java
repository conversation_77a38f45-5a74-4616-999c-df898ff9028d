package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entityDTO.sheBeiJianCe;

import java.util.List;

public interface DeviceMonitorService extends IService<sheBeiJianCe> {

    List<sheBeiJianCe> collectionMaxMin(String snName, String tableNme , String tabletime2);
    List<sheBeiJianCe> collectioAvg(String snName, String tableNme ,String tabletime2);
    List<sheBeiJianCe> Devicecurrent(String snName,String tableNme,String tableNme2);
}
