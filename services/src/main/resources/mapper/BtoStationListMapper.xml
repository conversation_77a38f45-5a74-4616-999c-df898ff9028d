<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.BtoStationListMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entity.BtoStationList" id="btoStationListMap">
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="userUid" column="user_uid"/>
        <result property="status" column="status"/>
        <result property="country" column="country"/>
        <result property="city" column="city"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="peakPower" column="peak_power"/>
        <result property="createDate" column="create_date"/>
        <result property="currentPower" column="current_power"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="planType" column="plan_type"/>
        <result property="installer" column="installer"/>
        <result property="operator" column="operator"/>
        <result property="owner" column="owner"/>
        <result property="ownerPhone" column="owner_phone"/>
        <result property="wisdomDeviceSn" column="wisdom_device_sn"/>
        <result property="imageUrl" column="image_url"/>
        <result property="locale" column="locale"/>
        <result property="plantNo" column="plant_no"/>
        <result property="ownerEmail" column="owner_email"/>
        <result property="ownerName" column="owner_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.BTOstationListDTO" id="btoStationListMapDTO">
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="userUid" column="user_uid"/>
        <result property="status" column="status"/>
        <result property="country" column="country"/>
        <result property="city" column="city"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="peakPower" column="peak_power"/>
        <result property="createDate" column="create_date"/>
        <result property="currentPower" column="current_power"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="planType" column="plan_type"/>
        <result property="installer" column="installer"/>
        <result property="operator" column="operator"/>
        <result property="owner" column="owner"/>
        <result property="ownerPhone" column="owner_phone"/>
        <result property="wisdomDeviceSn" column="wisdom_device_sn"/>
        <result property="imageUrl" column="image_url"/>
        <result property="locale" column="locale"/>
        <result property="plantNo" column="plant_no"/>
        <result property="ownerEmail" column="owner_email"/>
        <result property="ownerName" column="owner_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="monthEnergy" column="monthEnergy"/>
        <result property="totalEnergy" column="totalEnergy"/>
    </resultMap>

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.siteCarouselPage" id="siteCarouselPage">
        <result property="stationName" column="stationName"/>
        <result property="stationAdress" column="stationAdress"/>
        <result property="OutputPower" column="OutputPower"/>
        <result property="stationCapacity" column="stationCapacity"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="AllEnergy" column="AllEnergy"/>
        <result property="CO2" column="CO2"/>
        <result property="creatTime" column="creatTime"/>
        <result property="status" column="status"/>
        <result property="offlineTime" column="offlineTime"/>

    </resultMap>

    <resultMap type="entityDTO.stationNum" id="stationNumDTO">
        <result property="offline" column="offline"/>
        <result property="online" column="online"/>
        <result property="alert" column="alert"/>
        <result property="normal" column="normal"/>
    </resultMap>

    <resultMap type="entityDTO.AllStationList" id="AllStationList">
        <result property="plantId" column="plantId"/>
        <result property="status" column="status"/>
        <result property="name" column="name"/>
        <result property="peakPower" column="peakPower"/>
        <result property="deviceCount" column="deviceCount"/>
        <result property="plantType" column="plantType"/>
        <result property="time" column="time"/>
        <result property="createDate" column="createDate"/>
        <result property="realTimePower" column="realTimePower"/>
        <result property="workEfficiency" column="workEfficiency"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="monthEnergy" column="monthEnergy"/>
        <result property="yearEnergy" column="yearEnergy"/>
        <result property="hoursperday" column="hoursperday"/>
        <result property="totalEnergy" column="totalEnergy"/>
        <result property="city" column="city"/>
        <result property="sn" column="sn"/>
        <result property="plantUid" column="plantUid"/>
        <result property="ownerPhone" column="ownerPhone"/>
        <result property="owner" column="owner"/>
        <result property="address1" column="address1"/>
    </resultMap>

    <select id="sqlStaion" resultType="entity.BtoStationList"></select>

    <select id="StationAllenergy" resultType="entityDTO.BTOstationListDTO">
        SELECT sum(today_energy) todayEnergy,sum(month_energy) monthEnergy,sum(total_energy) totalEnergy,time
        <if test="null!=tableNme and ''!=tableNme">
            FROM
            bto_device_${tableNme}
        </if>
        WHERE
        time LIKE #{nowtime10}
        OR
        time LIKE #{nowtime20}
    </select>


    <select id="selectPages" resultType="entityDTO.siteCarouselPage">
    </select>
    <select id="StationStatus2" resultType="java.lang.Integer">
        SELECT count(
                       DISTINCT (b.plant_id))
        FROM v_event_mean a
                 LEFT JOIN v_device_list b ON a.sn = b.device_sn
        WHERE start_time LIKE '${curTime}'
          AND `status` = '0'
    </select>
    <select id="Stationonline" resultType="java.lang.Integer">
        SELECT count(DISTINCT (plant_id)) ONLINE
        FROM bto_device_${tableName} a
                 LEFT JOIN bto_device_list b ON a.sn = b.device_sn
        WHERE left(time, 13) = #{nearTime};
    </select>

    <select id="selectListt" resultMap="AllStationList">
        SELECT a.plant_id        plantId,
               a.plant_uid       plantuId,
               NAME              NAME,
               peak_power        peakPower,
               count(b.sn)       deviceCount,
               plant_type        plantType,
               time              time,
               create_date       createDate,
               sum(power)        realTimePower,
               sum(today_energy) todayEnergy,
               sum(total_energy) totalEnergy,
               city              city
        FROM `v_station_base` a
                 LEFT JOIN (
            SELECT plant_id,
                   sn,
                   power,
                   today_energy,
                   total_energy,
                   time
            FROM v_device_list aa
                     LEFT JOIN (
                SELECT sn,
                       power,
                       today_energy,
                       total_energy,
                       time
                FROM bto_device_${time}
                WHERE time LIKE '${time2}'
                GROUP BY SN,
                         time
                ORDER BY time DESC
            ) bb ON aa.device_sn = bb.sn
            WHERE sn is not null
            GROUP BY sn
        ) b ON a.plant_id = b.plant_id
        GROUP BY a.plant_id;
    </select>
    <select id="ListOfTotalPowerStationsstationName" resultMap="AllStationList">
        SELECT a.plant_id        plantId,
               a.plant_uid       plantuId,
               NAME              NAME,
               peak_power        peakPower,
               COUNT(b.sn)       deviceCount,
               plant_type        TYPE,
               TIME,
               create_date       createDate,
               SUM(POWER)        realTimePower,
               SUM(today_energy) todayEnergy,
               SUM(total_energy) totalEnergy,
               city              city
        FROM `v_station_base` a
                 LEFT JOIN (
            SELECT plant_id,
                   sn,
                   POWER,
                   today_energy,
                   total_energy,
                   TIME
            FROM v_device_list aa
                     LEFT JOIN (
                SELECT sn,
                       POWER,
                       today_energy,
                       total_energy,
                       TIME
                FROM bto_device_${time}
                WHERE TIME LIKE '${time2}'
                GROUP BY SN, TIME
                ORDER BY TIME DESC
            ) bb ON aa.device_sn = bb.sn
            GROUP BY sn
        ) b ON a.plant_id = b.plant_id
        WHERE NAME LIKE '%${stationName}%'
        GROUP BY a.plant_id
    </select>
    <select id="ListOfTotalPowerStationsPhone" resultMap="AllStationList">
        SELECT plant_id                         plantId,
               plant_uid                        plantUid,
               address1                         address1,
               peakPower,
               ownerPhone,
               OWNER,
               NAME,
               time,
               time1,
               sum(
                       DISTINCT (power))        power,
               sum(
                       DISTINCT (today_energy)) todayenergy,
               sum(
                       DISTINCT (total_energy)) totalEnergy
        FROM (
                 SELECT plant_id,
                        plant_uid,
                        address1,
                        peakPower,
                        ownerPhone,
                        OWNER,
                        NAME,
                        time,
                        time1,
                        power,
                        today_energy,
                        total_energy
                 FROM (
                          SELECT a.plant_id,
                                 b.time        time2,
                                 a.plant_uid,
                                 a.`name`      NAME,
                                 a.name1       OWNER,
                                 a.create_date time,
                                 a.tel         ownerPhone,
                                 a.peak_power  peakPower,
                                 c.address1    address1,
                                 a.sn,
                                 b.time        time1,
                                 b.power,
                                 b.today_energy,
                                 b.total_energy
                          FROM v1_station_device a
                                   LEFT JOIN bto_device_${time} b ON b.sn = a.sn
                                   LEFT JOIN v_station_base c ON a.plant_id = c.plant_id
                          WHERE a.plant_uid = #{plantUid}
                          GROUP BY b.sn,
                                   b.time
                          ORDER BY b.time DESC
                      ) q
                 GROUP BY sn
             ) w;
    </select>
    <select id="getAllStationNum" resultType="java.lang.Integer">
        SELECT count(plant_id)
        FROM v_station_list
        WHERE special = '1'
    </select>
    <select id="getWarnStationNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE special = '1'
          AND `status` = '2'
    </select>
    <select id="getWarnRecords" resultType="java.lang.Integer">
        SELECT count(*)
        FROM v_device_event a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE b.special = '1'
          AND a.STATUS = '0'
          AND LEFT(start_time, 10) = CURDATE()
          AND alarm_message not like 'PV%exception';
    </select>
    <select id="getEnergy" resultType="HashMap">
        SELECT sum(today_energy) dayElectricity,
               sum(month_energy) monthElectricity,
               sum(year_energy)  yearElectricity,
               sum(total_energy) allElectricity
        FROM (
                 SELECT today_energy,
                        month_energy,
                        year_energy,
                        total_energy
                 FROM v_device_list aa
                          LEFT JOIN (
                     SELECT sn,
                            today_energy,
                            month_energy,
                            year_energy,
                            total_energy
                     FROM bto_device_${tableName}
                     WHERE time like #{time}
                     GROUP BY SN,
                              time
                     ORDER BY time DESC
                 ) bb ON aa.device_sn = bb.sn
                 WHERE special = '1'
                   AND sn IS NOT NULL
                 GROUP BY sn
             ) q
    </select>
    <select id="getOnlineStationNum" resultType="java.lang.Integer">
        SELECT count(DISTINCT (plant_id))
        FROM bto_station_list
        WHERE special = '1'
          AND status in (1, 2, 3)
    </select>
    <select id="TotalInstalledCapacity" resultType="java.lang.Object">
        SELECT sum(peak_power)
        FROM v_station_list
        WHERE special = '1'
    </select>
    <select id="getStationEnergy" resultType="java.util.HashMap">
        select sum(today_energy) todayEnergy,sum(month_energy) monthEnergy,sum(total_energy) totalEnergy
        from bto_device_energy
        <if test="date!=null and date!='' ">
            where date_time like #{date}
        </if>
    </select>
    <select id="getDayElectricity" resultType="java.lang.Double">
        SELECT sum(today_energy)
        FROM bto_device_total
        WHERE special = '1'
          AND LEFT(update_time, 10) = curdate();#日
    </select>
    <select id="getMonthElectricity" resultType="java.lang.Double">
        SELECT sum(month_energy)
        FROM bto_device_total
        WHERE special = '1'
          AND LEFT(update_time, 7) = left(curdate(), 7);#月
    </select>
    <select id="getAllElectricity" resultType="java.lang.Double">
        SELECT sum(total_energy)
        FROM bto_device_total
        WHERE special = '1';#总
    </select>
    <select id="getWarnOperatorNum" resultType="java.lang.Integer">
        SELECT COUNT(a.alarm_name)
        FROM bto_operation_fault a
                 LEFT JOIN bto_station_list b ON a.plant_uid = b.plant_uid
        WHERE b.special = '1'
          AND LEFT(alarm_time, 10) = CURDATE()
          AND end_time > DATE_SUB(now(), INTERVAL 1 MINUTE)
    </select>
    <!--获取日/月/总 发电量-->
    <select id="getElectricity" resultType="java.util.HashMap">
        <!-- SELECT
            CASE
                WHEN LEFT (update_time, 10 ) = CURDATE() THEN sum(DISTINCT( today_energy ))
                ELSE 0 END dayElectricity,
            CASE
                WHEN MONTH ( update_time)= MONTH (now()) THEN sum(DISTINCT( month_energy ))
                ELSE 0 END monthElectricity,
            CASE
                WHEN YEAR ( update_time)= YEAR (now()) THEN sum(DISTINCT ( year_energy ))
                ELSE 0 END yearElectricity,
            SUM(DISTINCT ( total_energy )) allElectricity FROM bto_device_total WHERE special='1';
        -->
        SELECT
        today_energy dayElectricity,
        month_energy monthElectricity,
        year_energy yearElectricity,
        total_energy allElectricity,
        now() update_time
        FROM
        bto_sum_hy
        ORDER BY
        NO DESC
        LIMIT 1
    </select>
    <!--获取总装机容量-->
    <select id="getAllPeakPower" resultType="java.lang.Double">
        SELECT SUM(peak_power) peak_Power FROM bto_station_list WHERE special='1'
    </select>
    <!--    <select id="getStationEnergyByMonth" resultType="java.lang.Double">-->
    <!--        select sum(month_energy)-->
    <!--        from bto_device_energy-->
    <!--        where date_time like #{date}-->
    <!--    </select>-->
    <!--    <select id="getStationEnergyByAll" resultType="java.lang.Double">-->
    <!--        select sum(total_energy)-->
    <!--        from bto_device_energy-->
    <!--    </select>-->


</mapper>