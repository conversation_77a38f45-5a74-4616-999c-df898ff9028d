package com.botong.controller;

import com.botong.services.service.BtoIndexService;
import entity.BtoIndex;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

/*
*
*
* */
@RequestMapping("/btoIndex")
@RestController
@Api(tags = "采样指标")
@CrossOrigin
public class BtoIndexController {

    @Autowired
    private BtoIndexService btoIndexService;

    @ApiOperation("查询全部采样指标")
    @GetMapping("/SelectBtoIndex")
    public AjaxResult SelectBtoIndex(Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",btoIndexService.SelectBtoIndex(page, pageSize));
    }

    @ApiOperation("模糊查询采样指标")
    @GetMapping("/LikeSelectBtoIndex")
    public AjaxResult LikeSelectBtoIndex(String IndexName, Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",btoIndexService.LikeSelectBtoIndex(IndexName,page,pageSize));
    }


    @ApiOperation("插入采样指标")
    @PostMapping("/InsertBtoIndex")
    public AjaxResult InsertBtoIndex(@RequestBody BtoIndex btoIndex) {
        int i = btoIndexService.InsertBtoIndex(btoIndex);
        if (i < 0) {
            return AjaxResult.error("重复插入！");
        } else {
             return AjaxResult.success("操作成功");
        }
    }

    @ApiOperation("修改采样指标")
    @GetMapping("/updateBtoIndex")
    public AjaxResult updateBtoIndex(String indexId  ,String indexName) {
        int i = btoIndexService.UpdateBtoIndex(indexId,indexName);
        if (i < 0) {
            return AjaxResult.error("输入的id不存在！");
        } else {
            return AjaxResult.success("操作成功");
        }
    }

    @ApiOperation("删除采样指标")
    @DeleteMapping("/DeleteBtoIndex")
    public AjaxResult DeleteBtoIndex(String indexId) {
        int i = btoIndexService.DeleteBtoIndex(indexId);
        if (i <= 0) {
            return AjaxResult.error("删除失败！");
        }else {
            return AjaxResult.success("删除成功");
        }
    }
}
