package com.botong.services.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import entity.BtoDevice;
import org.apache.ibatis.annotations.Param;
import vo.DateElectricityVo;
import vo.EfficiencyVo;
import vo.ElectricityStatisticsVo;
import vo.InverterVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 逆变器数据
 *
 * <AUTHOR> @date 2022-08-16 10:10:10
 */
public interface BtoDeviceService extends IService<BtoDevice> {

    List<ElectricityStatisticsVo> inverterElectricityInfo();

    /**
     * 获取逆变器数据不止一个的用户
     */
    List<String> getUserByInverterMoreOne();

    /**
     * 获取逆变器数据只有一个的用户
     */
    List<String> getUserByInverterEqualOne();

    HashMap<String, Object> getUserComplexInfo(String plantId);

    float getCurrentPowerBySn(String sn);

    DateElectricityVo getDayMonthYearAllInfo(String dataloggerSn);

    HashMap<String, Object> getDayPowerBySn(String sn,String time,String tableName);

    HashMap<String, Object> getMonthElectricitySn(String plantId, String time);

    HashMap<String, Object> getYearElectricitySn(String plantId, String s);

    HashMap<String, Object> getAllElectricitySn(String deviceSns,String time);

    //根据sn获取日月年发电量
    List<BtoDevice> getElectricityBySn(String sn);

    BtoDevice getInverterDetails(String sn, String tableName, String time);

    int registeredUser(String cUserName, String cUsertel, String cUserEmail, String engnierId, Integer special,String password);


}

