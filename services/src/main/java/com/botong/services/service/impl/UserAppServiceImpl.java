package com.botong.services.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.botong.services.mapper.StationInfoMapper;
import com.botong.services.service.UserAppService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.AlarmInfoOnAppConditionVO;
import vo.ChartConditionVO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

/**
 * <AUTHOR>
 * @date 2023/4/6 14:27
 */
@Service
public class UserAppServiceImpl implements UserAppService {
    @Autowired
    private StationInfoMapper stationInfoMapper;

    @Override
    public IndexPageByUser selectIndexPage(String userUID) {
        List<StationInfoByUser> stationInfoList = stationInfoMapper.selectIndexPage(userUID);
        IndexPageByUser indexPageByUser = new IndexPageByUser();
        BigDecimal todayEnergy = BigDecimal.ZERO;
        BigDecimal monthEnergy = BigDecimal.ZERO;
        BigDecimal yearEnergy = BigDecimal.ZERO;
        BigDecimal totalEnergy = BigDecimal.ZERO;
        BigDecimal peakPower = BigDecimal.ZERO;
        List<String> plantIds = new ArrayList<>();

        Map<String, Long> statusMap = stationInfoList.stream().collect(Collectors.groupingBy(StationInfoByUser::getStatus, Collectors.counting()));
        int nomalStationCount = 0;
        int offlineStationCount = 0;
        int alarmStationCount = 0;
        int onlineStationCount = 0;
        for (String status : statusMap.keySet()) {
            switch (status) {
                case "0":
                    offlineStationCount = statusMap.get(status).intValue();
                    break;
                case "1":
                case "3":
                    nomalStationCount += statusMap.get(status).intValue();
                    onlineStationCount += statusMap.get(status).intValue();
                    break;
                case "2":
                    alarmStationCount = statusMap.get(status).intValue();
                    onlineStationCount += statusMap.get(status).intValue();
                    break;
                default:
            }
        }
        indexPageByUser.setOnlinestationCount(onlineStationCount);
        indexPageByUser.setOfflinestationCount(offlineStationCount);
        indexPageByUser.setAlarmStationCount(alarmStationCount);
        indexPageByUser.setNormalStationCount(nomalStationCount);

        for (StationInfoByUser stationInfo : stationInfoList) {
            todayEnergy = todayEnergy.add(stationInfo.getTodayEnergy());
            monthEnergy = monthEnergy.add(stationInfo.getMonthEnergy());
            yearEnergy = yearEnergy.add(stationInfo.getYearEnergy());
            totalEnergy = totalEnergy.add(stationInfo.getTotalEnergy());
            BigDecimal peakPowerL = new BigDecimal(stationInfo.getPeakPower());
            peakPower = peakPower.add(peakPowerL);
            plantIds.add(stationInfo.getPlantId());
        }
        stationInfoList.sort(comparing(StationInfoByUser::getDateTime));
        StationInfoByUser stationInfoByUser = stationInfoList.get(stationInfoList.size() - 1);

        indexPageByUser.setPlantIds(plantIds);
        indexPageByUser.setTodayEnergy(todayEnergy.toString());
        indexPageByUser.setMonthEnergy(monthEnergy.toString());
        indexPageByUser.setYearEnergy(yearEnergy.toString());
        indexPageByUser.setTotalEnergy(totalEnergy.toString());
        indexPageByUser.setPeakPower(peakPower.toString());
        indexPageByUser.setDateTime(stationInfoByUser.getDateTime());

        return indexPageByUser;
    }

    @Override
    public StationDetailsDTO stationDetails(String userUID) {
        List<StationInfoByUser> stationInfoList = stationInfoMapper.selectIndexPage(userUID);
        StationDetailsDTO stationDetails = new StationDetailsDTO();
        BigDecimal todayEnergy = BigDecimal.ZERO;
        BigDecimal monthEnergy = BigDecimal.ZERO;
        BigDecimal yearEnergy = BigDecimal.ZERO;
        BigDecimal totalEnergy = BigDecimal.ZERO;
        BigDecimal totalPower = BigDecimal.ZERO;
        BigDecimal peakPower = BigDecimal.ZERO;
        List<String> plantIds = new ArrayList<>();

        if (stationInfoList.size() > 0) {
            for (StationInfoByUser stationInfo : stationInfoList) {
                todayEnergy = todayEnergy.add(stationInfo.getTodayEnergy());
                monthEnergy = monthEnergy.add(stationInfo.getMonthEnergy());
                yearEnergy = yearEnergy.add(stationInfo.getYearEnergy());
                totalEnergy = totalEnergy.add(stationInfo.getTotalEnergy());
                totalPower = totalPower.add(stationInfo.getCurrentPower());
                BigDecimal peakPowerL = new BigDecimal(stationInfo.getPeakPower());
                peakPower = peakPower.add(peakPowerL);
                plantIds.add(stationInfo.getPlantId());
            }
        }
        stationInfoList.sort(comparing(StationInfoByUser::getDateTime));
        StationInfoByUser stationInfoByUser = stationInfoList.get(stationInfoList.size() - 1);

        stationDetails.setPlantIds(plantIds);
        stationDetails.setTodayEnergy(todayEnergy.toString());
        //当日收益=当日发电量*0.45
        stationDetails.setTodayProfit(todayEnergy.multiply(BigDecimal.valueOf(0.45)).toString());

        stationDetails.setMonthEnergy(monthEnergy.toString());
        stationDetails.setYearEnergy(yearEnergy.toString());
        stationDetails.setTotalEnergy(totalEnergy.toString());
        //总收益=总发电量*0.45
        stationDetails.setTotolProfit(totalEnergy.multiply(BigDecimal.valueOf(0.45)).toString());
        //累计植树=总发电量* 0.832 / 1800
        stationDetails.setTotolTree(totalEnergy.multiply(BigDecimal.valueOf(0.832)).divide(BigDecimal.valueOf(1800),2,BigDecimal.ROUND_HALF_UP).toString());
        //累计CO2减排=总发电量* 0.000997
        stationDetails.setTotolCo2(totalEnergy.multiply(BigDecimal.valueOf(0.000997)).toString());

        stationDetails.setCurrentPower(totalPower.toString());
        stationDetails.setPlantName(stationInfoByUser.getPlantName());
        stationDetails.setPhone(stationInfoByUser.getPhone());
        stationDetails.setAddress(stationInfoByUser.getAddress());
        stationDetails.setPeakPower(peakPower.toString());
        stationDetails.setCreateTime(stationInfoByUser.getCreateTime());
        stationDetails.setLongitude(stationInfoByUser.getLongitude());
        stationDetails.setLatitude(stationInfoByUser.getLatitude());

        stationDetails.setDateTime(stationInfoByUser.getDateTime());
        stationDetails.setSpecial(stationInfoByUser.getSpecial());
        return stationDetails;
    }

    @Override
    public List<InverterInfoDTO> inverterInfo(List<String> plantIds) {
        List<InverterInfoDTO> inverterInfos = stationInfoMapper.inverterInfo(plantIds);
        inverterInfos.forEach(inverterInfo -> {
            //质保日期在建站日期基础上加五百年
            DateTime dateTime = DateUtil.parse(inverterInfo.getWarrantyDate());
            DateTime warrantyDate = DateUtil.offset(dateTime, DateField.YEAR, 5);
            inverterInfo.setWarrantyDate(DateUtil.formatDate(warrantyDate));
        });
        return inverterInfos;
    }

    @Override
    public HashMap<String, Object> inverterDetails(String inverterSN) {
        HashMap<String, Object> inverterDetailsPage = new HashMap<String, Object>();
        //查询逆变器详细信息
        InverterDetailsDTO inverterDetails = stationInfoMapper.inverterDetails(inverterSN);
        String[] split = inverterDetails.getModel().split("-");
        Integer pvInputNumber = 3;
        switch (inverterDetails.getManufacturer()){
            case "SAJ":
                if(Integer.parseInt(split[2].substring(1)) >3){
                    int i = Integer.parseInt(split[2].substring(1));
                    pvInputNumber = Integer.parseInt(split[2].substring(1));
                }
                inverterDetails.setPvInputNumber(pvInputNumber);
                break;
            case "LANG":
                String inverterModel = inverterDetails.getModel();
                String substring = inverterModel.substring(4);
                pvInputNumber = stationInfoMapper.selectPvInputNumber(inverterModel.substring(4));
                inverterDetails.setPvInputNumber(pvInputNumber);
                break;
            default:
                inverterDetails.setPvInputNumber(0);
        }

        //查询光伏组件和电网信息
        //获取当前日期的字符串（yyyyMMdd）
        String today = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        PvMoudleDTO pvMoudle = stationInfoMapper.getPvMoudle(today, inverterSN);
        inverterDetailsPage.put("pvMoudleInfo", pvMoudle);
        inverterDetailsPage.put("inverterDetails", inverterDetails);
        return inverterDetailsPage;
    }

    @Override
    public <T> T stationChartInfo(ChartConditionVO chartConditionVO) {
        if ("D".equals(chartConditionVO.getConditionType())) {
            String tableSuffix = StrUtil.removeAll(chartConditionVO.getDateTime(), "-");
            List<DayChartDTO> dayChartList = stationInfoMapper.stationChartInfoByDay(chartConditionVO, tableSuffix);
            Map<String, List<DayChartDTO>> listMap = dayChartList.stream().collect(Collectors.groupingBy(DayChartDTO::getInverterSN));
            List<DayChartDTO> totalDayChartList = stationInfoMapper.totalStationChartInfoByDay(chartConditionVO, tableSuffix);
            //去除日图表数据中的并集时间轴
            List<String> dateTimeList = totalDayChartList.stream().map(dayChart -> {
                return dayChart.getDateTime();
            }).collect(Collectors.toList());
            //☠☠☠（高危操作）对残缺的数据进行数据填充☠☠☠
            //1.取出每一个逆变器的日数据列表
            Set<String> keySet = listMap.keySet();
            for (String key : keySet) {
                //如果逆变器日数据列表的长度和并集时间轴长度相等，则跳出本次循环
                if (listMap.get(key).size()< dateTimeList.size()){
                    List<DayChartDTO> dayChartsList = listMap.get(key);
                    for (int i = 0,j=0; i < dateTimeList.size(); i++) {
                            if (j>=dayChartsList.size() || !dateTimeList.get(i).equals(dayChartsList.get(j).getDateTime())){
                                dayChartsList.add(new DayChartDTO(key,"0.0","0.0",dateTimeList.get(i)));
                                continue;
                            }
                        j++;
                    }
                    dayChartsList.sort(Comparator.comparing(DayChartDTO::getDateTime));
                    listMap.replace(key,dayChartsList);
                }
            }
                listMap.put("totalDayChartList", totalDayChartList);
                return (T) listMap;
        } else {
            List<OtherChartDTO> otherChart = stationInfoMapper.stationChartInfo(chartConditionVO);
            return (T) otherChart;
        }
}

    @Override
    public List<OperatorInfoDTO> operatorInfo(String userUID) {
        List<OperatorInfoDTO> operatorInfoDTO = stationInfoMapper.operatorInfo(userUID);
        List<OperatorInfoDTO> operatorInfoList = operatorInfoDTO.stream().filter(operatorInfo -> {
            return operatorInfo.getImei() != null;
        }).collect(Collectors.toList());
            return  operatorInfoList;
    }

    @Override
    public   PageInfo<AppAlarmInfoDTO> inverterRealTimeAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO) {
        PageHelper.startPage(appConditionVO.getCurrentPage(), appConditionVO.getPageSize());
        List<AppAlarmInfoDTO> appAlarmInfoList = stationInfoMapper.inverterRealTimeAlarmInfo(appConditionVO.getPlantIds());
        PageInfo<AppAlarmInfoDTO> appAlarmInfoPage = new PageInfo<>(appAlarmInfoList);
        return appAlarmInfoPage;
    }

    @Override
    public  PageInfo<AppAlarmInfoDTO> inverterHistoryAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO) {
        PageHelper.startPage(appConditionVO.getCurrentPage(), appConditionVO.getPageSize());
        List<AppAlarmInfoDTO> appAlarmInfoList = stationInfoMapper.inverterHistoryAlarmInfo(appConditionVO.getPlantIds());
        PageInfo<AppAlarmInfoDTO> appAlarmInfoPage = new PageInfo<>(appAlarmInfoList);
        return appAlarmInfoPage ;
    }

    @Override
    public PageInfo<OperatorAlarmInfoDTO> operatorRealTimeAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO) {
        PageHelper.startPage(appConditionVO.getCurrentPage(), appConditionVO.getPageSize());
        List<OperatorAlarmInfoDTO> operatorAlarmInfoList = stationInfoMapper.operatorRealTimeAlarmInfo(appConditionVO.getUserUID());
        PageInfo<OperatorAlarmInfoDTO> appAlarmInfoPage = new PageInfo<>(operatorAlarmInfoList);
        return appAlarmInfoPage ;
    }

    @Override
    public PageInfo<OperatorAlarmInfoDTO> operatorHistoryAlarmInfo(AlarmInfoOnAppConditionVO appConditionVO) {
        PageHelper.startPage(appConditionVO.getCurrentPage(), appConditionVO.getPageSize());
        List<OperatorAlarmInfoDTO> operatorAlarmInfoList = stationInfoMapper.operatorHistoryAlarmInfo(appConditionVO.getUserUID());
        PageInfo<OperatorAlarmInfoDTO> operatorAlarmInfoPage = new PageInfo<>(operatorAlarmInfoList);
        return operatorAlarmInfoPage ;
    }

    @Override
    public HashMap<String,Object> operatorChartInfo(ChartConditionVO chartConditionVO) {
        List<OperatorChartInfoDTO> operatorChartInfoList = stationInfoMapper.operatorChartInfo(chartConditionVO);
        OperatorChartInfoDTO totalChartInfo = new OperatorChartInfoDTO();
       BigDecimal totalPvEnergy = BigDecimal.ZERO;
       BigDecimal totalUseEnergy = BigDecimal.ZERO;
       BigDecimal totalSelfEnergy = BigDecimal.ZERO;
       BigDecimal totalSelfUseEnergy = BigDecimal.ZERO;
       BigDecimal totalSellEnergy = BigDecimal.ZERO;
       BigDecimal totalBuyEnergy = BigDecimal.ZERO;
        for (OperatorChartInfoDTO operatorChartInfo : operatorChartInfoList) {
            totalPvEnergy =totalPvEnergy.add(new BigDecimal(operatorChartInfo.getPvEnergy()));
            totalUseEnergy= totalUseEnergy.add(new BigDecimal(operatorChartInfo.getUseEnergy()));
            totalSelfEnergy =totalSelfEnergy.add(new BigDecimal(operatorChartInfo.getSelfEnergy()));
            totalSelfUseEnergy =totalSelfUseEnergy.add(new BigDecimal(operatorChartInfo.getSelfUseEnergy()));
            totalSellEnergy =totalSellEnergy.add(new BigDecimal(operatorChartInfo.getSellEnergy()));
                totalBuyEnergy =totalBuyEnergy.add(new BigDecimal(operatorChartInfo.getBuyEnergy()));
        }
        totalChartInfo.setIMEI("total");
        totalChartInfo.setPlantUID("total");
        totalChartInfo.setPvEnergy(totalPvEnergy.toString());
        totalChartInfo.setUseEnergy(totalUseEnergy.toString());
        totalChartInfo.setSelfEnergy(totalSelfEnergy.toString());
        totalChartInfo.setSelfUseEnergy(totalSelfUseEnergy.toString());
        totalChartInfo.setSellEnergy(totalSellEnergy.toString());
        totalChartInfo.setBuyEnergy(totalBuyEnergy.toString());
        HashMap<String,Object> hashMap = new HashMap<String,Object>();
        hashMap.put("totalChartInfo",totalChartInfo);
        hashMap.put("operatorChartInfoList",operatorChartInfoList);
        return hashMap;
    }

    @Override
    public OperatorMonitorInfoDTO operatorMonitorInfo(String plantId, String plantUID) {
        //查询运维器实时监控信息
        OperatorMonitorInfoDTO operatorMonitorInfo = stationInfoMapper.operatorMonitorInfo(plantUID);
        if (operatorMonitorInfo==null){
            operatorMonitorInfo = new OperatorMonitorInfoDTO();
        }
        //查询运维器信号量强度
        String semaphore = stationInfoMapper.selectSemaphore(plantId);
        operatorMonitorInfo.setSemaphore(semaphore);
        //查询运维器(单个)实时故障信息
        OperatorAlarmInfoDTO operatorAlarmInfo = stationInfoMapper.selectOperatorAlarmInfo(plantId);
        operatorMonitorInfo.setOperatorAlarmInfo(operatorAlarmInfo);

        return operatorMonitorInfo;
    }
}
