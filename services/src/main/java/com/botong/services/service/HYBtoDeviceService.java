package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entity.BtoDevice;
import entity.BtoDeviceList;
import entityDTO.AlarmAnalysisConditionDTO;
import entityDTO.BtoStationDayDTO;
import entityDTO.xuDto;
import org.springframework.stereotype.Service;
import utils.AjaxResult;
import vo.DateElectricityVo;
import vo.ElectricityStatisticsVo;

import java.util.HashMap;
import java.util.List;


public interface HYBtoDeviceService extends IService<BtoDevice> {
    List<ElectricityStatisticsVo> inverterElectricityInfo();

    HashMap<String, Object> getUserComplexInfo(String plantId);

    float getCurrentPowerBySn(String sn);

    DateElectricityVo getDayMonthYearAllInfo(String dataloggerSn);

    xuDto powerStationDay();

    int updatePassword(String userUid, String password);

    Object returnPrimaryId(String plantId, String plantUid);

}
