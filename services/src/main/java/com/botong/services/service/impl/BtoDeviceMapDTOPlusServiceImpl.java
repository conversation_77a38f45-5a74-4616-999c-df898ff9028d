package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoDeviceMapDTOMapper;
import com.botong.services.service.BtoDeviceMapDTOPlusService;
import com.botong.services.utils.PageHelp1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.BtoDeviceMapDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class BtoDeviceMapDTOPlusServiceImpl extends ServiceImpl<BtoDeviceMapDTOMapper, BtoDeviceMapDTO> implements BtoDeviceMapDTOPlusService {

    @Autowired
    private BtoDeviceMapDTOMapper btoDeviceMapDTOMapper;

    @Override
    public PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlus( String sns, String day,Integer page,Integer pageSize){
        String[] split = day.split("-");
        PageHelp1 pageHelp1 = new PageHelp1();
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < split.length; i++) {
            buffer.append(split[i]);
        }
        String s = buffer.toString();
        String table =s;
        String time = day.concat("%");
        List<List<BtoDeviceMapDTO>> list = new ArrayList<>();
        String[] split1 = sns.split(",");
        for (int i = 0; i < split1.length; i++) {
            List<BtoDeviceMapDTO> dtoLists = btoDeviceMapDTOMapper.selectBtoDeviceMapPlus(table, time, split1[i]);
            list.add(dtoLists);
        }
        PageInfo info = pageHelp1.pageHelper(list, page, pageSize);
        return info;
    }



    @Override
    public PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusMonth(String plantId,String month, Integer page, Integer pageSize){
        String time= month+"%";
        PageHelp1 pageHelp1 = new PageHelp1();
        StringBuffer buffer = new StringBuffer();
        String[] split = plantId.split(",");
        ArrayList<List<BtoDeviceMapDTO>> list1 = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            List<BtoDeviceMapDTO> list = btoDeviceMapDTOMapper.selectBtoDeviceMapPlusMonth(split[i], time);
            list1.add(list);
        }
        PageInfo info = pageHelp1.pageHelper(list1, page, pageSize);
        return info;
    }

    @Override
    public PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusYear(String plantId,String year, Integer page, Integer pageSize){
        String time= year+"%";
        PageHelp1 pageHelp1 = new PageHelp1();
        String[] split = plantId.split(",");
        ArrayList<List<BtoDeviceMapDTO>> list1 = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            List<BtoDeviceMapDTO> list = btoDeviceMapDTOMapper.selectBtoDeviceMapPlusYear(split[i], time);
            list1.add(list);
        }
        PageInfo info = pageHelp1.pageHelper(list1, page, pageSize);
        return info;
    }
}
