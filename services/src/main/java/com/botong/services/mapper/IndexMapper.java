package com.botong.services.mapper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import entity.BtoStationList;
import entity.Carousel;
import entity.DayAndMonthEnergy;
import entityDTO.AllStationList;
import entityDTO.BTOstationListDTO;
import entityDTO.siteCarouselPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * 电站列表
 *
 * <AUTHOR>
 * @date 2022-12-17
 */
@Mapper
public interface IndexMapper{

    /**
     * 总装机容量
     * @return
     */
    String getAllPeakPower();

    /**
     * 日月年总发电量
     * @return
     */
    HashMap<String,Double> getEnergy();

    /**
     * 近七日发电量
     * @return
     */
    List<HashMap<String,Object>> getSevenDayEnergy();

    /**
     * 近六月发电量
     * @return
     */
    List<HashMap<String,Object>> getSixMonthEnergy();

    /**
     * 总电站数量
     * @return
     */
    Integer getAllStationNum();
    /**
     * 离线电站数量
     */
    Integer getOfflineStationNum();

    /**
     * 告警电站数量
     * @return
     */
    Integer getAlterStationNum();
    /**
     * 正常电站数量
     * @return
     */
    Integer getNormalStationNum();

    /**
     * 总逆变器数量
     * @return
     */
    Integer getAllDeviceNum();
    /**
     * 告警逆变器数量
     * @return
     */
    Integer getAlertDeviceNum();
    /**
     * 正常逆变器数量
     * @return
     */
    Integer getNormalDeviceNum();
    /**
     * 离线逆变器数量
     * @return
     */
    Integer getOfflineDeviceNum();
    /**
     * 告警记录数量
     * @return
     */
    Integer alertRecordNum();

    /**
     * --电站实时发电列表-轮播
     * --站点轮播
     */
    List<Carousel> realTimePowerCarousel();
}
