package com.botong.services.service;

import cn.hutool.core.lang.hash.Hash;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.DevicePvInfo;
import entityDTO.AddDeviceForm;
import entityDTO.HYAstationList;
import entityDTO.StationConditionDTO;
import entityDTO.userEnginerDTO;
import vo.StationInfoVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface HYAstationListService extends IService<HYAstationList> {
    /**
     *     河源APP电站列表
     */
    List<HYAstationList> HYadminStationList( );

    Map<String,Object> versionView() throws Exception;

    StationInfoVo selectStationInfo(String stationId);

    Object map(String stationId);

    Object getDeviceIdByStationId(String stationId);

    /**
     * 获取电站列表-多条件查询
     */
    List<HYAstationList> getStationsByCondition(StationConditionDTO stationCondition);

    Object versionViewipa();

    DevicePvInfo getDevicePvInfoBySN(String sn);

    String updateDevicePvInfo(DevicePvInfo devicePvInfo);

    String addDeviceInfo(AddDeviceForm addDeviceForm);

    HashMap<String,String> getDeviceInfoByPlantId(String plantId);

    HashMap<String,Object> selectCompanyIdAndDeviceMode();

    List<HashMap<String, String>> deviceList(String plantId);
}
