package com.botong.controller;

import com.botong.services.service.WisdomDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("WisdomDeviceController")
@Api(tags = "智慧运维器主页")
public class WisdomDeviceController {
    @Autowired
    private WisdomDeviceService wisdomDeviceService;

    @ApiOperation("智慧运维器信息")
    @GetMapping("WisdomDeviceInformation")
    public AjaxResult WisdomDeviceInformation(String plantId){
        return AjaxResult.success("操作成功",wisdomDeviceService.WisdomDeviceInformation(plantId));
    }

    @ApiOperation("智慧运维器报警信息")
    @GetMapping("WisdomDeviceAlarmInformation")
    public AjaxResult WisdomDeviceAlarmInformation(String imei){
        return AjaxResult.success("操作成功",wisdomDeviceService.wisdomDeviceAlarmInformation(imei));
    }


}
