<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYEfficiencyMapper">

    <resultMap type="entityDTO.EfficiencyDTO" id="EfficiencyMap">
        <result property="name" column="name"/>
        <result property="peakPower" column="peak_power"/>
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="date" column="date"/>
        <result property="date" column="date1"/>
        <result property="energy" column="sumEnergy"/>
        <result property="yoy" column="yoy"/>
        <result property="mom" column="mom"/>
        <result property="xl" column="xl"/>
    </resultMap>
    <select id="PowerGenerationMonth" resultMap="EfficiencyMap">
        SELECT b.name1                     name,
               peak_power,
               a.plant_id                  plant_id,
               b.plant_uid                 plant_uid,
               date,
               energy                      sumEnergy,
               energy / (3.5 * peak_power) xl
        FROM v_day a
                 LEFT JOIN v1_station_device b ON a.plant_id = b.plant_id
        WHERE a.plant_id = #{plantId}
          AND date LIKE #{month}
        GROUP BY date
        ORDER BY date
    </select>

    <select id="PowerGenerationYear" resultMap="EfficiencyMap">
        SELECT b.name1                     NAME,
               peak_power,
               a.plant_id                  plant_id,
               b.plant_uid,
               LEFT(date, 7)               time,
               date                        date1,
               SUM(energy)                 sumEnergy,
               energy / (3.5 * peak_power) xl
        FROM v_day a
                 LEFT JOIN v1_station_device b ON a.plant_id = b.plant_id
        WHERE a.plant_id = #{plantId}
          AND date LIKE #{year}
        GROUP BY time
        ORDER BY time
    </select>

    <select id="getStationInfoByCondition" parameterType="entityDTO.StationInfoConditionDTO"
            resultType="entity.HyStationInfo">
        SELECT
        a.plant_uid plantUid,
        a.name1 stationName,
        count(DISTINCT ( a.sn )) snCount,
        a.peak_power peakPower,
        sum(DISTINCT ( c.energy )) energy,
        count( c.alarm_message ) alarmCount,
        sum(DISTINCT ( b.total_energy )) totalEnergy,
        a.`name` userName,
        max(b.update_time) dateTime,
        a.address1 address,
        a.create_date createDate,
        a.meter_id meterId
        FROM
        v1_station_device a LEFT JOIN bto_device_total b ON a.plant_id = b.plant_id
        LEFT JOIN (
        SELECT
        q.plant_id,
        q.date,
        q.energy,
        w.sn,
        w.alarm_message,
        w.start_time
        FROM
        bto_station_day q
        LEFT JOIN v3_station_event w ON q.plant_id = w.plant_id AND q.date = LEFT ( w.start_time, 10 )
        WHERE
        <if test="timeFlag =='Y'.toString ">
            left(q.date,4) between #{startTime} and #{endTime}
        </if>
        <if test="timeFlag =='M'.toString ">
            left(q.date,7) between #{startTime} and #{endTime}
        </if>
        <if test="timeFlag =='D'.toString ">
        q.date BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY plant_id,DATE
        )c ON a.plant_id=c.plant_id
        WHERE
        a.special = '1'
        GROUP BY
        a.plant_id
        <trim prefix="having" suffixOverrides="and">
            <if test="stationName != null and stationName != ''">
                a.name1 LIKE CONCAT('%', #{stationName}, '%') AND
            </if>
            <if test="address != null and address != ''">
                a.address1 LIKE CONCAT('%', #{address}, '%') AND
            </if>
            <if test="createStartTime != null and createStartTime != '' and createEndTime != null and createEndTime != '' ">
                a.create_date between #{createStartTime}and #{createEndTime} AND
            </if>
        </trim>
        ORDER BY createDate DESC
    </select>
    <select id="operatorDetails" resultType="entity.OperatorDetails">
        SELECT
        a.`name1` stationName,
        a.plant_id plantId,
        a.plant_uid plantUid,
        a.`name` userName,
        a.create_date createTime,
        a.peak_power peakPower,
        a.device_sn deviceSn,
        a.wisdom_device_sn imei,
        c.operation_SN SN,
        a.cc_id iccId,
        b.start_time startTime,
        b.end_time endTime
        FROM
        v1_station_device a
        LEFT JOIN bto_Iotcard b ON a.wisdom_device_sn = b.imei
        LEFT JOIN bto_operation_message c ON a.wisdom_device_sn = c.IMEI
        WHERE
        a.special = '1'
        <if test="userName !=null and userName != ''">
            AND a.`name` LIKE CONCAT('%',#{userName},'%')
        </if>
        <if test="plantId !=null and plantId != ''">
            AND a.plant_id = #{plantId}
        </if>
        GROUP BY deviceSn
        ORDER BY startTime
    </select>
</mapper>