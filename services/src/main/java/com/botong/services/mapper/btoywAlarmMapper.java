package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.AlarmAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.OperatorInfoVO;

import java.util.List;

@Mapper
public interface btoywAlarmMapper extends BaseMapper<AlarmAnalysis> {
    /**
     * 电站告警信息列表
     * @param startTime
     * @param a
     * @param b
     * @return
     */
    List<AlarmAnalysis> AlarmInformationList(@Param("startTime")String startTime,@Param("a") Integer a,@Param("b") Integer b);

    /**
     * 运维器告警信息列表
     * @param page
     * @param pageSize
     * @return
     */
    List<OperatorInfoVO> OperatorAlarmInformationList(@Param("page") Integer page, @Param("pageSize") Integer pageSize);
}
