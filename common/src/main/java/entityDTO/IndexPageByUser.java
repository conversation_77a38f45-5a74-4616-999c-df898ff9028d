package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3 14:38
 */
@Data
@ApiModel("用户APP首页电站数量-APP")
public class IndexPageByUser implements Serializable {
    @ApiModelProperty("电站ID存放集合")
    private List<String> plantIds;
    @ApiModelProperty("总电站数量")
    private Integer totalStationCount = 1;
    @ApiModelProperty("正常电站数量")
    private Integer normalStationCount = 0;
    @ApiModelProperty("告警电站数量")
    private Integer alarmStationCount = 0;
    @ApiModelProperty("离线电站数量")
    private Integer offlinestationCount = 0;
    @ApiModelProperty("在线电站数量")
    private Integer onlinestationCount = 0;
    @ApiModelProperty("当日发电量")
    private String todayEnergy;
    @ApiModelProperty("当月发电量")
    private String monthEnergy;
    @ApiModelProperty("当年发电量")
    private String yearEnergy;
    @ApiModelProperty("总发电量")
    private String totalEnergy;
    @ApiModelProperty("总装机容量")
    private String peakPower;

    @ApiModelProperty("数据时间")
    private String dateTime;


}
