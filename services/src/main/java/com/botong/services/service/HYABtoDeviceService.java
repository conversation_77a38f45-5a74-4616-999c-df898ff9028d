package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.BtoDevice;
import entity.ResultChart;
import entityDTO.AppDeviceChartForm;
import entityDTO.DayDeviceChart;
import entityDTO.StationLunBo;
import vo.DateElectricityVo;
import vo.ElectricityStatisticsVo;
import vo.VersionInfoVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface HYABtoDeviceService extends IService<BtoDevice> {

    List<ElectricityStatisticsVo> inverterElectricityInfo();

    /**
     * 获取逆变器数据不止一个的用户
     */
    List<String> getUserByInverterMoreOne();

    /**
     * 获取逆变器数据只有一个的用户
     */
    List<String> getUserByInverterEqualOne();

    HashMap<String, Object> getUserComplexInfo(String plantId);

    float getCurrentPowerBySn(String sn);

    DateElectricityVo getDayMonthYearAllInfo(String dataloggerSn);

    HashMap<String, Object> getDayPowerBySn(String sn,String time,String tableName);

    HashMap<String, Object> getMonthElectricitySn(String plantId, String time);

    HashMap<String, Object> getYearElectricitySn(String plantId, String s);

    HashMap<String, Object> getAllElectricitySn(String deviceSns,String time);

    //根据sn获取日月年发电量
    List<BtoDevice> getElectricityBySn(String sn);

    BtoDevice getInverterDetails(String sn, String tableName, String time);

    List<String> selectDeviceByStation(String stationId);

    Object selectDeviceList(String plantId);

    Object chart(String timeType,String time, String stationId,String deviceSns);

    Object getStationDetailsHome(String stationId);

    Object getDeviceDetails(String sn);

    VersionInfoVo getDeviceVersionInfo(String sn);

    Object getDeviceAlertInfo(String plantUid,String stationId,Integer pageNum,Integer pageSize);

    //根据sn获取最新一条日月年发电量数据
    BtoDevice getElectricityBySnToOne(String sn);

    <T> HashMap<String, T> chart1(AppDeviceChartForm appDeviceChartForm);
}
