package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class StationListDTO implements Serializable {

    /**
     * 子电站用户id
     */
    private String userUid;

    /**
     * 查询类型
     * 参考值：
     * sort     排序
     * filter   筛选
     */
    private String selectType;

    /**
     * 根据什么字段进行查询
     * 参考值：
     * time         时间
     * electricity  电量
     */
    private String selectField;

    /**
     * 当前页
     */
    private Integer page;

    /**
     * 每页多少条数据
     */
    private Integer pageSize;

    /**
     * 依据的排序方式
     * timeBefore        创建时间早优先
     * timeAfter         创建时间晚优先
     * electricityMuch   累计发电量多优先
     * electricityFew    累计发电量少优先
     */
    private String sortWith;

    /**
     *
     */
    private String filterField;


}
