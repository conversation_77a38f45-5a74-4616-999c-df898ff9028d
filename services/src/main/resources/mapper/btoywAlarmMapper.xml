<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.btoywAlarmMapper">
    <resultMap id="alramAnalysis" type="entityDTO.AlarmAnalysis">
        <result property="sn" column="sn"/>
        <result property="mean" column="mean"/>
        <result property="startTime" column="startTime"/>
        <result property="grade" column="grade"/>
        <result property="name1" column="name1"/>
        <result property="name" column="name"/>
    </resultMap>
    <select id="AlarmInformationList" resultMap="alramAnalysis">
        SELECT
            a.sn,
            mean,
            start_time startTime,
            grade,
            name,
            name1
        FROM
            v_event_mean a
                LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
            b.special = '1'
          AND status = '0'
          AND start_time LIKE #{startTime}
        ORDER BY start_time DESC
        LIMIT ${a},${b}
    </select>
    <select id="OperatorAlarmInformationList" resultType="vo.OperatorInfoVO">
        SELECT
               a.plant_uid plantUid,
               a.wisdom_device_sn imei,
               a.alarm_name alarmName,
               a.alarm_time alarmTime,
               a.a_point_voltage aPointVoltage,
               a.b_point_voltage bPointVoltage,
               a.c_point_voltage cPointVoltage,
               b.name stationName
        FROM
        bto_operation_fault a
        LEFT JOIN bto_station_list b ON a.plant_uid = b.plant_uid
        WHERE
        b.special = '1'
        AND LEFT ( a.alarm_time, 10 )= CURDATE()
        AND a.end_time IS NOT NULL
        AND a.end_time &gt; DATE_SUB( now(), INTERVAL 10 MINUTE )
        ORDER BY alarm_time
        LIMIT #{page},#{pageSize}
    </select>
</mapper>