package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.WisdomDeviceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;


@Mapper
public interface WisdomDeviceMapper extends BaseMapper<WisdomDeviceDTO> {
    HashMap<String,String> WisdomDeviceInformation(@Param("plantId") String plantId);

    HashMap<String,String> selectRssi(@Param("plantId")String plantId);

    List<WisdomDeviceDTO> wisdomDeviceAlarmInformation(@Param("imei") String imei);
}
