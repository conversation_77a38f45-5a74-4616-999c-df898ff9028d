package com.botong.controller;

import com.botong.services.service.DeviceInformationService;
import com.botong.services.service.GFmonitoringCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@CrossOrigin
@RestController
@RequestMapping("DeviceInformation")
@Api(tags = "设备概况")
public class DeviceInformationController {
    @Autowired
    private DeviceInformationService deviceInformationService;
    @Autowired
    private GFmonitoringCenterService gFmonitoringCenterService;

    @ApiOperation("实时监控——设备概况-逆变器信息")
    @GetMapping("DeviceInformation")
    public AjaxResult DeviceInformation(String plantUid){
        return AjaxResult.success("操作成功",deviceInformationService.DeviceInformation(plantUid));
    }

    @ApiOperation("实时监控——设备概况-逆变器对应的sn")
    @GetMapping("DeviceInformationSN")
    public AjaxResult DeviceInformationSN(String plantUid){
        return AjaxResult.success("操作成功",deviceInformationService.DeviceInformationSN(plantUid));
    }

    @ApiOperation("实时监控——设备概况-逆变器对应的图表")
    @GetMapping("DeviceInformationTU")
    public AjaxResult DeviceInformationTU(String plantUid){
        return AjaxResult.success("操作成功",deviceInformationService.DeviceInformationTU(plantUid));
    }
    @ApiOperation("光伏监控中心")
    @GetMapping("GFmonitoringCenter")
    public AjaxResult GFmonitoringCenter(){
        return AjaxResult.success("操作成功",gFmonitoringCenterService.GFmonitoringCenter());
    }
}
