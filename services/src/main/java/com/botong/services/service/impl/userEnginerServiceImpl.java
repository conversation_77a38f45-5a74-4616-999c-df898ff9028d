package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.userEnginerMapper;
import com.botong.services.service.BtoStationEnginerService;
import com.botong.services.service.userEnginerService;
import entityDTO.BtoDeviceTypeDTO;
import entityDTO.btoProjectSign;
import entityDTO.userEnginerDTO;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static cn.hutool.poi.excel.sax.ElementName.f;

@Service
public class userEnginerServiceImpl extends ServiceImpl<userEnginerMapper, userEnginerDTO> implements userEnginerService {

    @Autowired
    private userEnginerMapper userEnginerMapper;

    @Autowired
    private BtoStationEnginerService btoStationEnginerService;
    @Override
    public int registeredUser(String cUserName, String cUsertel, String cUserEmail,String engnierId,Integer special) {
        String uuid = UUID.randomUUID().toString().toUpperCase();//用户id随机生成
        Date date=new Date();
        SimpleDateFormat dateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String regtiTime= dateFormat2.format(date);//注册时间
        if (userEnginerMapper.registeredUser(uuid,cUserName,cUsertel,cUserEmail,regtiTime,engnierId,special)==1){
            return 1;
        }else {
            return 0;
        }
    }

    @Override
    public List<userEnginerDTO> selectUid(String cUserName) {
        return userEnginerMapper.selectUid(cUserName);
    }

    @Override
    public List<btoProjectSign> QueryAreaUsers() {
        return userEnginerMapper.QueryAreaUsers();
    }

    @Override
    public int bindSmartOperator(String stationName, String smartOperatorName,Integer num) {
        String test= btoStationEnginerService.selectPlantUidByName(stationName);
        if (userEnginerMapper.bindSmartOperator(test,smartOperatorName,num)==1){
            return 1;
        }else {
            return 0;
        }
    }

    @Override
    public int updatePassword(String userUid, String password) {
        if (userEnginerMapper.updatePassword(userUid,password)==1){
            return 1;
        }else {
            return 0;
        }

    }

    @Override
    public List<userEnginerDTO> SelectcompanyId() {
        return userEnginerMapper.SelectcompanyId();
    }

    @Override
    public List<userEnginerDTO> SelUserAll() {
        return userEnginerMapper.SelUserAll();
    }

    @Override
    public Integer CheckUsername(String userName) {
        if (userEnginerMapper.CheckUsername(userName).isEmpty()){
            return 0;
        }else {
            return 1;
        }
    }

    @Override
    public Object versionView() {
        File file=new File("/etc/nginx/conf.d/gf/APP/gfEngineer.apk");
        String test = null;
        if (file.exists() && file.isFile()) {
            try {
                ApkFile apkFile = new ApkFile(file);
                ApkMeta apkMeta = apkFile.getApkMeta();
                test = apkMeta.getVersionName();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return test;
    }

    @Override
    public List<BtoDeviceTypeDTO> InverterModel() {
        return userEnginerMapper.InverterModel();
    }

    @Override
    public Object readFileName() {
        File file = new File("/etc/nginx/conf.d/imeiJson/");
        File fileArray[] = file.listFiles();
        String FileName=null;
        if (file.exists()==true){
            for (File f : fileArray) {
                FileName=f.getName();
            }
        }
        return FileName;
    }

    @Override
    public Integer QueryIMEIcode(String imei) {
        if (userEnginerMapper.QueryIMEIcode(imei)>=1){
            return 1;
        }else {
            return 0;
        }
    }

    @Override
    public List<BtoDeviceTypeDTO> getUseroPhone(String cUserPhone) {
        return userEnginerMapper.getUseroPhone(cUserPhone);
    }


}
