package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.lunboMapper;
import com.botong.services.service.lunboService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.Carousel;
import entityDTO.StationLunBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import utils.ListPageUtils;

import java.text.Collator;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class lunboServiceImpl extends ServiceImpl<lunboMapper, StationLunBo> implements lunboService {
    @Autowired
    private lunboMapper lunboMapper;

    @Override
    @Cacheable(cacheNames = "GF_web" ,key = " 'stationCarousel'+'page_'+#p0+'pageSize_'+#p1 ")
    public PageInfo<Carousel> stationCarousel(Integer page,Integer pageSize) {
        List<Carousel> stationCarousel = lunboMapper.stationCarousel();
//        Collections.sort(stationCarousel, new Comparator<Carousel>() {
//            @Override
//            public int compare(Carousel o1, Carousel o2) {
//                //获取中文环境
//                Comparator<Object> com = Collator.getInstance(java.util.Locale.CHINA);
//                return com.compare(o1.getStationName(), o2.getStationName());
//            }
//        });
        int total = stationCarousel.size();
        stationCarousel = new ArrayList<>(ListPageUtils.pageBySubCarouselList(stationCarousel, page, pageSize));
        PageInfo<Carousel> carouselPageInfo = new PageInfo<>(stationCarousel);
        carouselPageInfo.setTotal(total);
        return carouselPageInfo;
    }

    @Override
    @Cacheable(cacheNames = "GF_web" ,key = " 'sevenAndSixEnergy' " )//或者这样写key = "#id"
    public HashMap<String, Object> sevenAndSixEnergy() {
        HashMap<String, Object> sevenAndSixEnergyMap = new HashMap<>();
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        String time = simpleDateFormat.format(date);
        List<HashMap<String, String>> sevenDayEnergy = lunboMapper.powerSevenEnergy(time);
        List<HashMap<String, String>> sixMonthEnergy = lunboMapper.powerSIXEnergy();
        sevenAndSixEnergyMap.put("sevenDayEnergy",sevenDayEnergy);
        sevenAndSixEnergyMap.put("sixMonthEnergy",sixMonthEnergy);
        return sevenAndSixEnergyMap;
    }
}
