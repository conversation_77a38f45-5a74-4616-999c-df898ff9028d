package entityDTO;

import java.io.Serializable;
import entity.DevicePvInfo;
import lombok.Data;
import lombok.ToString;

@Data
public class AddDeviceForm extends DevicePvInfo implements Serializable  {
    /**
     * 随机生成的deviceId--UUID工具生成
     */
    private String deviceId;
    /**
     * 用户Uid
     */
    private String userUid;
    /**
     * 电站ID
     */
    private String plantId;
    /**
     * 逆变器SN
     */
    private String deviceSN;
    private String dataloggerSN;
    /**
     * 逆变器地址编号
     */
    private String deviceAddress;
    /**
     * 逆变器规格：manu_facturer厂家、model型号
     */
    private String manuFacturer;
    private String model;
    /**
     * 逆变器类型：0:并网 1：储能
     */
    private String type;
    /**
     * 站点
     */
    private String special;
    /**
     * 创建时间
     */
    private String createTime;

}
