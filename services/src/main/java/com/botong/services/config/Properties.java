//package com.botong.services.config;
//
//import com.google.common.collect.Maps;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.stereotype.Component;
//
//import java.time.Duration;
//import java.util.Map;
//
///**
// * <AUTHOR>
// */
//
//@Data
//@NoArgsConstructor
////@Component
//@ConfigurationProperties(prefix = "config")
//public class Properties {
//
////    private final Map<String, Duration> initCaches = Maps.newHashMap();
////
////    public Map<String, Duration> getInitCaches() {
////        return initCaches;
////    }
//}
