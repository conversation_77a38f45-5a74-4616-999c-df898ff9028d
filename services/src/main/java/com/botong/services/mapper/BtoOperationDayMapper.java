package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoOperation;
import entity.BtoOperationDay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * 运维器数据
 * 
 * <AUTHOR> @date 2022-08-19 14:24:48
 */
@Mapper
public interface BtoOperationDayMapper extends BaseMapper<BtoOperationDay> {


    List<BtoOperationDay> getUserBuySellInfoByMonth(@Param("plantUid") String plantUid, @Param("month") String month);

    List<BtoOperationDay> getUserBuySellInfoByYear(@Param("plantUid") String plantUid, @Param("year")  String year);

    List<BtoOperationDay> getUserBuySellALLInfo(@Param("plantUid") String plantUid);

     HashMap<String,String>WisdomDeviceInformation(@Param("plantId") String plantId);

}
