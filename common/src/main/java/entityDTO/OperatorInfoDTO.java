package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/6 17:50
 */
@Data
@ApiModel("运维器信息实体-用户APP")
public class OperatorInfoDTO implements Serializable {
    @ApiModelProperty("电站ID")
    private String plantId;
    @ApiModelProperty("电站UID")
    private String plantUID;
    @ApiModelProperty("运维器IMEI码")
    private String imei;
    @ApiModelProperty("项目专项")
    private String special;
}
