<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.VStationBaseMapper">
    <resultMap type="entity.VStationBase" id="vStationBaseMap">
        <result property="plantId" column="plant_id"/>
        <result property="name" column="name"/>
        <result property="city" column="city"/>
        <result property="plantType" column="plant_type"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
    </resultMap>


    <select id="selectAll" resultType="entity.VStationBase">
        SELECT
            plant_id,
            `name`,
            city,
            CASE
		WHEN plant_type = 0 THEN'并网'
		WHEN plant_type = 1 THEN'储能'
		WHEN plant_type = 2 THEN'混合'
		WHEN plant_type = 3 THEN'交流耦合'
        END,
	plant_uid ,
	longitude,
	latitude
FROM
	v_station_base;
    </select>

    <select id="Likeselect" resultType="entity.VStationBase">
        SELECT
        plant_id,
        name,
        city,
        plant_type,
       plant_uid ,
       longitude,
        latitude
        FROM
        v_station_base
        where 1=1
<if test="a != null or a != ''">
        and (plant_id like #{a}) or (name like #{a})
 </if>


    </select>

    <delete id="delectOne">

        delete from  v_station_base where  plant_uid = #{plantUid}
    </delete>

</mapper>