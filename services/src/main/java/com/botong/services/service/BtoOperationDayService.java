package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import entity.BtoOperationDay;

import java.util.List;


/**
 * 运维器数据
 *
 * <AUTHOR> @date 2022-08-19 14:24:48
 */
public interface BtoOperationDayService extends IService<BtoOperationDay> {


    List<BtoOperationDay> getUserBuySellInfoByMonth(String plantUid, String month);

    List<BtoOperationDay> getUserBuySellInfoByYear(String plantUid, String year);

    List<BtoOperationDay> getUserBuySellALLInfo(String plantUid);
}

