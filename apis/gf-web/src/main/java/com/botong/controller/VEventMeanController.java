package com.botong.controller;

import com.botong.services.service.VEventMeanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@Api(tags = "获取告警电站数，当日告警总数")
@RequestMapping("/VEventMean")
@CrossOrigin
public class VEventMeanController {
    @Autowired
    private VEventMeanService vEventMeanService;

    @GetMapping("/selectCountDay")
    @ApiOperation("告警总数")
    private AjaxResult selectCountDay(){
        return AjaxResult.success("查询成功！",vEventMeanService.selectCountDay());
    }

    @GetMapping("/selectCountSun")
    @ApiOperation("告警电站总数")
    private AjaxResult selectCountSun(){
        return AjaxResult.success("查询成功！",vEventMeanService.selectCountSun());
    }

    @GetMapping("/selectCountInHand")
    @ApiOperation("处理中")
    private AjaxResult selectCountInHand(){
        return AjaxResult.success("查询成功！",vEventMeanService.selectCountInHand());
    }

    @GetMapping("/selectCountHand")
    @ApiOperation("未处理")
    private AjaxResult selectCountHand(){
        return AjaxResult.success("查询成功！",vEventMeanService.selectCountHand());
    }

    @GetMapping("updateHostNoGrid")
    @ApiOperation("修改主机无电网")
    public AjaxResult updateHostNoGrid(){
        return AjaxResult.success("查询成功",vEventMeanService.updateHostNoGrid());
    }

    @GetMapping("stationEvent")
    @ApiOperation("电站告警处理")
    public AjaxResult stationEvent(){
        return AjaxResult.success("查询成功",vEventMeanService.stationEvent());
    }


}
