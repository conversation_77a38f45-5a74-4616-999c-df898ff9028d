<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.BtoStationEnginerMapper">
    <resultMap id="baseStation" type="entityDTO.BtoStationBaseEnginerDTO">
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="plantId" column="plant_id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="userUid" column="user_uid"/>
        <result property="country" column="country"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="town" column="town"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="plantType" column="plant_type"/>
        <result property="createDate" column="create_date"/>
        <result property="peakPower" column="peak_power"/>
        <result property="locale" column="locale"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="special" column="special"/>
        <result property="enginerId" column="enginer_id"/>
<!--        <result property="ownerPhone" column="owner_phone" />-->
<!--        <result property="ownerEmail" column="owner_email"/>-->
    </resultMap>
    <insert id="insertBase">
        insert into bto_station_base(name, user_id, plant_id, plant_uid, user_uid, country, state, city,
                                     town, address1, address2, longitude, latitude, plant_type, create_date,
                                     peak_power, locale, update_time, create_time, special, enginer_id)
        values (#{name}, #{userId}, #{plantId}, #{plantUid}, #{userUid}, #{country}, #{state},#{city},
                #{town}, #{address1}, #{address2}, #{longitude}, #{latitude}, #{planType}, #{createDate},
                #{peakPower}, #{locale}, #{updateTime}, #{createTime}, #{special}, #{enginerId})
    </insert>
    <update id="ModifyAlreadyregistered">
        UPDATE bto_user_hy set enrol='1' where user_uid=#{hyUserUid}
    </update>
    <!-- <resultMap type="entityDTO.BtoStationEnginerDTO" id="btoIndexMap">
         <result property="indexId" column="index_id"/>
         <result property="indexName" column="index_name"/>
         <result property="createDate" column="create_date"/>
     </resultMap>-->
    <select id="selectPlantUidByName" resultType="String">
        select plant_uid
        from bto_station_list
        where name = #{name}
    </select>
    <select id="SelStationAll" resultType="entityDTO.BtoStationEnginerDTO">
        select *
        from bto_station_list
    </select>
    <select id="CheckStationName" resultType="entityDTO.BtoStationEnginerDTO">
        select name
        from bto_station_list
        where name = #{stationName}
    </select>
    <select id="selectPlantIdBySpecial" resultType="java.lang.String">
        select special
        from bto_station_list
        where plant_id = #{plantId};
    </select>
    <select id="RequirementsPowerStationAlarms" resultType="entityDTO.AlarmAnalysisengnier">
        select b.name1,a.sn,a.mean alarmMessage,a.start_time startTime,a.grade,b.address1 from v_event_r a LEFT JOIN v1_station_device b on a.sn=b.device_sn where left(start_time,10) = #{time} and status='0' AND b.special='1'
    </select>
</mapper>