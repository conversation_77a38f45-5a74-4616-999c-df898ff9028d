package com.botong.controller;

import com.botong.services.service.VStationBaseService;
import entity.VStationBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

@RestController
@RequestMapping("/VStationBase")
@Api(tags = "站点档案")
@CrossOrigin
public class VStationBaseController {

    @Autowired
    private VStationBaseService vStationBaseService;

    @ApiOperation("查询所有的站点")
    @GetMapping("/selectAll")
    public AjaxResult selectAll(Integer page, Integer pageSize) {
        return AjaxResult.success("查询成功", vStationBaseService.selectAll(page, pageSize));
    }

    @ApiOperation("模糊查询站点")
    @GetMapping("/likeSelect")
    public AjaxResult likeSelect(Integer page, Integer pageSize, String a) {
        return AjaxResult.success("查询成功", vStationBaseService.Likeselect(page, pageSize, a));
    }

    @ApiOperation("新增站点信息")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody VStationBase vStationBase) {
        int add = vStationBaseService.add(vStationBase);
        if (add > 0) {
            return AjaxResult.success("增加成功！");
        } else {
            return AjaxResult.error("联系人plant_uid重复！增加失败");
        }
    }

    @ApiOperation("修改站点信息")
    @PutMapping("/update")
    public AjaxResult update(@RequestBody VStationBase vStationBase) {
        int update = vStationBaseService.update(vStationBase);
        if (update > 0) {
            return AjaxResult.success("修改成功！");
        } else {
            return AjaxResult.error("修改失败!");
        }
    }

    @ApiOperation("删除站点信息")
    @GetMapping("/delete")
    public AjaxResult delete(String plnatUid) {
        int i = vStationBaseService.delete(plnatUid);
        return AjaxResult.success("删除成功！");
    }


}
