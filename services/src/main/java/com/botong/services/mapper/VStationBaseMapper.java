package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.VStationBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VStationBaseMapper extends BaseMapper<VStationBase> {


    List<VStationBase> selectAll();

    List<VStationBase> Likeselect(@Param("a") String a);

    int delectOne(@Param("plantUid") String plantUid);
}
