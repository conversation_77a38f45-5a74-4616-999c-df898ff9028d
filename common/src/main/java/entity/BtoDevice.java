package entity;


import lombok.Data;


import java.util.Date;

/**
 * 逆变器数据
 * 
 * <AUTHOR> @date 2022-08-16 10:10:10
 */
@Data
public class BtoDevice {

	/**
	 * 逆变器对应的采集器SN
	 */
	private String dataloggerSn;

	/**
	 * 逆变器SN
	 */
	private String sn;

	/**
	 * 输入电流1路（A）
	 */
	private Float ipv1;

	/**
	 * 输入电流2路（A）
	 */
	private Float ipv2;

	/**
	 * 输入电流3路（A）
	 */
	private Float ipv3;
	/**
	 * 输入电流4路（A）
	 */
	private Float ipv4;
	/**
	 * 输入电流5路（A）
	 */
	private Float ipv5;
	/**
	 * 输入电流6路（A）
	 */
	private Float ipv6;
	/**
	 * 输入电流7路（A）
	 */
	private Float ipv7;
	/**
	 * 输入电流8路（A）
	 */
	private Float ipv8;
	/**
	 * 输入电流9路（A）
	 */
	private Float ipv9;

	/**
	 * 输入电压1路（V）
	 */
	private Float vpv1;

	/**
	 * 输入电压2路（V）
	 */
	private Float vpv2;

	/**
	 * 输入电压3路（V）
	 */
	private Float vpv3;
	/**
	 * 输入电压4路（V）
	 */
	private Float vpv4;
	/**
	 * 输入电压5路（V）
	 */
	private Float vpv5;
	/**
	 * 输入电压6路（V）
	 */
	private Float vpv6;
	/**
	 * 输入电压7路（V）
	 */
	private Float vpv7;
	/**
	 * 输入电压8路（V）
	 */
	private Float vpv8;
	/**
	 * 输入电压9路（V）
	 */
	private Float vpv9;

	/**
	 * 输出电流1路（A）
	 */
	private Float iac1;

	/**
	 * 输出电流2路（A）
	 */
	private Float iac2;

	/**
	 * 输出电流3路（A）
	 */
	private Float iac3;
	/**
	 * 输出电流4路（A）
	 */
	private Float iac4;
	/**
	 * 输出电流5路（A）
	 */
	private Float iac5;
	/**
	 * 输出电流6路（A）
	 */
	private Float iac6;
	/**
	 * 输出电流7路（A）
	 */
	private Float iac7;
	/**
	 * 输出电流8路（A）
	 */
	private Float iac8;
	/**
	 * 输出电流9路（A）
	 */
	private Float iac9;

	/**
	 * 输出电压1路（V）
	 */
	private Float vac1;

	/**
	 * 输出电压2路（V）
	 */
	private Float vac2;

	/**
	 * 输出电压3路（V）
	 */
	private Float vac3;
	/**
	 * 输出电压4路（V）
	 */
	private Float vac4;
	/**
	 * 输出电压5路（V）
	 */
	private Float vac5;
	/**
	 * 输出电压6路（V）
	 */
	private Float vac6;
	/**
	 * 输出电压7路（V）
	 */
	private Float vac7;
	/**
	 * 输出电压8路（V）
	 */
	private Float vac8;
	/**
	 * 输出电压9路（V）
	 */
	private Float vac9;
	/**
	 * 组串数目(1~9)
	 */
	private String pv1;
	private String pv2;
	private String pv3;
	private String pv4;
	private String pv5;
	private String pv6;
	private String pv7;
	private String pv8;
	private String pv9;

	/**
	 * 输出功率（W）
	 */
	private Float power;

	/**
	 * 
	 */
	private Float powerFactor;

	/**
	 * 当天发电量（kWh）
	 */
	private Float todayEnergy;

	/**
	 * 当月发电量（kWh）
	 */
	private Float monthEnergy;

	/**
	 * 当年发电量（kWh）
	 */
	private Float yearEnergy;

	/**
	 * 累计发电量（kWh）
	 */
	private Float totalEnergy;

	/**
	 * 温度（℃）
	 */
	private Float temperature;

	/**
	 * 频率（Hz）
	 */
	private Float fac;

	/**
	 * 频率（Hz）
	 */
	private Float fac1;

	/**
	 * 频率（Hz）
	 */
	private Float fac2;
	/**
	 * 频率（Hz）
	 */
	private Float fac3;
	/**
	 * 频率（Hz）
	 */
	private Float fac4;
	/**
	 * 频率（Hz）
	 */
	private Float fac5;
	/**
	 * 频率（Hz）
	 */
	private Float fac6;
	/**
	 * 频率（Hz）
	 */
	private Float fac7;
	/**
	 * 频率（Hz）
	 */
	private Float fac8;

	/**
	 * 数据时间
	 */
	private String time;

	/**
	 * 
	 */
	private String status;

	/**
	 * 
	 */
	private Date updateTime;

	/**
	 * 
	 */
	private Date createTime;


}
