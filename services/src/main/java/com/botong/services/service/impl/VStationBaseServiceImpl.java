package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.VStationBaseMapper;
import com.botong.services.service.VStationBaseService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.VStationBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VStationBaseServiceImpl extends ServiceImpl<VStationBaseMapper, VStationBase> implements VStationBaseService {

    @Autowired
    private VStationBaseMapper vStationBaseMapper;

    @Override
    public PageInfo<VStationBase> selectAll(Integer page,Integer pageSize){
        PageHelper.startPage(page,pageSize);
        List<VStationBase> vStationBases = vStationBaseMapper.selectList(null);

        PageInfo<VStationBase> info = new PageInfo<>(vStationBases);
        return info;
    }

    @Override
    public PageInfo<VStationBase> Likeselect(Integer page, Integer pageSize, String a){
        PageHelper.startPage(page,pageSize);
        String b = "%" + a + "%";
        List<VStationBase> bases = vStationBaseMapper.Likeselect(b);
        PageInfo<VStationBase> info = new PageInfo<>(bases);
        return info;
    }

    @Override
    public int add(VStationBase vStationBase){
        QueryWrapper<VStationBase> wrapper = new QueryWrapper<>();
        wrapper.eq("plant_uid",vStationBase.getPlantUid());
        if( vStationBaseMapper.selectOne(wrapper) != null ){
            return -1;
        } else {
            vStationBaseMapper.insert(vStationBase);
            return 1;
        }
    }

    @Override
    public int update(VStationBase vStationBase){
        QueryWrapper<VStationBase> wrapper = new QueryWrapper<>();
        wrapper.eq("plant_uid",vStationBase.getPlantUid());
        System.out.println(vStationBaseMapper.selectOne(wrapper));
        if (vStationBaseMapper.selectOne(wrapper) != null) {
                vStationBaseMapper.update(vStationBase, wrapper);
                return 1;
            }else {
                return -1;
            }
    }

    @Override
    public int delete(String plantUid){
        int i = vStationBaseMapper.delectOne(plantUid);
        return i;
    }

}
