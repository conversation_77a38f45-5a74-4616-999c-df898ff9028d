package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.BtoDeviceEnginerDTO;
import entityDTO.UpdateDeviceFormDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface BtoDeviceEnginerMapper extends BaseMapper<BtoDeviceEnginerDTO> {

    String selectStationIDByName(@Param("name") String name);


    List<BtoDeviceEnginerDTO> selectBySN(@Param("sn") String sn);


    Integer btostationBase(@Param("orentation") String orentation,@Param("plantId")String plantId);

    Integer btostationList(@Param("orentation") String orentation,@Param("plantId")String plantId);

    List<HashMap<String, String>> getDeviceCode(@Param("value") String value,@Param("type")String type);

    Integer updateDeviceList(@Param("updateDeviceFormDTO") UpdateDeviceFormDTO updateDeviceFormDTO);

    Integer updateDeviceModules(@Param("updateDeviceFormDTO") UpdateDeviceFormDTO updateDeviceFormDTO);

    Integer deleteBySN(@Param("updateDeviceFormDTO") UpdateDeviceFormDTO updateDeviceFormDTO);
}
