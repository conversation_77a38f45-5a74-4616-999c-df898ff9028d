package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.Carousel;
import entityDTO.StationLunBo;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;


public interface HYlunboService extends IService<StationLunBo> {
    PageInfo<Carousel> stationCarousel(Integer page,Integer pageSize);

    HashMap<String,Object> sevenAndSixEnergy();

//    List<HashMap<String,String>> powerSevenEnergy();
//
//    List<HashMap<String,String>> powerSIXEnergy();
}
