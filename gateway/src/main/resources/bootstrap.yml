# 应用服务 WEB 访问端口
server:
  port: 8808
spring:
  application:
    name: gateway # 应用名称
  cloud:
    # Nacos帮助文档: https://nacos.io/zh-cn/docs/concepts.html
    # Nacos认证信息
    nacos:
      discovery:
        # Nacos 服务发现与注册配置，其中子属性 server-addr 指定 Nacos 服务器主机和端口
        server-addr: 127.0.0.1:8848
        service: gateway
        namespace:
    gateway:
      # 路由数组[路由 就是指定当请求满足什么条件的时候转到哪个微服务]
      routes:
        # 我们⾃定义的路由 ID，保持唯⼀
        - id: gf-web
          # ⽬标服务地址（部署多实例）
          uri: lb://gf-web
          # gateway⽹关从服务注册中⼼获取实例信息然后负载后路由
          # 断⾔：路由条件，Predicate 接受⼀个输⼊参数，返回⼀个布尔值结果。该接⼝包含多种默认⽅法来将 Predicate 组合成其他复杂的逻辑（⽐如：与，或，⾮）。
          predicates:
            - Path=/gf-web/**
          filters: # 过滤器,请求在传递过程中可以通过过滤器对其进行一定的修改
            - StripPrefix=1              # 转发之前去掉1层路径
        - id: auth
          uri: lb://auth
          predicates:
            - Path=/auth/**
          filters: # 过滤器,请求在传递过程中可以通过过滤器对其进行一定的修改
            - StripPrefix=1              # 转发之前去掉1层路径
dubbo:
  registry:
    # 挂载到 Spring Cloud 注册中心
    address: spring-cloud://localhost
  cloud:
    # 订阅服务提供方的应用列表，订阅多个服务提供者使用 "," 连接
    subscribed-services: services,openfeign