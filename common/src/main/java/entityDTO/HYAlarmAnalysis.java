package entityDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class HYAlarmAnalysis implements Serializable {
    private String createDate;
    private String address;
    private String sn;
    private String startTime;
    private String updateTime;
    private String alarmMessage;
    private String mean;
    private int status;
    private String plantId;
    private String plantUid;
    private String stationName;
    private String pvele;
//    private String useele;
//    private String selfele;
//    private String sellele;
//    private String buyele;
    private String time;
    private Long tel;
    private int grade;
    private int num;
    private String IMEI;
    private String SN4G;

}
