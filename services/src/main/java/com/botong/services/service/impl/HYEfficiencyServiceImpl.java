package com.botong.services.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYEfficiencyMapper;
import com.botong.services.service.HYEfficiencyService;
import com.botong.services.utils.PageHelp1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.HyStationInfo;
import entity.OperatorDetails;
import entityDTO.EfficiencyDTO;
import entityDTO.EfficiencyPageDTO;
import entityDTO.HYAstationList;
import entityDTO.StationInfoConditionDTO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import utils.AjaxResult;
import utils.ListPageUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Service
public class HYEfficiencyServiceImpl extends ServiceImpl<HYEfficiencyMapper, EfficiencyDTO> implements HYEfficiencyService {

    @Autowired
    private HYEfficiencyMapper hyEfficiencyMapper;

    @Override
    public AjaxResult PowerGenerationMonth(String plantId, @NotNull String month, Integer page, Integer pageSize) {
        String[] curSplit = month.split("-");
        int year = Integer.parseInt(curSplit[0]);
        String curMonthStr = curSplit[1];
        Integer curMonthNum = Integer.parseInt(curMonthStr);
        String curMonthSql = year + (curMonthNum > 10 ? "-" + curMonthStr : "-" + curMonthStr).concat("%");
        // last
        Integer lastMonthNum = curMonthNum - 1 == 0 ? 12 : curMonthNum - 1;
        String lastMonthStr = lastMonthNum > 10 ? String.valueOf(lastMonthNum) : "0" + lastMonthNum;
        String lastMonthSql = lastMonthNum == 12 ? String.valueOf(year - 1) : year + "-" + lastMonthStr + "%";
        //当前月所有数据
        List<EfficiencyDTO> efficiencyCurMonth = hyEfficiencyMapper.PowerGenerationMonth(plantId, curMonthSql);
        // 上一个月所有数据
        List<EfficiencyDTO> efficiencyLastMonth = hyEfficiencyMapper.PowerGenerationMonth(plantId, lastMonthSql);

        if (CollUtil.isNotEmpty(efficiencyCurMonth) && CollUtil.isNotEmpty(efficiencyLastMonth)) {
            //调用求同比的方法
            Double[] yoy = getYoy(efficiencyLastMonth, efficiencyCurMonth, 8, 10);
            for (int i = 0; i < efficiencyCurMonth.size(); i++) {
                efficiencyCurMonth.get(i).setYoy(yoy[i]);
                System.out.println(efficiencyCurMonth);
                efficiencyCurMonth.set(i, efficiencyCurMonth.get(i));
            }
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurMonth, "月");
            for (int i = 0; i < efficiencyCurMonth.size(); i++) {
                efficiencyCurMonth.get(i).setMom(mom[i]);
                efficiencyCurMonth.set(i, efficiencyCurMonth.get(i));
            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurMonth, page, pageSize);
            return AjaxResult.success("请求成功!", subList);
        } else if (CollUtil.isNotEmpty(efficiencyCurMonth) && CollUtil.isEmpty(efficiencyLastMonth)) {
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurMonth, "月");
            for (int i = 0; i < efficiencyCurMonth.size(); i++) {
                efficiencyCurMonth.get(i).setMom(mom[i]);
                efficiencyCurMonth.set(i, efficiencyCurMonth.get(i));
            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurMonth, page, pageSize);
            return AjaxResult.success("无同比数据:上月份无数据", subList);
        } else if (CollUtil.isNotEmpty(efficiencyLastMonth) && CollUtil.isEmpty(efficiencyCurMonth)) {
            return AjaxResult.error("本月份无数据!");
        } else {
            return AjaxResult.error("本月份和上月份均无数据");
        }
    }


    @Override
    public AjaxResult PowerGenerationYear(String plantId, String year, Integer page, Integer pageSize) {
        String lastYear = String.valueOf(Integer.parseInt(year) - 1).concat("%");
        String curYear = year.concat("%");
        List<EfficiencyDTO> efficiencyCurYear = hyEfficiencyMapper.PowerGenerationYear(plantId, curYear);
        List<EfficiencyDTO> efficiencyLastYear = hyEfficiencyMapper.PowerGenerationYear(plantId, lastYear);
        if (CollUtil.isNotEmpty(efficiencyCurYear) && CollUtil.isNotEmpty(efficiencyLastYear)) {
            //调用求同比的方法
            Double[] yoy = getYoy(efficiencyLastYear, efficiencyCurYear, 0, 3);
            for (int i = 0; i < efficiencyCurYear.size(); i++) {
                efficiencyCurYear.get(i).setYoy(yoy[i]);
                System.out.println(efficiencyCurYear);
                efficiencyCurYear.set(i, efficiencyCurYear.get(i));
            }
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurYear, "年");
            for (int i = 0; i < efficiencyCurYear.size(); i++) {
                efficiencyCurYear.get(i).setMom(mom[i]);
                efficiencyCurYear.set(i, efficiencyCurYear.get(i));
            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurYear, page, pageSize);
            return AjaxResult.success("请求成功!", subList);
        } else if (CollUtil.isNotEmpty(efficiencyCurYear) && CollUtil.isEmpty(efficiencyLastYear)) {
            //调用求环比的方法
            Double[] mom = getMom(efficiencyCurYear, "年");
            for (int i = 0; i < efficiencyCurYear.size(); i++) {
                efficiencyCurYear.get(i).setMom(mom[i]);
                efficiencyCurYear.set(i, efficiencyCurYear.get(i));

            }
            EfficiencyPageDTO subList = ListPageUtils.pageBySubListByEfficiencyDTO(efficiencyCurYear, page, pageSize);
            return AjaxResult.success("无同比数据:上年份无数据", subList);
        } else if (CollUtil.isNotEmpty(efficiencyLastYear) && CollUtil.isEmpty(efficiencyCurYear)) {
            return AjaxResult.error("本年份无数据!");
        } else {
            return AjaxResult.error("本年份和上年份均无数据");
        }
    }

    @Override
    public AjaxResult StatisticsStationInfoByCondition(StationInfoConditionDTO stationInfoConditionDTO) {
        Integer page = stationInfoConditionDTO.getPage();
        Integer pageSize = stationInfoConditionDTO.getPageSize();
        PageHelper.startPage(page, pageSize);
        String timeFlag = stationInfoConditionDTO.getTimeFlag();
        String startTime = stationInfoConditionDTO.getStartTime();
        String endTime = stationInfoConditionDTO.getEndTime();
        if ("Y".equals(timeFlag)) {
            startTime = startTime.substring(0, 4);
            endTime = endTime.substring(0, 4);
        } else if ("M".equals(timeFlag)) {
            startTime = startTime.substring(0, 7);
            endTime = endTime.substring(0, 7);
        } else {
            startTime = startTime.substring(0, 10);
            endTime = endTime.substring(0, 10);
        }
        stationInfoConditionDTO.setStartTime(startTime);
        stationInfoConditionDTO.setEndTime(endTime);
        List<HyStationInfo> stationInfoByCondition = hyEfficiencyMapper.getStationInfoByCondition(stationInfoConditionDTO);
        for (int i = 0; i < stationInfoByCondition.size(); i++) {
            HyStationInfo hyStationInfo = stationInfoByCondition.get(i);
            //遍历获取日发电量字段
            Float energy = Optional.ofNullable(hyStationInfo.getEnergy()).orElse(0.0f);
//            Float energy = energyfield!=null ? energyfield : 0.0f;
            //遍历获取总装机容量
            Float peakPower = Float.valueOf(hyStationInfo.getPeakPower());
            //遍历获取总发电量
            Float totalEnergy = hyStationInfo.getTotalEnergy() != null ? hyStationInfo.getTotalEnergy() : 0.0f;
            String dateTimeStr = DateUtil.now();
            Long dateTimeMillis = System.currentTimeMillis();
            //获取数据更新时间(利用hutool转为dateTime类型)
            if (hyStationInfo.getDateTime() != null && !"".equals(hyStationInfo.getDateTime())) {
                dateTimeStr = hyStationInfo.getDateTime();
                DateTime dateTime = DateUtil.parse(dateTimeStr);
                // dateTime 转换为 时间戳 （单位：毫秒）
                dateTimeMillis = dateTime.getTime();
            }
            //获取当前系统时间
            Long currentTimeMillis = System.currentTimeMillis();

            //等效利用小时 = 日发电量 / 总装机容量
            Double equivalentUseHour = energy / peakPower / 1.00;
            // 收入 = 总发电量 * 0.45
            Double income = totalEnergy * 0.45;
            // 二氧化碳减排 (吨) = (总发电量 * 0.832) / 1000
            Double totalReduceCo2 = (totalEnergy * 0.832) / 1000;
            // 减少砍伐树木 (棵) = (总发电量 * 0.832) / 1800
            Double totalPlantTreeNum = (totalEnergy * 0.832) / 1800;
            // 离线时长 = 当前时间（时间戳）- 数据更新时间 （时间戳）- 20分钟
            Long offlineTimeMillis = currentTimeMillis - dateTimeMillis - (20 * 60 * 1000);
            //离线时长相差（精确到分）
            String offlineTime = DateUtil.formatBetween(offlineTimeMillis, BetweenFormatter.Level.HOUR);
            //计算后的数据依次存入List集合中的每一个对象中
            stationInfoByCondition.get(i).setDateTime(dateTimeStr);
            stationInfoByCondition.get(i).setEnergy(energy);
            stationInfoByCondition.get(i).setTotalEnergy(totalEnergy);
            stationInfoByCondition.get(i).setEquivalentUseHour(equivalentUseHour);
            stationInfoByCondition.get(i).setIncome(income);
            stationInfoByCondition.get(i).setTotalReduceCo2(totalReduceCo2);
            stationInfoByCondition.get(i).setTotalPlantTreeNum(totalPlantTreeNum);
            stationInfoByCondition.get(i).setOfflineTime(offlineTime);
        }
        PageInfo<HyStationInfo> stationInfoByConditionInfo = new PageInfo<>(stationInfoByCondition);
        return AjaxResult.success("查询成功", stationInfoByConditionInfo);
    }

    @Override
    public PageInfo<OperatorDetails> operatorDetails(String userName, String plantId, Integer page, Integer pageSize) {
        PageHelper.startPage(page, pageSize);
        List<OperatorDetails> operatorDetailsList = hyEfficiencyMapper.operatorDetails(userName, plantId);
        PageInfo<OperatorDetails> operatorDetailsPageInfo = new PageInfo<>(operatorDetailsList);
        return operatorDetailsPageInfo;
    }

    /**
     * 封装求同比数据的方法
     */
    @NotNull
    private static Double[] getYoy(List<EfficiencyDTO> lastEfficiencyDTOs, List<EfficiencyDTO> curEfficiencyDTOs, int startIndex, int endIndex) {
        //求出本月/年有效数据的集合大小
        int Isize = curEfficiencyDTOs.size();
        int Jsize = lastEfficiencyDTOs.size();
        //定义一个结果集存放同比数据并赋初值为0.0
        Double[] yoy = new Double[Isize];
        for (int i = 0; i < Isize; i++) {
            yoy[i] = 0.0;
        }
        int i = 0;
        int j = 0;
        while (i < Isize && j < Jsize) {

            //当前月/年的每一天/月的日期(Integer)
            Integer curDay = Integer.parseInt(curEfficiencyDTOs.get(i).getDate().substring(startIndex, endIndex));
            //上一月/年的每一天/月的日期(Integer)
            Integer lastDay = Integer.parseInt(lastEfficiencyDTOs.get(j).getDate().substring(startIndex, endIndex));
            //判断当前月日期是否小于或等于上月日期

            if (curDay <= lastDay) {
                //判断两个月/年的天是否是同一天/月
                if (curDay.equals(lastDay)) {
                    //直接求出这一天/月的同比数据并存入yoy结果集
                    Double curEnergy = curEfficiencyDTOs.get(i).getEnergy();
                    Double lastEnergy = lastEfficiencyDTOs.get(j).getEnergy();
                    double yoyDay = (curEnergy - lastEnergy) / lastEnergy;
                    if (!NumberUtil.isValid(lastEnergy)) {
                        yoy[i] = 0.0;
                    } else {
                        yoy[i] = yoyDay;
                    }
//                    yoy[i] = yoyDay;
                    //i和j的指针往后移一位
                    i++;
                    if (j < Jsize - 1) {
                        j++;
                    }
                }
                i++;
            } else {
                //j的指针往后移一位，i不动
                if (j >= Jsize) {
                    break;
                }
                j++;
            }
        }
        return yoy;
    }

    /**
     * 封装求环比
     */
    private static Double[] getMom(List<EfficiencyDTO> curEfficiencyDTOs, String dateStr) {
        //求本月/年数据条数
        int size = curEfficiencyDTOs.size();
        //定义一个结果集存放同比数据并赋初值为0.0
        Double[] mom = new Double[size];
        for (int i = 0; i < size; i++) {
            mom[i] = 0.0;
        }
        //求当前月/年的每一天/月
        for (int i = 1; i < curEfficiencyDTOs.size(); i++) {
            DateTime lastDate = DateUtil.parse(curEfficiencyDTOs.get(i - 1).getDate());
            DateTime curDate = DateUtil.parse(curEfficiencyDTOs.get(i).getDate());
            long between = 0L;
            if ("年".equals(dateStr)) {
                int lastMonth = lastDate.month();
                int curMonth = curDate.month();
                between = curMonth - lastMonth;
            } else {
                between = DateUtil.between(lastDate, curDate, DateUnit.DAY);
            }
            if (between == 1) {
                Double lastEnergy = curEfficiencyDTOs.get(i - 1).getEnergy();
                Double curEnergy = curEfficiencyDTOs.get(i).getEnergy();
                double curMom = (curEnergy - lastEnergy) / lastEnergy;
                if (!NumberUtil.isValid(lastEnergy)) {
                    mom[i] = 0.0;
                } else {
                    mom[i] = curMom;
                }
            }
        }
        return mom;
    }


}
