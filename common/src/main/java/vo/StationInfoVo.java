package vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "电站信息")
public class StationInfoVo {
    @ApiModelProperty(value = "电站ID",notes = "电站ID")
    private String plantId;
    @ApiModelProperty(value = "电站名",notes = "电站名")
    private String name;
    @ApiModelProperty(value = "装机容量",notes = "装机容量")
    private String peakPower;
    @ApiModelProperty(value = "创建时间",notes = "创建时间")
    private String createdate;
    @ApiModelProperty(value = "电站朝向",notes = "电站朝向")
    private String orentation;
    @ApiModelProperty(value = "电站地址",notes = "电站地址")
    private String address1;
    @ApiModelProperty(value = "用户手机号",notes = "用户手机号")
    private String ownerphone;

}
