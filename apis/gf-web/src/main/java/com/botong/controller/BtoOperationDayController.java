package com.botong.controller;

import com.botong.services.service.BtoOperationDayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import utils.AjaxResult;


/**
 * 运维器数据
 *
 * <AUTHOR> @date 2022-08-19 14:24:48
 */
@RestController
@RequestMapping("Operator")
@Api(tags = "运维器数据")
@CrossOrigin
public class BtoOperationDayController {

    @Autowired
    private BtoOperationDayService btoOperationDayService;

    @ApiOperation("获取用户用电月卖电信息，sell_tel：卖电，buy_tel：买电，auto_tel：自用")
    @GetMapping("getUserBuySellInfoByMonth")
    public AjaxResult getUserBuySellInfoByMonth(String plantUid,String month){return AjaxResult.success("操作成功",btoOperationDayService.getUserBuySellInfoByMonth(plantUid,month));}

    @ApiOperation("获取用户用电年卖电信息，sell_tel：卖电，buy_tel：买电，auto_tel：自用")
    @GetMapping("getUserBuySellInfoByYear")
    public AjaxResult getUserBuySellInfoByYear(String plantUid,String year){return AjaxResult.success("操作成功",btoOperationDayService.getUserBuySellInfoByYear(plantUid,year));}

    @ApiOperation("获取用户用电总卖电信息，sell_tel：卖电，buy_tel：买电，auto_tel：自用")
    @GetMapping("getUserBuySellALLInfo")
    public AjaxResult getUserBuySellALLInfo(String plantUid){return AjaxResult.success("操作成功",btoOperationDayService.getUserBuySellALLInfo(plantUid));}


}
