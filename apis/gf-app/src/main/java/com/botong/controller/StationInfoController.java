package com.botong.controller;

import com.botong.services.service.UserAppService;
import entityDTO.OperatorChartInfoDTO;
import entityDTO.OperatorInfoDTO;
import entityDTO.OperatorMonitorInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;
import utils.HttpStatus;
import vo.ChartConditionVO;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 9:39
 */
@RestController
@RequestMapping("stationInfo")
@Api(tags = "电站信息接口")
public class StationInfoController {
    @Autowired
    private UserAppService userAppService;
    @ApiOperation("电站详情信息")
    @GetMapping("stationDetails")
    public AjaxResult stationDetails(String userUID){
        return AjaxResult.success("操作成功",userAppService.stationDetails(userUID));
    }

    @ApiOperation("电站日月年总图表数据信息")
    @PostMapping("stationChartInfo")
    public AjaxResult stationChartInfo(@RequestBody ChartConditionVO chartConditionVO){
        return AjaxResult.success("操作成功",userAppService.stationChartInfo(chartConditionVO));
    }

    @ApiOperation("逆变器信息列表")
    @PostMapping ("inverterInfo")
    public AjaxResult inverterInfo(@RequestBody List<String>plantIds){
        return AjaxResult.success("操作成功",userAppService.inverterInfo(plantIds));
    }
    @ApiOperation("逆变器详情信息")
    @PostMapping ("inverterDetails")
    public AjaxResult inverterDetails(String inverterSN){
        return AjaxResult.success("操作成功",userAppService.inverterDetails(inverterSN));
    }
    @ApiOperation("运维器信息列表")
    @GetMapping ("operatorInfo")
    public AjaxResult operatorInfo( String userUID){
        List<OperatorInfoDTO> operatorInfoList = userAppService.operatorInfo(userUID);
        if (operatorInfoList.size()<=0){
            return AjaxResult.success(HttpStatus.NO_CONTENT,"此电站没有安装运维器设备",operatorInfoList);
        }
        return AjaxResult.success("查询成功",operatorInfoList);
    }
    @ApiOperation("运维器日月年总图表数据信息")
    @PostMapping ("operatorChartInfo")
    public AjaxResult operatorChartInfo(@RequestBody ChartConditionVO chartConditionVO){
        HashMap<String,Object> operatorChartInfo = userAppService.operatorChartInfo(chartConditionVO);
        if (operatorChartInfo.size()<=0){
            return AjaxResult.success(HttpStatus.NO_CONTENT,"数据为空",operatorChartInfo);
        }
        return AjaxResult.success("查询成功",operatorChartInfo);
    }

    @ApiOperation("运维器实时监控数据信息")
    @GetMapping ("operatorMonitorInfo")
    public AjaxResult operatorMonitorInfo(String plantId,String plantUID){
        OperatorMonitorInfoDTO operatorMonitorInfo = userAppService.operatorMonitorInfo(plantId, plantUID);
        if (operatorMonitorInfo==null){
            return AjaxResult.success(HttpStatus.NO_CONTENT,"数据为空",operatorMonitorInfo);
        }
        return AjaxResult.success("查询成功",operatorMonitorInfo);
    }



}
