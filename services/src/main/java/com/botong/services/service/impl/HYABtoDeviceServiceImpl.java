package com.botong.services.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYABtoDeviceMapper;
import com.botong.services.service.HYABtoDeviceListService;
import com.botong.services.service.HYABtoDeviceService;
import com.botong.services.service.HYABtoStationBaseService;
import com.botong.services.service.HYABtoStationListService;
import com.botong.services.vo.ChartVo;
import com.botong.services.vo.DayPowerVo;
import com.botong.services.vo.InverterVo;
import com.botong.services.vo.YearChartVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoDevice;
import entity.BtoDeviceList;
import entity.BtoStationBase;
import entity.ResultChart;
import entityDTO.AppDeviceChartForm;
import entityDTO.DayDeviceChart;
import entityDTO.StationConditionDTO;
import entityDTO.StationLunBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class HYABtoDeviceServiceImpl extends ServiceImpl<HYABtoDeviceMapper, BtoDevice> implements HYABtoDeviceService {

    @Autowired
    private HYABtoDeviceListService hyaBtoDeviceListService;

    @Autowired
    private HYABtoStationListService hyaBtoStationListService;

    @Autowired
    private HYABtoStationBaseService hyaBtoStationBaseService;

    @Autowired
    private HYABtoDeviceService hyaBtoDeviceService;

    @Autowired
    private HYABtoDeviceMapper hyaBtoDeviceMapper;

    @Override
    public List<ElectricityStatisticsVo> inverterElectricityInfo() {
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        tableName += format.format(date);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time = dateFormat.format(date);
        return baseMapper.inverterElectricityInfo(tableName, time);
    }

    @Override
    public List<String> getUserByInverterMoreOne() {
        return baseMapper.getUserByInverterMoreOne();
    }

    @Override
    public List<String> getUserByInverterEqualOne() {
        return baseMapper.getUserByInverterEqualOne();
    }

    @Override
    public HashMap<String, Object> getUserComplexInfo(String plantId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        List<BtoDeviceList> userInverterNum = hyaBtoDeviceListService.getUserInverterNum(plantId);
        int normalStatus = 0;
        int alertNum = 0;
        float currentPower = 0;
        double allElectrical = 0;
        ArrayList<String> sns = new ArrayList<>();
        for (BtoDeviceList btoDeviceList : userInverterNum) {
            if ("1".equals(btoDeviceList.getDeviceStatus())) {
                normalStatus++;
            }
            if ("2".equals(btoDeviceList.getDeviceStatus())) {
                alertNum++;
            }
            sns.add(btoDeviceList.getDataloggerSn());
        }
        //逆变器正常率
        hashMap.put("normalRate", (normalStatus / userInverterNum.size()) * 100);
        //逆变器数量
        hashMap.put("inverterNum", userInverterNum.size());
        //告警数量
        hashMap.put("alertNum", alertNum);
        //装机容量
        hashMap.put("installedCapacity", hyaBtoStationListService.getInstalledCapacityById(plantId));
        //当前功率、总发电量
        for (String sn : sns) {
            currentPower += getCurrentPowerBySn(sn);
            allElectrical += hyaBtoDeviceService.getDayMonthYearAllInfo(sn).getTotalEnergy();
        }
        hashMap.put("currentPower", currentPower);
        hashMap.put("allElectrical", allElectrical);
        hashMap.put("sns", sns);
        //经纬度
        BtoStationBase latitudeAndLongitude = hyaBtoStationBaseService.getLatitudeAndLongitude(plantId);
        hashMap.put("longitude", latitudeAndLongitude.getLongitude());
        hashMap.put("latitude", latitudeAndLongitude.getLatitude());
        //电站uid
        hashMap.put("plantUid", hyaBtoStationListService.getIdByUId(plantId));
        return hashMap;
    }

    @Override
    public float getCurrentPowerBySn(String sn) {
        QueryWrapper<BtoDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("datalogger_sn", sn);
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableName = "bto_device_" + dateFormat.format(date);
        return baseMapper.getCurrentPowerBySn(tableName, sn);
    }

    @Override
    public DateElectricityVo getDayMonthYearAllInfo(String dataloggerSn) {
        QueryWrapper<BtoDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("today_energy", "month_energy", "year_energy", "total_energy");
        queryWrapper.eq("datalogger_sn", dataloggerSn);
        queryWrapper.orderByDesc("update_time");

        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        tableName += dateFormat.format(date);

        BtoDevice btoDevice = baseMapper.getDayMonthYearAllInfo(tableName, dataloggerSn);

        DateElectricityVo dateElectricityVo = new DateElectricityVo();
        dateElectricityVo.setMonthEnergy(btoDevice.getMonthEnergy());
        dateElectricityVo.setYearEnergy(btoDevice.getYearEnergy());
        dateElectricityVo.setTodayEnergy(btoDevice.getTodayEnergy());
        dateElectricityVo.setTotalEnergy(btoDevice.getTotalEnergy());
        return dateElectricityVo;
    }

    @Override
    public HashMap<String, Object> getDayPowerBySn(String sn, String time, String tableName) {
        String tableName1 = "bto_device_" + tableName;
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        //        tableName += dateFormat.format(date);
        time += "%";
        List<DayPowerVo> dayPowerBySn = baseMapper.getDayPowerBySn(sn, tableName1, time);

        ArrayList<String> dates = new ArrayList<>();
        ArrayList<String> powers = new ArrayList<>();
        for (DayPowerVo btoDevice : dayPowerBySn) {
            dates.add(btoDevice.getDateTime());
            powers.add(btoDevice.getPower());
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("dates", dates);
        hashMap.put("powers", powers);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> getMonthElectricitySn(String plantId, String time) {
        HashMap<String, Object> hashMap = new HashMap<>();
        ArrayList<String> dates = new ArrayList<>();
        ArrayList<String> energys = new ArrayList<>();
        for (ChartVo chartVo : baseMapper.getMonthElectricitySn(plantId, time)) {
            dates.add(chartVo.getDate());
            energys.add(chartVo.getEnergy());
        }
        hashMap.put("dates", dates);
        hashMap.put("energys", energys);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> getYearElectricitySn(String plantId, String time) {
        HashMap<String, Object> hashMap = new HashMap<>();
        ArrayList<String> dates = new ArrayList<>();
        ArrayList<String> energys = new ArrayList<>();
        for (YearChartVo chartVo : baseMapper.getYearElectricitySn(plantId, time)) {
            dates.add(chartVo.getTime());
            energys.add(chartVo.getEnergy());
        }
        hashMap.put("dates", dates);
        hashMap.put("energys", energys);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> getAllElectricitySn(String deviceSns, String time) {
        HashMap<String, Object> hashMap = new HashMap<>();
        ArrayList<String> energys = new ArrayList<>();
        String[] split = deviceSns.split(",");
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        tableName += dateFormat.format(date);
        for (String s : split) {
            for (ChartVo chartVo : baseMapper.getAllElectricitySn(s, time += "%", tableName)) {
                hashMap.put(s, chartVo.getEnergy());
            }
        }
        return hashMap;
    }

    @Override
    public List<BtoDevice> getElectricityBySn(String sn) {
        HashMap<String, Object> hashMap = new HashMap<>();
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        tableName += dateFormat.format(date);
        String time = format.format(date);
        return baseMapper.getElectricityBySn(sn, tableName, time += "%");
    }

    @Override
    public BtoDevice getInverterDetails(String sn, String tableName, String time) {
        return baseMapper.getInverterDetails(sn, tableName, time);
    }

    @Override
    public List<String> selectDeviceByStation(String stationId) {
        return baseMapper.selectDeviceByStation(stationId);
    }

    @Override
    public Object selectDeviceList(String plantId) {
        DeviceListVo deviceListVo = baseMapper.selectDeviceList(plantId);
        return null;
    }

    @Override
    public Object chart(String timeType, String time, String stationId, String deviceSns) {
        HashMap<String, Object> hashMap = new HashMap<>();
        String[] split = deviceSns.split(",");
        hashMap.put("deviceSns", split);
        String tableName = "bto_device_";
        tableName += time.replace("-", "");
        if ("day".equals(timeType)) {
            for (String sn : split) {
                ArrayList<String> times = new ArrayList<>();
                ArrayList<String> powes = new ArrayList<>();
                for (DayPowerVo dayPowerVo : baseMapper.getNewDayPowerBySn(sn, tableName, time)) {
                    times.add(dayPowerVo.getDateTime());
                    powes.add(dayPowerVo.getPower());
                    hashMap.put(sn + "time", times);
                    hashMap.put(sn + "powes", powes);
                }
                Double todayEnergy = baseMapper.getEnergy(time, stationId, timeType);
                hashMap.put("todayEnergy", todayEnergy);
            }
        } else if ("month".equals(timeType)) {
            List<MonthElectricityVo> newMonthElectricitySn = baseMapper.getNewMonthElectricitySn(stationId, time.concat("%"));
            hashMap.put(stationId, newMonthElectricitySn);
            Double monthEnergy = baseMapper.getEnergy(time, stationId, timeType);
            hashMap.put("monthEnergy", monthEnergy);
        } else if ("year".equals(timeType)) {
            hashMap.put(stationId, baseMapper.getYearElectricitySn(stationId, time));
            Double yearEnergy = baseMapper.getEnergy(time, stationId, timeType);
            hashMap.put("yearEnergy", yearEnergy);
        } else if ("all".equals(timeType)) {
            Date date = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            for (String sn : split) {
                List<ChartVo> allElectricitySn = baseMapper.getAllElectricitySn(sn, time.concat("%"), "bto_device_" + simpleDateFormat.format(date));
                hashMap.put(sn, allElectricitySn);
            }
            Double totalEnergy = baseMapper.getTotalEnergy(stationId);
            hashMap.put("totalEnergy", totalEnergy);
        } else {
            hashMap.put("message", "时间类型错误");
        }
        return hashMap;
    }

    @Override
    public <T> HashMap<String, T> chart1(AppDeviceChartForm appDeviceChartForm) {
        String plantId = appDeviceChartForm.getPlantId();
        String time = appDeviceChartForm.getTime();
        String timeType = appDeviceChartForm.getTimeType();
        String suffix = appDeviceChartForm.getTime().replace("-", "");
        String tableName = "bto_device_" + suffix;
        String[] deviceSns = appDeviceChartForm.getDeviceSns();

        switch (timeType) {
            case "day":
                //存放所有逆变器发电量之和
                BigDecimal sumTotalEnergy = new BigDecimal("0");
                //定义最终的返回结果集
                HashMap<String, DayDeviceChart> resultMapDay = new HashMap<>();
                //通过SN数组查询 所有逆变器当前所有时间段的功率、发电量数据
                List<DayPowerVo> deviceChart = hyaBtoDeviceMapper.getDeviceChart(tableName, appDeviceChartForm.getDeviceSns(), time);
                //通过数据库查询多个逆变器的并集时间轴
                List<String> dateTimeList = hyaBtoDeviceMapper.getDateTimeList(tableName, plantId, time);
                //遍历SN数组
                for (String deviceSn : deviceSns) {
                    //定义未处理前的时间数据集合
                    List<String> oldTimeList = new ArrayList<String>();
                    //定义功率数据集合
                    List<String> powerList = new ArrayList<>();
                    //定义单个结果对象：{ sn, powerList, dateTimeList, todayEnergy}
                    DayDeviceChart dayDeviceChart = new DayDeviceChart();
                    //SN存入结果对象
                    dayDeviceChart.setSn(deviceSn);
                    //时间集合存入结果对象
                    dayDeviceChart.setDateTimeList(dateTimeList);
                    //从deviceChart集合中取出所有当前循环SN的数据
                    List<DayPowerVo> collect = deviceChart.stream().filter(dayPowerVo -> {
                        return deviceSn.equals(dayPowerVo.getSn());
                    }).collect(Collectors.toList());
                    //但collect小于等于0时，直接返回空对象
                    if (collect.size() <= 0) {
                        for (String sn : deviceSns) {
                            resultMapDay.put(sn, new DayDeviceChart(sn, powerList, dateTimeList, "0.0"));
                        }
                        return (HashMap<String, T>) resultMapDay;
                    }
                    //取最后一条todayEnergy存入结果对象
                    dayDeviceChart.setTodayEnergy(collect.get(collect.size() - 1).getTodayEnergy());
                    //遍历当前循环SN的每一个对象，并将每一条功率和时间记录分别存入powerList和oldTimeList
                    collect.forEach(dayPowerVo -> {
                        powerList.add(dayPowerVo.getPower());
                        oldTimeList.add(dayPowerVo.getDateTime());
                    });
                    //判断当时间轴并集长度大于powerList长度时，对powerList进行数据填充
                    if (dateTimeList.size() > powerList.size()) {
                        List<String> tempList = new ArrayList<>();
                        tempList.addAll(dateTimeList);
                        tempList.removeAll(oldTimeList);
                        for (String addTime : tempList) {
                            collect.add(new DayPowerVo(deviceSn, "0.0", "0.00", addTime));
                        }
//                        boolean flag = dateTimeList.get(dateTimeList.size() - 1).equals(collect.get(collect.size() - 1).getDateTime());
//                        String status = hyaBtoDeviceMapper.selectStatusByDeviceSN(deviceSn);
//                        if(!flag) {
//                            if( "0".equals(status) || "2".equals(status) ){
//                                for (String addTime : tempList) {
//                                    collect.add(new DayPowerVo(deviceSn, "1.0", "0.00", addTime));
//                                }
//                            }else {
//                                collect.add(collect.get(collect.size() - 1));
//                            }
//                        }else{
//                            for (String addTime : tempList) {
//                                collect.add(new DayPowerVo(deviceSn, "1.0", "0.00", addTime));
//                            }
//                        }
//                        collect.remove(collect.size() - 1);
                    }
                    //对填充完的数据进行按时间排序，得到有序的当前循环SN的数据集合collect
                    ListUtil.sortByProperty(collect, "dateTime");
                    //清空powerList数据
                    powerList.clear();
                    //对collect集合进行遍历，得到所有powerList集合
                    for (int i = 0; i < collect.size(); i++) {
                        powerList.add(collect.get(i).getPower());
                    }
                    //将最终的功率数组存入结果对象
                    dayDeviceChart.setPowerList(powerList);
                    sumTotalEnergy = sumTotalEnergy.add(new BigDecimal(dayDeviceChart.getTodayEnergy()));
                    //将当前SN对应的结果对象存入最终结果集合
                    resultMapDay.put(deviceSn, dayDeviceChart);
                }
                List<String> totalPowerList = hyaBtoDeviceMapper.selectTotalPower(tableName, plantId);
//                Set<Map.Entry<String, DayDeviceChart>> entries = resultMapDay.entrySet();

//                for (Map.Entry<String, DayDeviceChart> entry : entries) {
//                    String key = entry.getKey();
//                    DayDeviceChart dayDeviceChart = resultMapDay.get(key);
//                    List<String> powerList = dayDeviceChart.getPowerList();
//
//                    for(String power: powerList){
//                        Integer.parseInt(power);
//                    }
//                }
                DayDeviceChart totalPowerChart = new DayDeviceChart("total", totalPowerList, dateTimeList, sumTotalEnergy.toString());
                resultMapDay.put("totalPowerChat", totalPowerChart);
                return (HashMap<String, T>) resultMapDay;


            case "month":
                HashMap<String, Object> resultMapMonth = new HashMap<>();
                List<MonthElectricityVo> newMonthElectricitySn = baseMapper.getNewMonthElectricitySn(plantId, time.concat("%"));
                resultMapMonth.put(plantId, newMonthElectricitySn);
                Double monthEnergy = baseMapper.getEnergy(time, plantId, timeType);
                resultMapMonth.put("monthEnergy", monthEnergy);
                return (HashMap<String, T>) resultMapMonth;
            case "year":
                HashMap<String, Object> resultMapYear = new HashMap<>();
                resultMapYear.put(plantId, baseMapper.getYearElectricitySn(plantId, time));
                Double yearEnergy = baseMapper.getEnergy(time, plantId, timeType);
                resultMapYear.put("yearEnergy", yearEnergy);
                return (HashMap<String, T>) resultMapYear;
            case "all":
                HashMap<String, Object> resultMapAll = new HashMap<>();
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                tableName = "bto_device_" + sdf.format(date);
                List<ChartVo> allEnergy = baseMapper.getAllElectricity(plantId, tableName);
                Double totalEnergy = baseMapper.getTotalEnergy(plantId);
                resultMapAll.put("totalEnergy", totalEnergy);
                resultMapAll.put("allEnergy", allEnergy);
                return (HashMap<String, T>) resultMapAll;
            default:
                HashMap<String, Object> resultMapMsg = new HashMap<>();
                resultMapMsg.put("msg", "时间类型匹配错误");
                return (HashMap<String, T>) resultMapMsg;
        }
    }

    @Override
    public Object getStationDetailsHome(String stationId) {
        HashMap<String, Object> hashMap = new HashMap<>();

        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        tableName += dateFormat.format(date);
        StationDetailsHomeVo stationDetailsHomeVo = baseMapper.getStationDetailsHome(stationId, tableName);
        hashMap.put("entity", stationDetailsHomeVo);

        //当日收益
        hashMap.put("dayIncome", Float.parseFloat(stationDetailsHomeVo.getTodayEnergy()) * 0.45);
        //累计收益
        hashMap.put("allIncome", Float.parseFloat(stationDetailsHomeVo.getTotalEnergy()) * 0.45);
        //累计植树
        hashMap.put("allTree", Float.parseFloat(stationDetailsHomeVo.getTotalEnergy()) * 0.832 / 1800);
        //累计减排Co2
        hashMap.put("allReduction", Float.parseFloat(stationDetailsHomeVo.getTotalEnergy()) * 0.000997);
        return hashMap;
    }

    @Override
    public Object getDeviceDetails(String sn) {
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        tableName += dateFormat.format(date);
        return hyaBtoDeviceMapper.getDeviceDetails(sn, tableName);
    }

    @Override
    public VersionInfoVo getDeviceVersionInfo(String sn) {
        return baseMapper.getDeviceVersionInfo(sn);
    }

    @Override
    public Object getDeviceAlertInfo(String plantUid, String stationId,Integer pageNum,Integer pageSize) {
        PageHelper.startPage(pageNum,pageSize);
        List<DeviceAlertVo> deviceAlertInfo = hyaBtoDeviceMapper.getDeviceAlertInfo(stationId);
        List<OperatorInfoVO> wisdomAlarmInfo = hyaBtoDeviceMapper.getWisdomALarmInfo(plantUid);
        PageInfo deviceAlertPage = new PageInfo(deviceAlertInfo);
        PageInfo wisdomAlarmPage = new PageInfo(wisdomAlarmInfo);
        HashMap<String, PageInfo> deviceAlertMap = new HashMap<>(2);
        deviceAlertMap.put("deviceAlertInfo", deviceAlertPage);
        deviceAlertMap.put("wisdomAlarmInfo", wisdomAlarmPage);
        return deviceAlertMap;
    }

    @Override
    public BtoDevice getElectricityBySnToOne(String sn) {
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        tableName += dateFormat.format(date);
        String time = format.format(date);



        return baseMapper.getElectricityBySnToOne(sn, tableName, time += "%");
    }

}
