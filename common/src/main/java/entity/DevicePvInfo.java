package entity;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;

@Data
public class DevicePvInfo implements Serializable {
    private String orientation;
    private String SN;
    private String capacity;
    private Integer pv1OnDc1;
    private Integer pv1OnDc2;
    private Integer pv1OnDc3;
    private Integer pv1OnDc4;
    private Integer pv2OnDc1;
    private Integer pv2OnDc2;
    private Integer pv2OnDc3;
    private Integer pv2OnDc4;
    private Integer pv3OnDc1;
    private Integer pv3OnDc2;
    private Integer pv3OnDc3;
    private Integer pv3OnDc4;
    private Integer pv4OnDc1;
    private Integer pv4OnDc2;
    private Integer pv4OnDc3;
    private Integer pv4OnDc4;
    private Integer pv5OnDc1;
    private Integer pv5OnDc2;
    private Integer pv5OnDc3;
    private Integer pv5OnDc4;
    private Integer pv6OnDc1;
    private Integer pv6OnDc2;
    private Integer pv6OnDc3;
    private Integer pv6OnDc4;
    private Integer pv7OnDc1;
    private Integer pv7OnDc2;
    private Integer pv7OnDc3;
    private Integer pv7OnDc4;
    private Integer pv8OnDc1;
    private Integer pv8OnDc2;
    private Integer pv8OnDc3;
    private Integer pv8OnDc4;
    private Integer pv9OnDc1;
    private Integer pv9OnDc2;
    private Integer pv9OnDc3;
    private Integer pv9OnDc4;
    private Integer pv10OnDc1;
    private Integer pv10OnDc2;
    private Integer pv10OnDc3;
    private Integer pv10OnDc4;
    private Integer pv11OnDc1;
    private Integer pv11OnDc2;
    private Integer pv11OnDc3;
    private Integer pv11OnDc4;
    private Integer pv12OnDc1;
    private Integer pv12OnDc2;
    private Integer pv12OnDc3;
    private Integer pv12OnDc4;
    private String updateTime;
    HashMap<String,Double> kValueMap;
}
