package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/4 11:36
 */
@Data
@ApiModel("逆变器详情信息实体-APP")
public class InverterDetailsDTO implements Serializable {
    @ApiModelProperty("逆变器SN")
    private String inverterSN;
    @ApiModelProperty("逆变器厂商")
    private String manufacturer;
    @ApiModelProperty("逆变器型号")
    private String model;
    @ApiModelProperty("设备所有人")
    private String  userName;
    @ApiModelProperty("显示版本号")
    private String displayVersion;
    @ApiModelProperty("控制版本号")
    private String masterVersion;
    @ApiModelProperty("软件版本号")
    private String slaveVersion;
    @ApiModelProperty("光伏输入电路数量")
    private Integer pvInputNumber;
}
