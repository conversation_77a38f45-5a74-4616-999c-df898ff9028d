package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.VEventMean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface HYVEventMeanMapper extends BaseMapper<VEventMean> {
    /**
     * 告警总数
     * @param time
     * @return
     */
    int selectCountDay(@Param("time") String time);
    /**
     * 天告警数
     * @param time
     * @return
     */
    int selectCountSun(@Param("time") String time);
    /**
     * 处理中
     * @param time
     * @return
     */
    int selectCountInHand(@Param("time") String time);
    /**
     * 未处理
     * @param time
     * @return
     */
    int selectCountHand(@Param("time") String time);
    /**
     * 获取已处理
     * @param time
     * @return
     */
    Integer selectProcessedNum(String time);

    Integer unhandledNum();
}
