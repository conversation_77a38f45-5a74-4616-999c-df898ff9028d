package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/4 9:27
 */
@Data
@ApiModel("逆变器告警信息实体")
public class AppAlarmInfoDTO implements Serializable {
    @ApiModelProperty("逆变器SN码")
   private String SN;
    @ApiModelProperty("告警信息")
   private String alarmMessage;
    @ApiModelProperty("告警含义")
   private String alarmMean;
    @ApiModelProperty("告警级别")
    private String grade;
    @ApiModelProperty("告警开始时间")
   private String startTime;

}
