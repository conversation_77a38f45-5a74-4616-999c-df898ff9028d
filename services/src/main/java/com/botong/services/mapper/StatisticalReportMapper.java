package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.StatisticalReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StatisticalReportMapper extends BaseMapper<StatisticalReport> {
    List<StatisticalReport> PowerGenerationStatisticsDay(@Param("shi") String shi,@Param("time2") String time2, @Param("sn")String sn,@Param("page")String page,@Param("pageSize")String pageSize);

    List<StatisticalReport> PowerGenerationStatisticsMonth(@Param("date") String date,@Param("plantId") String plantId);

    List<StatisticalReport> PowerGenerationStatisticsYear(@Param("date") String date,@Param("plantId") String plantId);
}
