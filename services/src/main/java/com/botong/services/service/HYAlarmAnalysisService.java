package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.HyStationAlarmInfo;
import entityDTO.*;
import utils.AjaxResult;

import java.util.HashMap;
import java.util.List;

public interface HYAlarmAnalysisService extends IService<AlarmAnalysis> {
//    PageInfo<AlarmAnalysis> AlarmAnalysisListForm(Integer page, Integer pagesize);
//    PageInfo<AlarmAnalysis> AlarmAnalysisTimeIntervalQuery(String time1, String time2, int page, int pagesize,String plantUid);
//
//    PageInfo<AlarmAnalysis> AlarmAnalysisTconditionQuery(String startTime, int page, int pagesize, String name, String name1, String sn);
//
//    PageInfo<AlarmAnalysis> AlarmAnalysisPolymerizationAll(String startTime, String endTime, String page, String pageSize);
//
//    //多个
//    PageInfo<AlarmAnalysis> AlarmAnalysisPolymerization( String page, String pageSize, String plantUid);
//
//    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarm(String startTime, String endTime, String page, String pageSize,int grade);
//
    //列表表格
//    PageInfo<AlarmAnalysis> AlarmAnalysisListFormplantUid(Integer page, Integer pageSize, String plantUid);
//
//    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll(String startTime, String endTime, String page, String pageSize, int grade, String plantUid);
//
//    List<AlarmAnalysis> AlarmAnalysisTimeIntervalAll(String time1, String time2);
//
//    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll3(Integer page, Integer pageSize,String name1,String startTime,String endTime,String grade);
//
//    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmQuann(String page, String pageSize);
//
//    Object AlarmAnalysisPolymerizationplantUid(String startTime, String endTime, String page, String pageSize, String plantUid);
//
//    Object totalAlarmStatistics();
//
//    Object totalAlarmStatisticsTime(String time);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIME(String time, String plantUid);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMETU(String time);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEgeren(String time, String plantUid);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenyaoxin(String time);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennyaoxin(String time, String plantUid);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEstatistics(String time);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennstatistics(String time, String plantUid);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEEquipmentStatistics(String time);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenEquipmentStatistics(String time, String plantUid);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMELevelStatistics(String time);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerennLevelStatistics(String time, String plantUid);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEyaoxinlist(String time);
//
//    List<AlarmAnalysis> totalAlarmStatisticsTIMEgerenlist(String time, String plantUid);

//    PageInfo<AlarmAnalysis> AlarmAnalysisHistoryAlarmAll2(Integer page, Integer pageSize, String name1, String grade);

//    PageInfo<AlarmAnalysis> AlarmAnalysisListFormplantUid(Integer page, Integer pageSize, String plantUid);

    /**
     * 实时报警----聚合&列表--&自检提示列表--汇总--五合一
     * @return
     * @throws ClassNotFoundException
     * @throws NoSuchFieldException
     */
    AjaxResult AlarmAnalysisByCondition(AlarmAnalysisConditionDTO alarmAnalysisConditionDTO) throws ClassNotFoundException, NoSuchFieldException;

    /**
     * 历史告警信息
     * @param alarmHistoryConditionDTO
     * @return
     */
    AjaxResult AlarmHistoryByCondition(AlarmHistoryConditionDTO alarmHistoryConditionDTO);

    /**
     * 告警信息处理
     * @param alarmHandlerDTO
     */
    void alarmHandler(AlarmHandlerDTO alarmHandlerDTO);

    /**
     * 运维器数据
     * @param page
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param stationName
     * @return
     */
    PageInfo<HYAlarmAnalysis> OperatorData(Integer page,Integer pageSize,String startTime, String endTime,String stationName);

    /**
     * 运维器事件数据
     * @param page
     * @param pageSize
     * @param alarmTime
     * @param address
     * @param endTime
     * @return
     */
    PageInfo<OperatorEvents> OperatorEventData( Integer page, Integer pageSize,String alarmTime, String address,String endTime);

    /**
     * 运维器数据-日综合
     * @param page
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param stationName
     * @return
     */
    PageInfo<HYAlarmAnalysis> OperatorDataMonth(Integer page, Integer pageSize, String startTime, String endTime, String stationName);

    /**
     * 电站列表--告警信息
     * @param page
     * @param pageSize
     * @param plantUid
     * @return
     */
    PageInfo<HyStationAlarmInfo> stationAlarmInfoByPlantUid(Integer page, Integer pageSize, String plantUid);

    /**
     * 某个电站的当日告警数(根据电站id查询)
     * @param plantId
     * @return
     */
    HashMap<String,Integer> getCurDayAlarmNum(String plantId);
}
