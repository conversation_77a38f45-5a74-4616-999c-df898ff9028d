<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.VBaseDeviceDTOMapper">
    <resultMap type="entityDTO.VBaseDeviceDTO" id="vBaseDeviceDTOMap">
        <result property="sn" column="sn"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="time" column="time"/>
        <result property="name" column="name"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="address" column="address"/>
        <result property="plantUid" column="plantUid"/>
    </resultMap>

    <select id="selectAll" resultMap="vBaseDeviceDTOMap">
        SELECT
            a.sn sn,
            today_energy,
            time,
            b.`name` name,
            b.state state,
            b.city city,
            b.address1 address,
            b.plant_uid plantUid
        FROM
            bto_device_${table} a
            LEFT JOIN v_base_device b ON a.sn = b.sn
        WHERE
            time LIKE #{time}
          AND state LIKE #{state}
          AND city LIKE #{city}
          AND address1 LIKE #{address}
          AND a.sn = #{sn}

    </select>


    <select id="selectAllSn" resultType="String">
        SELECT
            distinct  a.sn sn
        FROM
            bto_device_${table} a
                LEFT JOIN v1_station_device b ON a.sn = b.sn
    </select>

    <select id="selectSn" resultType="String">
        SELECT distinct
            a.sn sn
        FROM
            bto_device_${table} a
                LEFT JOIN v_base_device b ON a.sn = b.sn
        WHERE
            state LIKE #{state}
          AND city LIKE #{city}
          AND address1 LIKE #{address}
    </select>

    <select id="selectMonthSn" resultType="String">
        SELECT distinct
            b.sn sn
        FROM
            v_day a
                LEFT JOIN v_base_device b ON a.plant_id = b.plant_id
        WHERE

            state LIKE #{state}
          AND city LIKE #{city}
          AND b.address1 LIKE #{address}
    </select>

    <select id="selectMonth" resultMap="vBaseDeviceDTOMap">
        SELECT
            b.sn,
            a.date time,
	a.energy today_energy,
	b.`name` NAME,
	b.state state,
	b.city city,
	b.address1 address,
    b.plant_uid plantUid
        FROM
            v_day a
            LEFT JOIN v_base_device b ON a.plant_id = b.plant_id
        WHERE
            a.date LIKE #{month}
          AND state LIKE #{state}
          AND city LIKE #{city}
          AND b.address1 LIKE #{address}
          AND b.sn = #{sn}

    </select>

    <select id="selectYear" resultMap="vBaseDeviceDTOMap">
        SELECT
            b.sn,
            LEFT ( a.date, 7 ) time,
            sum( a.energy ) today_energy,
            b.`name` NAME,
            b.state state,
            b.city city,
            b.address1 address,
            b.plant_uid plantUid
        FROM
            v_day a
            LEFT JOIN v_base_device b ON a.plant_id = b.plant_id
        WHERE
            a.date LIKE #{year}
          AND state LIKE #{state}
          AND city LIKE #{city}
          AND b.address1 LIKE #{address}
          AND b.sn = #{sn}
        GROUP BY
            time

    </select>


</mapper>