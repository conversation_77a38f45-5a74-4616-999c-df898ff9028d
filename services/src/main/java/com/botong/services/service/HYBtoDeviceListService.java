package com.botong.services.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entity.BtoDeviceList;
import entityDTO.*;

import java.util.HashMap;
import java.util.List;

public interface HYBtoDeviceListService extends IService<BtoDeviceList> {
/*   Integer Deviceonline();
    Integer DeviceAllNum();
 Integer DeviceStatus2();*/

     HashMap<String,Object> workEfficiencyRanking();

    List<BtoDeviceList> DeviceInformation(String plantUid);
    List<BtoDeviceList> DeviceInformationSN(String plantUid);

    /**
     * 逆变器图标数据
     * @param plantUid
     * @return
     */
    List<TUBIAO> DeviceInformationTU(String plantUid);

    List<DeviceMonitorUListDTO> DeviceMonitorUlist();
    List<sheBeiJianCe> collectionMaxMin(String snName, String tableNme , String tabletime2);
    List<sheBeiJianCe> collectioAvg(String snName, String tableName ,String tableName2);
    List<sheBeiJianCe> Devicecurrent(String snName,String tableNme,String tableNme2);

    List<HashMap<String, Object>> EquipmentMonitoringplantUid( String plantId);

   List<sheBeiJianCe> DeviceVoltage(String snName, String tableName, String tableName2);

   List<sheBeiJianCe> DeviceFrequency(String snName, String tableName, String tableName2);

   List<sheBeiJianCe> Devicetemperature(String snName, String tableName, String tableName2);

   List<sheBeiJianCe> DeviceActivePower(String snName, String tableName, String tableName2);


    List<BtoDeviceList> getUserInverterNum(String plantId);

    /**
     * 逆变器数量及状态列表
     * @return
     */
    HashMap<String,Integer> DeviceNum();

   HashMap<String,Object> mergecollectionMaxMin(String snName, String tableName, String tableName2);

    /**
     * 查看逆变器详细信息
     * @param plantId
     * @return
     */
    Object deviceDetail(String plantId);

    /**
     * 查询单个电站总发电量和总功率(根据电站Uid)
     * @param plantUId
     * @param date
     * @return
     */
    List<HashMap<String,String>> totalEnergyAndPower(String plantId, String date);

    /**
     * 逆变器列表数据
     * @return
     */
    PageInfo<DeviceListDTO> deviceList(DeviceListForm deviceListForm);
}

