package entityDTO;

import lombok.Data;

import java.io.Serializable;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/11/18 15:38
 */
@Data
public class mergecollectionMaxMinDTO implements Serializable {

    private String iac1;
    private String iac2;
    private String iac3;

    private String vac1;
    private String vac2;
    private String vac3;

    private String time;

    private String ActivePower;

    private String Fac;

    private String Temperature;
}
