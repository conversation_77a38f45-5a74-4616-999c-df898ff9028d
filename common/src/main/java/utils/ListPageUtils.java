package utils;

import entity.Carousel;
import entityDTO.AreaDeviceDTO;
import entityDTO.EfficiencyDTO;
import entityDTO.EfficiencyPageDTO;
import entityDTO.HYAstationList;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ListPageUtils implements Serializable {
    public static List<HYAstationList> pageBySubList(List list, int currentPage, int pagesize) {
        List<HYAstationList> subList = new ArrayList<>();
        try {
            int totalcount = list.size();
//            currentPage =currentPage+1;
            int pagecount = 0;
            int m = totalcount % pagesize;
            if (m > 0) {
                pagecount = totalcount / pagesize + 1;
            } else {
                pagecount = totalcount / pagesize;
            }
            if (m == 0) {
                subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
            } else {
                if (currentPage == pagecount) {
                    subList = list.subList((currentPage - 1) * pagesize, totalcount);
                } else {
                    subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
                }
            }
        }catch (Exception e){
        }
        return subList;
    }

    /**
     * 利用subList方法进行分页
     * @param list 分页数据
     * @param pagesize  页面大小
     * @param currentPage   当前页面
     */
    public static <T> List<T> pageBySubTList(List<T> list, int currentPage, int pagesize) {
        List<T> subList = new ArrayList<>();
        try {
            int totalcount = list.size();
//            currentPage =currentPage+1;
            int pagecount = 0;
            int m = totalcount % pagesize;
            if (m > 0) {
                pagecount = totalcount / pagesize + 1;
            } else {
                pagecount = totalcount / pagesize;
            }
            if (m == 0) {
                subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
            } else {
                if (currentPage == pagecount) {
                    subList = list.subList((currentPage - 1) * pagesize, totalcount);
                } else {
                    subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
                }
            }
        }catch (Exception e){
        }
        return subList;
    }



    public static EfficiencyPageDTO pageBySubListByEfficiencyDTO(List list, int currentPage, int pagesize) {
        EfficiencyPageDTO subList = new EfficiencyPageDTO();
        try {
            int totalcount = list.size();
//            currentPage =currentPage+1;
            int pagecount = 0;
            int m = totalcount % pagesize;
            if (m > 0) {
                pagecount = totalcount / pagesize + 1;
            } else {
                pagecount = totalcount / pagesize;
            }
            if (m == 0) {
                subList.setEfficiencyDTOList( list.subList((currentPage - 1) * pagesize, pagesize * (currentPage)));
                subList.setPage(currentPage);
                subList.setPageSize(pagesize);
                subList.setTotal(totalcount);
            } else {
                if (currentPage == pagecount) {
                    subList.setEfficiencyDTOList(list.subList((currentPage - 1) * pagesize, totalcount));
                    subList.setPage(currentPage);
                    subList.setPageSize(pagesize);
                    subList.setTotal(totalcount);
                } else {
                    subList.setEfficiencyDTOList(list.subList((currentPage - 1) * pagesize, pagesize * (currentPage)));
                    subList.setPage(currentPage);
                    subList.setPageSize(pagesize);
                    subList.setTotal(totalcount);
                }
            }
        }catch (Exception e){
        }
        return subList;
    }

    public static List<Carousel> pageBySubCarouselList(List list, int currentPage, int pagesize) {
        List<Carousel> subList = new ArrayList<>();
        try {
            int totalcount = list.size();
//            currentPage =currentPage+1;
            int pagecount = 0;
            int m = totalcount % pagesize;
            if (m > 0) {
                pagecount = totalcount / pagesize + 1;
            } else {
                pagecount = totalcount / pagesize;
            }
            if (m == 0) {
                subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
            } else {
                if (currentPage == pagecount) {
                    subList = list.subList((currentPage - 1) * pagesize, totalcount);
                } else {
                    subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
                }
            }
        }catch (Exception e){
        }
        return subList;
    }

    public static List<List<AreaDeviceDTO>> pageBySubAreaDeviceList(List list, int currentPage, int pagesize) {
        List<List<AreaDeviceDTO>> subList = new ArrayList<>();
        try {
            int totalcount = list.size();
//            currentPage =currentPage+1;
            int pagecount = 0;
            int m = totalcount % pagesize;
            if (m > 0) {
                pagecount = totalcount / pagesize + 1;
            } else {
                pagecount = totalcount / pagesize;
            }
            if (m == 0) {
                subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
            } else {
                if (currentPage == pagecount) {
                    subList = list.subList((currentPage - 1) * pagesize, totalcount);
                } else {
                    subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
                }
            }
        }catch (Exception e){
        }
        return subList;
    }
}
