package com.botong.controller;

import com.botong.services.mapper.EfficiencyMapper;
import com.botong.services.service.BtoDeviceMapDTOPlusService;
import com.botong.services.service.EfficiencyService;
import com.botong.services.service.VBaseDeviceDTOService;
import entityDTO.VBaseDeviceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("/BtoDeviceMapDTOPlus")
@Api(tags = "统计报表")
@CrossOrigin
public class BtoDeviceMapDTOPlusController {

    @Autowired
    private BtoDeviceMapDTOPlusService btoDeviceMapDTOPlusService;

    @Autowired
    private VBaseDeviceDTOService vBaseDeviceDTOService;

    @Autowired
    private EfficiencyService efficiencyService;

    @ApiOperation("发电统计——查询日数据")
    @GetMapping("/selectAllDay")
    public AjaxResult selectAll(String sn, String day , Integer page,Integer pageSize){
        return AjaxResult.success("查询成功", btoDeviceMapDTOPlusService.SelectBtoDeviceMapDTOPlus(sn,day,page,pageSize));
    }

    @ApiOperation("发电统计——查询月数据")
    @GetMapping("/selectAllMonth")
    public AjaxResult selectAllMonth(String plantId, String month , Integer page,Integer pageSize){
        return AjaxResult.success("查询成功", btoDeviceMapDTOPlusService.SelectBtoDeviceMapDTOPlusMonth(plantId,month,page,pageSize));
    }

    @ApiOperation("发电统计——查询年数据")
    @GetMapping("/selectAllYear")
    public AjaxResult selectAllYear(String plantId, String year , Integer page,Integer pageSize){
        return AjaxResult.success("查询成功", btoDeviceMapDTOPlusService.SelectBtoDeviceMapDTOPlusYear(plantId,year,page,pageSize));
    }


    @ApiOperation("区域统计——查询天数据")
    @GetMapping("/selectDay")
    public AjaxResult selectDay(String time, String state, String city, String address ,Integer page,Integer pageSize){
        return AjaxResult.success("查询成功",vBaseDeviceDTOService.selectDay(time,state,city,address,page,pageSize));
    }

    @ApiOperation("区域统计——查询月数据")
    @GetMapping("/selectMonth")
    public AjaxResult selectMonth(String month, String state, String city, String address ,Integer page,Integer pageSize){
        return AjaxResult.success("查询成功",vBaseDeviceDTOService.selectMonth(month,state,city,address,page,pageSize));
    }

    @ApiOperation("区域统计——查询年数据")
    @GetMapping("/selectYear")
    public AjaxResult selectYear(String year, String state, String city, String address ,Integer page,Integer pageSize){
        return AjaxResult.success("查询成功",vBaseDeviceDTOService.selectYear(year,state,city,address,page,pageSize));
    }

    @ApiOperation("发电效率——月发电量-发电效率-环比-同比数据")
    @GetMapping("/EfficiencyDTOSelectMonth")
    public AjaxResult EfficiencyDTOSelectMonth(String plantId,String month,Integer page,Integer pageSize){
        return AjaxResult.success("查询成功",efficiencyService.PowerGenerationMonth(plantId,month,page,pageSize));
    }

    @ApiOperation("发电效率——年发电量-发电效率-环比-同比数据")
    @GetMapping("/EfficiencyDTOSelectYear")
    public AjaxResult EfficiencyDTOSelectYear(String plantId,String year,Integer page,Integer pageSize){
        return AjaxResult.success("查询成功",efficiencyService.PowerGenerationYear(plantId,year,page,pageSize));
    }
}
