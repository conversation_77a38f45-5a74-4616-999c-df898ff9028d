server:
  port: 8804
spring:
  application:
    name: auth
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *****************************************************************************************************************************************************************************
    #url: *************************************************************************************************************************************************************************
    username: user
    password: 1234
    druid:
      initialSize: 10
      minIdle: 10
      maxActive: 30
      maxWait: 50000
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    timeout: 5000
    password: botongredis666

mybatis:
  mapper-locations: classpath:mapper/*.xml