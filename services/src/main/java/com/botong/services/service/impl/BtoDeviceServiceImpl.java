package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.botong.services.mapper.BtoDeviceMapper;
import com.botong.services.service.BtoDeviceListService;
import com.botong.services.service.BtoDeviceService;
import com.botong.services.service.BtoStationBaseService;
import com.botong.services.service.BtoStationListService;
import com.botong.services.vo.ChartVo;
import com.botong.services.vo.DayPowerVo;
import entity.BtoDevice;
import entity.BtoDeviceList;
import entity.BtoStationBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vo.DateElectricityVo;
import vo.ElectricityStatisticsVo;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 逆变器数据 impl
 *
 * <AUTHOR> @date 2022-08-16 10:10:10
 */
@Service
public class BtoDeviceServiceImpl extends ServiceImpl<BtoDeviceMapper, BtoDevice> implements BtoDeviceService {

    @Autowired
    private BtoDeviceListService btoDeviceListService;

    @Autowired
    private BtoStationListService btoStationListService;

    @Autowired
    private BtoStationBaseService btoStationBaseService;

    @Autowired
    private BtoDeviceService btoDeviceService;


    @Override
    @Cacheable(cacheNames = "inverterElectricityInfo" ,key = " 'inverterElectricityInfo' " )//或者这样写key = "#id"
    public List<ElectricityStatisticsVo> inverterElectricityInfo() {
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        tableName += format.format(date);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time = dateFormat.format(date);
        time += "%";
        return baseMapper.inverterElectricityInfo(tableName, time);
    }

    @Override
    public List<String> getUserByInverterMoreOne() {
        return baseMapper.getUserByInverterMoreOne();
    }

    @Override
    public List<String> getUserByInverterEqualOne() {
        return baseMapper.getUserByInverterEqualOne();
    }

    @Override
    public HashMap<String, Object> getUserComplexInfo(String plantId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        List<BtoDeviceList> userInverterNum = btoDeviceListService.getUserInverterNum(plantId);
        int normalStatus = 0;
        int alertNum = 0;
        float currentPower = 0;
        double allElectrical = 0;
        ArrayList<String> sns = new ArrayList<>();
        for (BtoDeviceList btoDeviceList : userInverterNum) {
            if ("1".equals(btoDeviceList.getDeviceStatus())) {
                normalStatus++;
            }
            if ("2".equals(btoDeviceList.getDeviceStatus())) {
                alertNum++;
            }
            sns.add(btoDeviceList.getDataloggerSn());
        }
        //逆变器正常率
        hashMap.put("normalRate", (normalStatus / userInverterNum.size()) * 100);
        //逆变器数量
        hashMap.put("inverterNum", userInverterNum.size());
        //告警数量
        hashMap.put("alertNum", alertNum);
        //装机容量
        hashMap.put("installedCapacity", btoStationListService.getInstalledCapacityById(plantId));
        //当前功率、总发电量
        for (String sn : sns) {
            currentPower += getCurrentPowerBySn(sn);
            allElectrical += btoDeviceService.getDayMonthYearAllInfo(sn).getTotalEnergy();
        }
        hashMap.put("currentPower", currentPower);
        hashMap.put("allElectrical", allElectrical);
        hashMap.put("sns", sns);
        //经纬度
        BtoStationBase latitudeAndLongitude = btoStationBaseService.getLatitudeAndLongitude(plantId);
        hashMap.put("longitude", latitudeAndLongitude.getLongitude());
        hashMap.put("latitude", latitudeAndLongitude.getLatitude());
        //电站uid
        hashMap.put("plantUid", btoStationListService.getIdByUId(plantId));
        return hashMap;
    }

    @Override
    public float getCurrentPowerBySn(String sn) {
        QueryWrapper<BtoDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("datalogger_sn", sn);
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String tableName = "bto_device_" + dateFormat.format(date);
        return baseMapper.getCurrentPowerBySn(tableName, sn);
    }

    @Override
    public DateElectricityVo getDayMonthYearAllInfo(String dataloggerSn) {
        QueryWrapper<BtoDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("today_energy", "month_energy", "year_energy", "total_energy");
        queryWrapper.eq("datalogger_sn", dataloggerSn);
        queryWrapper.orderByDesc("update_time");

        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        tableName += dateFormat.format(date);

        BtoDevice btoDevice = baseMapper.getDayMonthYearAllInfo(tableName, dataloggerSn);

        DateElectricityVo dateElectricityVo = new DateElectricityVo();
        dateElectricityVo.setMonthEnergy(btoDevice.getMonthEnergy());
        dateElectricityVo.setYearEnergy(btoDevice.getYearEnergy());
        dateElectricityVo.setTodayEnergy(btoDevice.getTodayEnergy());
        dateElectricityVo.setTotalEnergy(btoDevice.getTotalEnergy());
        return dateElectricityVo;
    }

    @Override
    public HashMap<String, Object> getDayPowerBySn(String sn, String time,String tableName) {
        String tableName1 = "bto_device_"+tableName;
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
//        tableName += dateFormat.format(date);
        time += "%";
        List<DayPowerVo> dayPowerBySn = baseMapper.getDayPowerBySn(sn, tableName1, time);

        ArrayList<String> dates = new ArrayList<>();
        ArrayList<String> powers = new ArrayList<>();
        for (DayPowerVo btoDevice : dayPowerBySn) {
            dates.add(btoDevice.getDateTime());
            powers.add(btoDevice.getPower());
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("dates", dates);
        hashMap.put("powers", powers);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> getMonthElectricitySn(String plantId, String time) {
        HashMap<String, Object> hashMap = new HashMap<>();
        ArrayList<String> dates = new ArrayList<>();
        ArrayList<String> energys = new ArrayList<>();
        for (ChartVo chartVo : baseMapper.getMonthElectricitySn(plantId, time)) {
            dates.add(chartVo.getDate());
            energys.add(chartVo.getEnergy());
        }
        hashMap.put("dates", dates);
        hashMap.put("energys", energys);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> getYearElectricitySn(String plantId, String time) {
        HashMap<String, Object> hashMap = new HashMap<>();
        ArrayList<String> dates = new ArrayList<>();
        ArrayList<String> energys = new ArrayList<>();
        for (ChartVo chartVo : baseMapper.getYearElectricitySn(plantId, time)) {
            dates.add(chartVo.getDate());
            energys.add(chartVo.getEnergy());
        }
        hashMap.put("dates", dates);
        hashMap.put("energys", energys);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> getAllElectricitySn(String deviceSns, String time) {
        HashMap<String, Object> hashMap = new HashMap<>();
        ArrayList<String> energys = new ArrayList<>();
        String[] split = deviceSns.split(",");
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        tableName += dateFormat.format(date);
        for (String s : split) {
            for (ChartVo chartVo : baseMapper.getAllElectricitySn(s, time += "%", tableName)) {
                hashMap.put(s, chartVo.getEnergy());
            }
        }
        return hashMap;
    }

    @Override
    public List<BtoDevice> getElectricityBySn(String sn) {
        HashMap<String, Object> hashMap = new HashMap<>();
        String tableName = "bto_device_";
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        tableName += dateFormat.format(date);
        String time = format.format(date);
        return baseMapper.getElectricityBySn(sn, tableName, time += "%");
    }

    @Override
    public BtoDevice getInverterDetails(String sn, String tableName, String time) {
        return baseMapper.getInverterDetails(sn, tableName, time);
    }

    @Override
    public int registeredUser(String cUserName, String cUsertel, String cUserEmail,String engnierId,Integer special,String password) {
        String uuid = UUID.randomUUID().toString().toUpperCase();//用户id随机生成
        Date date=new Date();
        SimpleDateFormat dateFormat2=new SimpleDateFormat("yyyy-MM-dd");
        String regtiTime= dateFormat2.format(date);//注册时间
        if (baseMapper.registeredUser(uuid,cUserName,cUsertel,cUserEmail,regtiTime,engnierId,special,password)==1){
            return 1;
        }else {
            return 0;
        }
    }

}
