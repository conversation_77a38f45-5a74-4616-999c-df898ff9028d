package com.botong.services.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import entityDTO.BtoDeviceMapDTO;
import vo.PowerStatisticVo;

import java.util.HashMap;
import java.util.List;

public interface HYBtoDeviceMapDTOPlusService extends IService<BtoDeviceMapDTO> {
//    PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlus(PowerStatisticVo powerStatisticVo);
//
//    PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusMonth(PowerStatisticVo powerStatisticVo);
//
//    PageInfo<List<BtoDeviceMapDTO>> SelectBtoDeviceMapDTOPlusYear(PowerStatisticVo powerStatisticVo);

    List<HashMap<String, Object>>  selectPowerStatistic(PowerStatisticVo powerStatisticVo);
}
