<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.BtoStationDayMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.BtoStationDayDTO" id="btoStationDayMap">
        <result property="plantId" column="plant_id"/>
        <result property="date" column="date"/>
        <result property="energy" column="energy"/>
        <result property="updateDay" column="update_day"/>
        <result property="power" column="power"/>
        <result property="time" column="time"/>
    </resultMap>

    <select id="StationMothEnergy" resultType="entity.BtoStationDay">
        select
            date,sum(energy) energy
        from v_day
        WHERE date like '${jie}'
        group by date;
    </select>
    <select id="powerStationDay" resultType="entityDTO.BtoStationDayDTO">
        SELECT
            sum( power ) power,
            update_time time
        FROM
            `bto_device_total`
        WHERE
            special = '1'
          AND LEFT ( update_time, 15 )= '${time}';
    </select>


</mapper>