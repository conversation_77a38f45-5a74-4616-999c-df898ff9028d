package entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class OperatorDetails implements Serializable {
    /**
     * 电站名
     */
    private String stationName;
    /**
     * 电站id
     */
    private String plantId;
    /**
     * 电站Uid
     */
    private String plantUid;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 运维器IMEI码
     */
    private String imei;
    /**
     * 运维器IccId
     */
    private String iccId;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 逆变器SN
     */
    private String SN;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 装机容量
     */
    private String peakPower;
    /**
     * 逆变器SN
     */
    private String deviceSn;

}
