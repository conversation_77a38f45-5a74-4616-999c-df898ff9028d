<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYAlarmAnalysisMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="entityDTO.AlarmAnalysis" id="AlarmAnalysisMap">
        <result property="sn" column="sn"/>
        <result property="startTime" column="startTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="alarmMessage" column="alarmMessage"/>
        <result property="mean" column="mean"/>
        <result property="STATUS" column="STATUS"/>
        <result property="plantId" column="plantId"/>
        <result property="plantUid" column="plantUid"/>
        <result property="name1" column="name1"/>
        <result property="NAME" column="NAME"/>
        <result property="tel" column="tel"/>
        <result property="grade" column="grade"/>
        <result property="NUM" column="NUM"/>
        <result property="q" column="q"/>
        <result property="yaoxin" column="yaoxin"/>
        <result property="nb" column="nb"/>
        <result property="name" column="name"/>
        <result property="city" column="city"/>
        <result property="planId" column="planId"/>
        <result property="wisdomdevicesn" column="wisdomdevicesn"/>
        <result property="pvele" column="pvele"/>
        <result property="useele" column="useele"/>
        <result property="selfele" column="selfele"/>
        <result property="sellele" column="sellele"/>
        <result property="buyele" column="buyele"/>
        <result property="time" column="time"/>
        <result property="mingzi" column="mingzi"/>
        <result property="createdate" column="createdate"/>
    </resultMap>

    <resultMap type="entityDTO.OperatorEvents" id="OperatorEventsMap">
        <result property="name" column="name"/>
        <result property="ownerPhone" column="ownerPhone"/>
        <result property="address" column="address"/>
        <result property="plantUid" column="plantUid"/>
        <result property="wisdomDevicesn" column="wisdomDevicesn"/>
        <result property="alarmname" column="alarmname"/>
        <result property="alarmtime" column="alarmtime"/>
        <result property="apointname" column="apointname"/>
        <result property="bpointname" column="nambpointnamee"/>
        <result property="cpointname" column="cpointname"/>
        <result property="apointvoltage" column="apointvoltage"/>
        <result property="bpointvoltage" column="bpointvoltage"/>
        <result property="cpointvoltage" column="cpointvoltage"/>
        <result property="dpointvoltage" column="dpointvoltage"/>
        <result property="endtime" column="endtime"/>
        <result property="plantUid" column="plantUid"/>
    </resultMap>
    <select id="selectListt" resultMap="AlarmAnalysisMap">
        SELECT a.sn          sn,
               start_time    startTime,
               update_time   updateTime,
               alarm_message alarmMessage,
               mean          mean,
               STATUS        STATUS,
               plant_id      plantId,
               plant_uid     plantUid,
               name1         name1,
               NAME          NAME,
               tel           tel,
               grade         grade,
               CASE
                   WHEN STATUS = 0 THEN '未处理'
                   WHEN STATUS = 1 THEN '已处理'
                   WHEN STATUS = 2 THEN '失效'
                   END       state #状态
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '%${time}%'
    </select>
    <select id="AlarmAnalysisTimeIntervalQuery" resultMap="AlarmAnalysisMap">
        SELECT
        a.sn sn,
        start_time startTime,
        update_time updateTime,
        alarm_message alarmMessage,
        mean mean,
        STATUS STATUS,
        plant_id plantId,
        plant_uid plantUid,
        name1 name1,
        NAME NAME,
        tel tel,
        CASE
        WHEN STATUS = 0 THEN'未处理'
        WHEN STATUS = 1 THEN'已处理'
        WHEN STATUS = 2 THEN'失效'
        END state
        FROM
        v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        start_time BETWEEN '${time1}' AND '${time2}'
        and
        <foreach collection="plantUid" item="plantUid" separator="or">
            plant_uid='${plantUid}'
        </foreach>
    </select>
    <select id="AlarmAnalysisTconditionQuery" resultMap="AlarmAnalysisMap">
        SELECT a.sn          sn,
               start_time    startTime,
               update_time   updateTime,
               alarm_message alarmMessage,
               mean          mean,
               STATUS        STATUS,
               plant_id      plantId,
               plant_uid     plantUid,
               name1         name1,
               NAME          NAME,
               tel           tel,
               CASE
                   WHEN STATUS = 0 THEN '未处理'
                   WHEN STATUS = 1 THEN '已处理'
                   WHEN STATUS = 2 THEN '失效'
                   END       state #状态
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '%${startTime}%'
           OR name LIKE '#{name}'
           OR a.sn LIKE '#{sn}'
           OR name1 LIKE '#{name1}'
    </select>
    <select id="AlarmAnalysisPolymerization" resultMap="AlarmAnalysisMap">
        SELECT
        plantUid,
        name1,
        sn,
        mean,
        q,
        startTime,
        grade,
        NUM
        FROM(
        SELECT
        plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        substr('${startTime}',1,10) q,
        start_time startTime,
        grade grade,
        count(*) NUM
        FROM
        v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        GROUP BY grade,q,mean,a.sn,name1
        ) w WHERE
        <foreach collection="plantUid" item="plantUid" separator="or">
            plantUid='${plantUid}'
        </foreach>

    </select>
    <select id="AlarmAnalysisPolymerizationAll" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid                     plantUid,
               name1                         name1,
               a.sn                          sn,
               mean                          mean,
               substr('${startTime}', 1, 10) q,
               start_time                    startTime,
               grade                         grade,
               count(*)                      NUM
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time BETWEEN '${endTime}' AND '${startTime}'
        GROUP BY grade, q, mean, a.sn, name1
    </select>
    <select id="AlarmAnalysisHistoryAlarm" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        start_time BETWEEN '${endTime}' AND '${startTime}'
        <if test="grade!= null and grade != '' ">
            and grade=${grade}
        </if>
    </select>
    <!--    <select id="AlarmAnalysisListFormplantUid" resultType="entityDTO.AlarmAnalysis">-->
    <!--        SELECT a.sn          sn,-->
    <!--               start_time    startTime,-->
    <!--               update_time   updateTime,-->
    <!--               alarm_message alarmMessage,-->
    <!--               mean          mean,-->
    <!--               STATUS        STATUS,-->
    <!--               plant_id      plantId,-->
    <!--               plant_uid     plantUid,-->
    <!--               name1         name1,-->
    <!--               NAME          NAME,-->
    <!--               tel           tel-->
    <!--        FROM v_event_mean a-->
    <!--                 LEFT JOIN v1_station_device b ON a.sn = b.sn-->
    <!--        WHERE alarm_message NOT LIKE 'PV%exception'-->
    <!--          AND plant_uid = #{plantUid}-->
    <!--    </select>-->
    <select id="AlarmAnalysisHistoryAlarmAll" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        <if test="endTime != null and endTime != '' ">
            start_time BETWEEN '${endTime}' AND '${startTime}'
        </if>
        <if test="plantUid != null and plantUid != '' ">
            AND plantUid ='#{plantUid}'
        </if>
        <if test="grade != null and grade != '' ">
            AND grade=${grade}
        </if>
    </select>
    <select id="AlarmAnalysisTimeIntervalAll" resultMap="AlarmAnalysisMap">
        SELECT a.sn          sn,
               start_time    startTime,
               update_time   updateTime,
               alarm_message alarmMessage,
               mean          mean,
               STATUS        STATUS,
               plant_id      plantId,
               plant_uid     plantUid,
               name1         name1,
               NAME          NAME,
               tel           tel,
               CASE
                   WHEN STATUS = 0 THEN '未处理'
                   WHEN STATUS = 1 THEN '已处理'
                   WHEN STATUS = 2 THEN '失效'
                   END       state #状态
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time BETWEEN '${time1}' AND '${time2}'
    </select>
    <select id="AlarmAnalysisHistoryAlarmAll3" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        name name,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        <if test="(name1 != null and name1 != '') or (grade != null and grade != '') or (startTime != null and startTime != '')">
            where
        </if>
        <if test="startTime != null and startTime != ''">
            start_time between '${startTime}' and '${endTime}'
        </if>
        <if test="grade != null and grade != ''">
            <if test="startTime != null and startTime !=''">and</if>
            grade like #{grade}
        </if>
        <if test="name1 != null and name1 != ''">
            <if test="(startTime != null and startTime !='') or (grade != null and grade !='')">and</if>
            name1 like CONCAT ('%',#{name1},'%')
        </if>

    </select>
    <select id="AlarmAnalysisHistoryAlarmQuann" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid  plantUid,
               name1      name1,
               a.sn       sn,
               mean       mean,
               start_time startTime,
               grade      grade
        FROM (
                 select sn, alarm_code, alarm_message, start_time, `status`, mean, grade
                 from v_event_r
                 union
                 select sn, alarm_code, alarm_message, start_time, `status`, mean, grade
                 from v_event_h) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
    </select>
    <select id="AlarmAnalysisPolymerizationplantUid" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid                     plantUid,
               name1                         name1,
               a.sn                          sn,
               mean                          mean,
               substr('${startTime}', 1, 10) q,
               start_time                    startTime,
               grade                         grade,
               count(*)                      NUM
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time BETWEEN '${endTime}' AND '${startTime}'
          and plant_uid = '${plantUid}'
        GROUP BY grade, q, mean, a.sn, name1
    </select>
    <select id="totalAlarmStatistics" resultType="java.lang.Object">
        SELECT count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
    </select>
    <select id="totalAlarmStatisticsTime" resultType="java.lang.Object">
        SELECT count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
    </select>
    <select id="totalAlarmStatisticsTIME" resultType="entityDTO.AlarmAnalysis">
        SELECT count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plantUid}'
    </select>
    <select id="totalAlarmStatisticsTIMETU" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid,
               name1,
               #a.sn,
               mean,
               substr(start_time, 1, 10) q,
               #start_time,
               #grade,
               count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'

    </select>
    <select id="totalAlarmStatisticsTIMEgeren" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid,
               name1,
               #a.sn,
               mean,
               substr(start_time, 1, 10) q,
               #start_time,
               #grade,
               count(*)
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'

          AND plant_uid = '${plantUid}'

        GROUP BY q
    </select>
    <select id="totalAlarmStatisticsTIMEgerenyaoxin" resultType="entityDTO.AlarmAnalysis">
        SELECT count(distinct (mean)) yaoxin
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
    </select>
    <select id="totalAlarmStatisticsTIMEgerennyaoxin" resultType="entityDTO.AlarmAnalysis">
        SELECT count(distinct (mean)) yaoxin
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plantUid}'
    </select>
    <select id="totalAlarmStatisticsTIMEstatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(*)                 nb,
               substr(start_time, 1, 7) q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${tiem}%'
        GROUP BY q
    </select>
    <select id="totalAlarmStatisticsTIMEgerennstatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(*)                 nb,
               substr(start_time, 1, 7) q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${tiem}%'
          AND plant_uid = '${plantUid}'
        GROUP BY q
    </select>
    <select id="totalAlarmStatisticsTIMEEquipmentStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(a.sn) q,
               a.sn
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
        GROUP BY a.sn;

    </select>
    <select id="totalAlarmStatisticsTIMEgerenEquipmentStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(a.sn) q,
               a.sn
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'

          AND plant_uid = '${plantUid}'
        GROUP BY a.sn;
    </select>
    <select id="totalAlarmStatisticsTIMELevelStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(grade),
               grade
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
        GROUP BY grade;
    </select>
    <select id="totalAlarmStatisticsTIMEgerennLevelStatistics" resultType="entityDTO.AlarmAnalysis">
        SELECT count(grade),
               grade
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plantUid}'
        GROUP BY grade;
    </select>
    <select id="totalAlarmStatisticsTIMEgerenlist" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid plantUid,
               name1     name1,
               a.sn      sn,
               count(*)  q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
          AND plant_uid = '${plnatUid}'
        GROUP BY a.sn;
    </select>
    <select id="totalAlarmStatisticsTIMEyaoxinlistt" resultType="entityDTO.AlarmAnalysis">
        SELECT plant_uid plantUid,
               name1     name1,
               a.sn      sn,
               count(*)  q
        FROM (
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_r
                 UNION
                 SELECT sn,
                        alarm_code,
                        alarm_message,
                        start_time,
                        `status`,
                        mean,
                        grade
                 FROM v_event_h
             ) a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE start_time LIKE '${time}%'
        GROUP BY a.sn;
    </select>
    <select id="OperatorEventData" resultMap="OperatorEventsMap">
        SELECT
        b.name name,
        b.owner_phone ownerPhone,
        b.address1 address,
        a.wisdom_device_sn wisdomdevicesn,
        a.alarm_name alarmname,
        a.alarm_time alarmtime,
        a.end_time endtime,
        a.a_point_name apointname,
        a.b_point_name bpointname,
        a.c_point_name cpointname,
        a.a_point_voltage apointvoltage,
        a.b_point_voltage bpointvoltage,
        a.c_point_voltage cpointvoltage,
        a.d_point_voltage dpointvoltage,
        a.plant_uid plantUid
        FROM
        bto_operation_fault a
        LEFT JOIN bto_station_list b ON a.wisdom_device_sn = b.wisdom_device_sn
        WHERE b.special = '1'
        <if test="alarmTime !=null and alarmTime != ''">
            AND left(alarm_time,10) between #{alarmTime} and #{endTime}
        </if>
        <if test="address !=null and address != ''">
            AND
            b.address1 LIKE concat('%',#{address},'%')
        </if>
        ORDER BY a.alarm_time DESC
    </select>
    <select id="AlarmAnalysisHistoryAlarmAll2" resultType="entityDTO.AlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 name1,
        name name,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        where
        <if test="name1 !=null and name1 != ''">
            name1 like CONCAT ('%',#{name1},'%')
        </if>
        <if test="grade !=null and grade != ''">

            <if test="name1 !=null and name1 != ''">
                and
            </if>
            grade like #{grade}
        </if>
        <if test="startTime !=null and startTime != ''">
            start_time between '${startTime}' and '${endTime}'
        </if>
    </select>
    <select id="OperatorData" resultType="entityDTO.HYAlarmAnalysis">
        SELECT
        a.`name` stationName,
        a. create_date createDate,
        a.address1 address,
        a.plant_id plantId,
        a.plant_uid plantUid,
        a.wisdom_device_sn IMEI,
        c.operation_SN sn,
        c.4G_SN SN4G,
        CASE WHEN b.pv_tel IS NULL THEN 0
        ELSE b.pv_tel END  pvele,
        CASE WHEN b.time IS NULL THEN NOW()
        ELSE b.time END time
        FROM
        bto_station_list a
        LEFT JOIN
        (
        SELECT plant_uid,pv_tel,time FROM
        bto_operation_day
        WHERE time BETWEEN #{startTime} AND #{endTime}
        )b
        ON a.plant_uid = b.plant_uid
        LEFT JOIN bto_operation_message c
        ON a.wisdom_device_sn=c.IMEI
        WHERE a.special = '1'
        <if test="name != null and name != ''">
        AND a.name LIKE CONCAT ('%',#{name},'%')
        </if>
        GROUP BY a.plant_id,b.time
        ORDER BY time
    </select>
    <select id="OperatorDataMonth" resultType="entityDTO.HYAlarmAnalysis">
        SELECT
        stationName,
        createdate createDate,
        address,
        plantId,
        plantUid,
        wisdomdevicesn sn,
        SUM(pvele) pvele,
        LEFT ( time, 7 ) time
        FROM(
        SELECT
        a.`name` stationName,
        create_date createdate,
        a.address1 address,
        a.plant_id plantId,
        a.plant_uid plantUid,
        a.wisdom_device_sn wisdomdevicesn,
        b.pv_tel pvele,
        b.time time
        FROM
        v_wisdom_sn a
        LEFT JOIN bto_operation_day b ON a.wisdom_device_sn = b.wisdom_device_sn
        WHERE a.special = '1'
        <if test="name != null and name != ''">
            AND a.name LIKE CONCAT ('%',#{name},'%')
        </if>
        AND b.time BETWEEN '${startTime}' AND '${endTime}'
        GROUP BY
        a.plant_id,b.time
        ORDER BY
        b.time)qq GROUP BY plantId,time
    </select>

    <select id="AlarmByConditionOnListQuery" resultType="entityDTO.HYAlarmAnalysis">
        SELECT a.sn sn,
        start_time startTime,
        update_time updateTime,
        alarm_message alarmMessage,
        mean mean,
        STATUS STATUS,
        plant_id plantId,
        plant_uid plantUid,
        name1 stationName,
        NAME NAME,
        tel tel,
        grade grade
        FROM v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE special = '1'
        <if test="type == 2">
        AND start_time &lt;= DATE_SUB( end_time, INTERVAL 10 MINUTE )
        </if>
        AND alarm_message
        <if test="type == 0">
            NOT
        </if>
        LIKE 'PV%exception'
        AND start_time BETWEEN #{startTime} AND #{endTime}
        <if test="alarmMessage!=null and alarmMessage!='' ">
            AND alarm_message = #{alarmMessage}
        </if>
        <if test="mean!=null and mean!='' ">
            AND mean = #{mean}
        </if>
        <if test="grade!=null and grade!='' ">
            AND grade = #{grade}
        </if>
        <if test="status!=null and status!='' ">
            AND status = #{status}
        </if>
        <if test="plantUidArray.length>0 and plantUidArray!=null">
            AND plant_uid IN
            <foreach collection="plantUidArray" item="plantUid" open="(" separator="," close=")">
                #{plantUid}
            </foreach>
        </if>
        ORDER BY status ASC,startTime DESC
    </select>
    <select id="AlarmByConditionOnAggregation" resultType="entityDTO.HYAlarmAnalysis">
        SELECT plant_uid plantUid,
        name1 stationName,
        a.sn sn,
        mean mean,
        start_time startTime,
        grade grade,
        status,
        alarm_message alarmMessage
        FROM v_event_mean a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE special = '1'
        AND alarm_message NOT LIKE 'PV%exception'
        AND start_time BETWEEN #{startTime} AND #{endTime}
        <if test="plantUidArray.length>0 and plantUidArray!=null">
            AND plant_uid IN
            <foreach collection="plantUidArray" item="plantUid" open="(" separator="," close=")">
                #{plantUid}
            </foreach>
        </if>
    </select>
    <select id="AlarmHistoryByConditionOnListQuery" resultType="entityDTO.HYAlarmAnalysis">
        SELECT
        plant_uid plantUid,
        name1 stationName,
        a.sn sn,
        mean mean,
        start_time startTime,
        update_time updateTime,
        `status` status,
        grade grade
        FROM
        (
        select sn,alarm_code,alarm_message,start_time,update_time,`status`,mean,grade from v_event_r
        union select sn,alarm_code,alarm_message,start_time,update_time,`status`,mean,grade from v_event_h)
        a
        LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE
        b.special='1'
        AND alarm_message NOT LIKE 'PV%exception'
        <if test="endTime != null and endTime != '' and startTime != null and startTime != '' ">
            AND start_time BETWEEN '${startTime}' AND '${endTime}'
        </if>
        <if test="grade != null and grade != '' ">
            AND grade=${grade}
        </if>
        <if test="stationName != null and stationName != '' ">
            AND name1 like concat('%',#{stationName},'%')
        </if>
        ORDER BY startTime DESC
    </select>
    <select id="stationAlarmInfoByPlantUid" resultType="entity.HyStationAlarmInfo">
        SELECT a.sn            machineType,
               a.start_time    startTime,
               a.update_time   updateTime,
               a.alarm_message alarmMessage,
               a.mean          mean,
               a.STATUS        status,
               b.plant_id      plantId,
               b.plant_uid     plantUid,
               b.name1         stationName,
               b.NAME          userName
        FROM v_event_mean a
                 LEFT JOIN v1_station_device b ON a.sn = b.sn
        WHERE alarm_message NOT LIKE 'PV%exception'
          AND plant_uid = #{plantUid}
        UNION
        (
            SELECT '运维器'        machineType,
                   a.alarm_time startTime,
                   a.end_time   updateTime,
                   NULL         alarmMessage,
                   a.alarm_name mean,
                   CASE
                       WHEN end_time > DATE_SUB(now(), INTERVAL 10 MINUTE) THEN
                           0
                       ELSE 1
                       END      status,
                   b.plant_id   plantId,
                   b.plant_uid  plantUid,
                   b.name1      stationName,
                   b.NAME       userName
            FROM bto_operation_fault a
                     LEFT JOIN v1_station_device b ON a.plant_uid = b.plant_uid
            WHERE a.plant_uid = #{plantUid}
        )
        ORDER BY updateTime DESC
    </select>
    <!--处理告警的SQL-->
    <update id="alarmHandler" parameterType="entityDTO.AlarmHandlerDTO">
        UPDATE bto_device_event
        SET `status`='1',
            update_time = now(),
            end_time = now()
        where sn = #{sn}
          and `status` = '0'
          and alarm_message = #{alarmMessage}
          and start_time = #{startTime}
    </update>
    <update id="alarmHandlerByList" parameterType="entityDTO.AlarmHandlerDTO">
        UPDATE bto_station_list
        SET `name`=#{remarks},
            update_time = now()
        where `name` = #{stationName}
    </update>
    <update id="alarmHandlerByBase" parameterType="entityDTO.AlarmHandlerDTO">
        UPDATE bto_station_base
        SET `name`=#{remarks},
            update_time = now()
        where `name` = #{stationName}
    </update>
    <select id="getCurDayAlarmNum" resultType="java.util.HashMap">
        SELECT
            count(*) curAlarmNum
        FROM
            bto_device_event a
                LEFT JOIN bto_device_list b ON a.sn = b.device_sn
        WHERE
            b.plant_id = #{plantId}
          AND LEFT ( a.start_time, 10 )= CURDATE()
    </select>


</mapper>