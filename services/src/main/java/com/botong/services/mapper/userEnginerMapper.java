package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.BtoDeviceTypeDTO;
import entityDTO.btoProjectSign;
import entityDTO.userEnginerDTO;
import entityDTO.userListLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface userEnginerMapper extends BaseMapper<userEnginerDTO> {
    int registeredUser(@Param("uuid") String uuid, @Param("cUserName") String cUserName,@Param("cUsertel")  String cUsertel, @Param("cUserEmail") String cUserEmail,@Param("regtiTime")  String regtiTime,@Param("engnierId") String engnierId,@Param("special")Integer special);


    List<userEnginerDTO> selectUid(String cUserName);

    List<btoProjectSign> QueryAreaUsers();

    int bindSmartOperator(@Param("test") String test,@Param("smartOperatorName") String smartOperatorName,@Param("num")Integer num);

    int updatePassword(@Param("userUid") String userUid,@Param("password") String password);

    List<userEnginerDTO> SelectcompanyId();

    List<userEnginerDTO> SelUserAll();

    List<userEnginerDTO> CheckUsername(@Param("userName") String userName);

    List<BtoDeviceTypeDTO> InverterModel();

    Integer QueryIMEIcode(@Param("imei") String imei);


    List<BtoDeviceTypeDTO> getUseroPhone(@Param("cUserPhone") String cUserPhone);
}
