package com.botong.services.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.UserListMapper;
import com.botong.services.service.BtoUserService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.BtoUserList;
import entityDTO.Resource;
import entityDTO.Role;
import entityDTO.roleresoure;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BtoUserServiceImpl extends ServiceImpl<UserListMapper, BtoUserList> implements BtoUserService {
    @Autowired
    private UserListMapper userListMapper;

    @Override
    public List<BtoUserList> test() {
        return baseMapper.selectList(null);
    }

    @Override
    public PageInfo<BtoUserList> BTOuserList(Integer page,Integer pagesize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pagesize));
        List<BtoUserList> stationLunBos = userListMapper.selectListt(page,pagesize);
        PageInfo<BtoUserList> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return  stationLunBoPageInfo;
    }

    @Override
    public PageInfo<BtoUserList> BTOuserListLike(String userName,String page,String pagesize) {
        PageHelper.startPage(Integer.valueOf(page), Integer.valueOf(pagesize));
        QueryWrapper<BtoUserList> btoUserListQueryWrapper=new QueryWrapper<>();
        btoUserListQueryWrapper.like("c_user_name",userName).or().like("c_user_tel",userName);
        List<BtoUserList> stationLunBos = baseMapper.selectList(btoUserListQueryWrapper);
        PageInfo<BtoUserList> stationLunBoPageInfo = new PageInfo<>(stationLunBos);
        return stationLunBoPageInfo;
    }

    @Override
    public Object UserRoleQuery(String userUid) {
        return userListMapper.UserRoleQuery(userUid);
    }

    @Override
    public List<roleresoure> getPermissionId(String userUid) {
        return userListMapper.getPermissionId(userUid);
    }

    @Override
    public List<roleresoure> getroleResourceId(int roleId) {
        return userListMapper.getroleResourceId(roleId);
    }

    @Override
    public List<Resource> listResource() {
        return userListMapper.listResource();
    }

    @Override
    public List<Role> listRole() {
        return userListMapper.listRole();
    }

    @Override
    public Object returnPrimaryId(String plantId, String plantUid) {
        if (plantId!=null && plantUid!=null){
            plantUid=null;
        }
        return userListMapper.returnPrimaryId(plantId,plantUid);
    }

    @Override
    public int updatePassword(String userUid, String password) {
        if (userListMapper.updatePassword(userUid,password)==1){
            return 1;
        }else {
            return 0;
        }
    }


}
