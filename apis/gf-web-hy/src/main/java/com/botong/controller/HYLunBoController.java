package com.botong.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

import java.util.HashMap;

@CrossOrigin
@RestController
@Api(tags = "光伏监控中心")
@RequestMapping("LunBoController")
public class HYLunBoController {
    @Autowired
    private com.botong.services.service.HYlunboService hYlunboService;

    @ApiOperation(value = " 站点轮播" , response = HashMap.class)
    @GetMapping("stationCarousel")
    public AjaxResult stationCarousel(Integer page,Integer pageSize){
        return AjaxResult.success("操作成功",hYlunboService.stationCarousel(page,pageSize));
    }

    @ApiOperation(value = "近6月&近7日发电量统计",response = HashMap.class)
    @GetMapping("sevenAndSixEnergy")
    public AjaxResult sevenAndSixEnergy(){
        return AjaxResult.success("操作成功",hYlunboService.sevenAndSixEnergy());
    }


//    @ApiOperation("近7日发电量统计")
//    @GetMapping("powerSevenEnergy")
//    public AjaxResult powerSevenEnergy(){
//        return AjaxResult.success("操作成功",hYlunboService.powerSevenEnergy());
//    }
//
//    @ApiOperation("近6月发电量统计")
//    @GetMapping("powerSIXEnergy")
//    public AjaxResult powerSIXEnergy(){
//        return AjaxResult.success("操作成功",hYlunboService.powerSIXEnergy());
//    }


}
