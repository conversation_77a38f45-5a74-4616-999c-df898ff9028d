<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.HYlunboMapper">

    <select id="stationCarousel" resultType="entity.Carousel">
        SELECT (@i := @i + 1)                   `stationNo`,
               q.`status`,
               q.stationName,
               q.createTime,
               q.peakPower,
               ((q.totalEnergy * 0.997) / 1000) co2,
               q.realTimePower,
               q.todayEnergy,
               q.totalEnergy,
               q.updateTime,
               q.city
        FROM (
                 SELECT a.status1                      `status`,
                        a.`name1`                      stationName,
                        SUM(DISTINCT (a.peak_power))   peakPower,
                        LEFT(a.create_date, 10)        createTime,
                        CASE
                            WHEN b.date_time > DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN SUM(DISTINCT (b.power))
                            ELSE 0 END                 realTimePower,
                        CASE
                            WHEN LEFT(b.date_time, 10) = CURDATE() THEN SUM(DISTINCT (b.today_energy))
                            ELSE 0 END                 todayEnergy,
                        SUM(DISTINCT (b.total_energy)) totalEnergy,
                        b.date_time                    updateTime,
                        a.city                         city
                 FROM v1_station_device a
                          LEFT JOIN bto_device_energy b ON a.sn = b.sn
                 WHERE a.special = '1'
                 GROUP BY a.plant_id
                 ORDER BY a.create_date DESC,
                          a.name1) q,
             (SELECT @i := 0) w
    </select>
    <select id="powerSevenEnergy" resultType="java.util.HashMap">
        SELECT date,
               sum(energy) sumEnergy
        FROM bto_station_list a
                 LEFT JOIN bto_station_day b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
        GROUP BY date
        ORDER BY date DESC
        LIMIT 7
    </select>
    <select id="powerSIXEnergy" resultType="java.util.HashMap">
        SELECT left(date, 7) `month`,
               sum(energy)   sumEnergy
        FROM bto_station_list a
                 LEFT JOIN bto_station_day b ON a.plant_id = b.plant_id
        WHERE a.special = '1'
        GROUP BY `month`
        ORDER BY `month` DESC
        LIMIT 6
    </select>


</mapper>