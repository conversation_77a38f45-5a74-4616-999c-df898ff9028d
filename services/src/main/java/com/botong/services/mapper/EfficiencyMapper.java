package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.EfficiencyDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface EfficiencyMapper extends BaseMapper<EfficiencyDTO> {

    List<EfficiencyDTO> PowerGenerationMonth(@Param("plantId") String plantId, @Param("month") String month );

    List<EfficiencyDTO> PowerGenerationYear(@Param("plantId") String plantId, @Param("year") String year );

}