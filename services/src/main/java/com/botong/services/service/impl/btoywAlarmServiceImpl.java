package com.botong.services.service.impl;

import cn.hutool.core.lang.hash.Hash;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.btoywAlarmMapper;
import com.botong.services.service.btoywAlarmService;
import com.github.pagehelper.PageHelper;

import com.github.pagehelper.PageInfo;
import entityDTO.AlarmAnalysis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import vo.OperatorInfoVO;

import java.util.HashMap;
import java.util.List;

@Service
public class btoywAlarmServiceImpl extends ServiceImpl<btoywAlarmMapper, AlarmAnalysis> implements btoywAlarmService {

    @Autowired
    private btoywAlarmMapper btoywAlarmMapper;


    @Override
    @Cacheable(cacheNames = "HY_APP3M",key = "'AlarmInformationList'+#p1+#p2",cacheManager = "HY_APP_Cache_3M")
    public HashMap<String,Object> AlarmInformationList(String startTime, Integer page, Integer pageSize){
        HashMap<String,Object> resultMap = new HashMap<>();
        //获取电站告警信息列表
        List<AlarmAnalysis> alarmStationList= btoywAlarmMapper.AlarmInformationList(startTime, page, pageSize);
        //获取运维器告警信息列表
        List<OperatorInfoVO> alarmOperatorList = btoywAlarmMapper.OperatorAlarmInformationList(page,pageSize);
        resultMap.put("alarmStationList",alarmStationList);
        resultMap.put("alarmOperatorList",alarmOperatorList);
        return resultMap;
    }
}
