<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.services.mapper.DeviceInformationMapper">
    <resultMap type="entityDTO.DeviceDTO" id="DeviceDTOMap">
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="todayEnergy" column="todayEnergy"/>
        <result property="sysPower" column="sysPower"/>
        <result property="sn" column="sn"/>
        <result property="name" column="name"/>
        <result property="time" column="time"/>
        <result property="power" column="Power"/>
        <result property="snName" column="snName"/>
        <result property="sncount" column="sncount"/>
        <result property="userUid" column="userUid"/>
        <result property="plantId" column="plantId"/>
        <result property="model" column="model"/>
        <result property="dataloggersn" column="dataloggersn"/>
        <result property="devicesn" column="devicesn"/>
        <result property="modulesn" column="modulesn"/>
        <result property="cUserName" column="cUserName"/>
    </resultMap>

    <resultMap type="entityDTO.TUBIAO" id="tubiao">
        <result property="plantUid" column="plantUid"/>
        <result property="time" column="time"/>
        <result property="power" column="power"/>
    </resultMap>

    <select id="DeviceInformation" resultMap="DeviceDTOMap">
        SELECT *
        FROM (
                 SELECT a.plant_uid,
                        name                         name,
                        count(b.sn)                  sncount,
                        time                         time,
                        FORMAT(sum(power) / 1000, 4) power,
                        sum(today_energy)            todayEnergy,
                        sum(total_energy)            totalEnergy
                 FROM `v_station_base` a
                          LEFT JOIN (
                     SELECT plant_id,
                            count(DISTINCT (sn)),
                            sn,
                            power,
                            today_energy,
                            month_energy,
                            year_energy,
                            total_energy,
                            time
                     FROM v_device_list aa
                              LEFT JOIN (
                         SELECT sn, power, today_energy, month_energy, year_energy, total_energy, time
                         FROM bto_device_${tableNme}
                         WHERE time LIKE #{nowtime10}
                            OR time LIKE #{nowtime20}
                         GROUP BY SN
                     ) bb
                                        ON aa.device_sn = bb.sn
                     GROUP BY sn
                 ) b ON a.plant_id = b.plant_id
                 GROUP BY a.plant_id) qw
        WHERE plant_uid = #{plantUid};
    </select>
    <select id="DeviceInformationSN" resultMap="DeviceDTOMap">
        SELECT plant_uid plantUid,
               sn        snName
        FROM v1_station_device
        WHERE plant_uid = #{plantUid}
    </select>
    <select id="DeviceInformationTU" resultMap="tubiao">
        SELECT a.plant_uid                      plantUid,
               sum(b.power) / 1000              power,
               CONCAT(LEFT(b.time, 15), '0:00') time
        FROM v1_station_device a
                 LEFT JOIN bto_device_${tableName} b ON a.sn = b.sn
        WHERE LEFT(b.time, 10) = CURDATE()
          AND plant_uid = #{plantUid}
        GROUP BY a.plant_uid,
                 time
        ORDER BY b.time;
    </select>
</mapper>
