package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYAstationListMapper;
import com.botong.services.mapper.HYAuserListLoginMapper;
import com.botong.services.service.HYAuserlogionService;
import entityDTO.HYAstationList;
import entityDTO.userListLogin;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static utils.ListPageUtils.pageBySubList;

@Service
public class HYAuserlogionServiceImpl extends ServiceImpl<HYAuserListLoginMapper, userListLogin> implements HYAuserlogionService {
    @Autowired
    private com.botong.services.mapper.HYAuserListLoginMapper hyAuserListLoginMapper;

    @Autowired
    private HYAstationListMapper hyAstationListMapper;

    @Override
    public int userlogin(String username, String password) {
        if (hyAuserListLoginMapper.login(username, password) == null) {
            return 0;
        } else {
            return 1;
        }
    }

    @Override
    public List<userListLogin> frontPage(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem = "bto_device_" + dateFormat.format(date);
        String tiem2 = dateFormat2.format(date) + "%";
        return hyAuserListLoginMapper.frontPage(userUid, tiem, tiem2);
    }

    @Override
//    @Cacheable(cacheNames = "accordingToTiemenergy",key = "'accordingToTiemenergy'+#p2",cacheManager = "HY_APP_Cache")
    public int TotalNumberOfAlarms(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem2 = dateFormat2.format(date) + "%";
        return hyAuserListLoginMapper.TotalNumberOfAlarms(userUid, tiem2);
    }

    @Override
    @Cacheable(cacheNames = "TotalNumberOfAlarmsDetails", key = " 'TotalNumberOfAlarmsDetails_'+#p0", cacheManager = "HY_APP_Cache")
    public List<userListLogin> TotalNumberOfAlarmsDetails(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem2 = dateFormat2.format(date) + "%";
        return hyAuserListLoginMapper.TotalNumberOfAlarmsDetails(userUid, tiem2);
    }

    @Override
    public List<userListLogin> AlarmNumberClassification(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem2 = dateFormat2.format(date) + "%";
        return hyAuserListLoginMapper.AlarmNumberClassification(userUid, tiem2);
    }

    @Override
    public List<userListLogin> OfflineNumberClassification(String userUid) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String tiem = dateFormat.format(date);
        return hyAuserListLoginMapper.OfflineNumberClassification(userUid, tiem);
    }

    @Override
    public List<userListLogin> frontPageplantid(String userUid) {
        return hyAuserListLoginMapper.frontPageplantid(userUid);
    }

    @Override
    public List<userListLogin> PowerStationList(Integer a, Integer b) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem = dateFormat.format(date);
        String time2 = dateFormat2.format(date) + "%";
        return hyAuserListLoginMapper.PowerStationList(tiem, time2, a, b);
    }

    @Override
    public List<userListLogin> ifSmartOM(String plantId) {
        String[] split = plantId.split(",");
        return hyAuserListLoginMapper.ifSmartOM(Arrays.asList(split));
    }

    @Override
    public List<userListLogin> ifSmartOMYueShang(String plantId, String time) {
        return hyAuserListLoginMapper.ifSmartOMYueShang(plantId, time);
    }

    @Override
    public List<userListLogin> ifSmartOMYueXia(String plantId, String time) {
        return hyAuserListLoginMapper.ifSmartOMYueXia(plantId, time);
    }

    @Override
    public List<userListLogin> ifSmartOMNianShang(String plantId, String time) {
        return hyAuserListLoginMapper.ifSmartOMNianShang(plantId, time);
    }

    @Override
    public List<userListLogin> ifSmartOMNianXia(String plantId, String time) {
        return hyAuserListLoginMapper.ifSmartOMNianXia(plantId, time);
    }

    @Override
    public List<userListLogin> ifSmartOMALLShang(String plantId) {
        return hyAuserListLoginMapper.ifSmartOMALLShang(plantId);
    }

    @Override
    public List<userListLogin> ifSmartOMALLXia(String plantId) {
//        List<userListLogin> list = hyAuserListLoginMapper.ifSmartOMALLXia(plantId);
//        List<userListLogin> newlist = list.stream().sorted(Comparator.comparing(userListLogin::getTime).reversed()).collect(Collectors.toList());
        return hyAuserListLoginMapper.ifSmartOMALLXia(plantId);
    }

    @Override
    @Cacheable(cacheNames = "accordingToTiemenergy", key = " 'Tiemenergysort_'+#p0+'_num_'+#p1+'_size_'+#p2 ", cacheManager = "HY_APP_Cache_3M")
    public List<HYAstationList> accordingToTiemenergy(int sort, int pageNo, int pageSize) {
        List<HYAstationList> newlist = new ArrayList<>();
        List<HYAstationList> pageList = new ArrayList<>();
        List<HYAstationList> list = selectListt();
        if (sort == 1) {
            newlist = list.stream().sorted(Comparator.comparing(HYAstationList::getCreateDate)).collect(Collectors.toList());
            pageList = new ArrayList<>(pageBySubList(newlist, pageNo, pageSize));
        } else if (sort == 2) {
            newlist = list.stream().sorted(Comparator.comparing(HYAstationList::getCreateDate).reversed()).collect(Collectors.toList());
            pageList = new ArrayList<>(pageBySubList(newlist, pageNo, pageSize));
        } else if (sort == 3) {
            // 获取userName不为空的user的List
            List<HYAstationList> totalNull = list.stream().filter(hYAstationList -> hYAstationList.getTotalenergy() == null).collect(Collectors.toList());
            for (HYAstationList hyAstationList : totalNull) {
                hyAstationList.setTotalenergy("0.0");
                newlist.add(hyAstationList);
            }
            List<HYAstationList> notNullList = list.stream().filter(hYAstationList -> hYAstationList.getTotalenergy() != null).collect(Collectors.toList());
            newlist = notNullList.stream().sorted(Comparator.comparing(HYAstationList::getTotalenergy,Comparator.comparing(Double::parseDouble))).collect(Collectors.toList());
            pageList = new ArrayList<>(pageBySubList(newlist, pageNo, pageSize));
        } else if (sort == 4) {
            List<HYAstationList> totalNull = list.stream().filter(hYAstationList -> hYAstationList.getTotalenergy() == null).collect(Collectors.toList());
            for (HYAstationList hyAstationList : totalNull) {
                hyAstationList.setTotalenergy("0.0");
                newlist.add(hyAstationList);
            }
            List<HYAstationList> notNullList = list.stream().filter(hYAstationList -> hYAstationList.getTotalenergy() != null).collect(Collectors.toList());
            newlist = notNullList.stream().sorted(Comparator.comparing(HYAstationList::getTotalenergy,Comparator.comparing(Double::parseDouble)).reversed()).collect(Collectors.toList());
            pageList = new ArrayList<>(pageBySubList(newlist, pageNo, pageSize));
        }
        return pageList;
    }

    @Override
    public Object versionViewipa() {
        File file = new File("C:/APP/gfUserApp.APK");
        String test = null;
        if (file.exists() && file.isFile()) {
            try {
                ApkFile apkFile = new ApkFile(file);
                ApkMeta apkMeta = apkFile.getApkMeta();
                test = apkMeta.getVersionName();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return test;
    }

    @Override
    public int updatePassword(String userUid, String password) {
        if (hyAuserListLoginMapper.updatePassword(userUid, password) == 1) {
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public int registeredUser(String cUserName, String cUsertel, String cUserEmail, String updateTime, String delFlag) {
        String uuid = UUID.randomUUID().toString().toUpperCase();
        Date date = new Date();
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String nb = dateFormat2.format(date);
        if (hyAuserListLoginMapper.registeredUser(uuid, cUserName, cUsertel, nb, cUserEmail, updateTime, delFlag) == 1) {
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public Object versionView() {
        File file = new File("C:/APP/gfUserApp.APK");
        String test = null;
        if (file.exists() && file.isFile()) {
            try {
                ApkFile apkFile = new ApkFile(file);
                ApkMeta apkMeta = apkFile.getApkMeta();
                test = apkMeta.getVersionName();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return test;
    }

    @Override
    public List<userListLogin> PowerStationListName(Integer a, Integer b, String name) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        String tiem = dateFormat.format(date);
        String time2 = dateFormat2.format(date) + "%";
        String name1 = "%" + name + "%";
        return hyAuserListLoginMapper.PowerStationListName(a, b, tiem, time2, name1);
    }

    //封装查询电站列表
    public List<HYAstationList> selectListt() {
        Date cur_date = new Date();
        Date near_date = new Date(cur_date.getTime() - 10 * 60 * 1000);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String tableName = simpleDateFormat.format(cur_date);
        String nearTime = simpleDateFormat2.format(near_date).substring(0, 15).concat("%");
        List<HYAstationList> list = hyAstationListMapper.selectListt(tableName, nearTime);
        return list;
    }
}
