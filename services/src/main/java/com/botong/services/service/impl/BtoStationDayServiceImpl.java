package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.BtoStationDayMapper;
import com.botong.services.service.BtoStationDayService;
import entity.BtoStationDay;
import entityDTO.BtoStationDayDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * 电站日发电量 impl
 *
 * <AUTHOR> @date 2022-08-15 09:45:17
 */
@Service
public class BtoStationDayServiceImpl extends ServiceImpl<BtoStationDayMapper, BtoStationDay> implements BtoStationDayService {
    @Autowired
    private BtoStationDayMapper btoStationDayMapper;


    @Override
    public List<BtoStationDayDTO> StationMothEnergy() {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        String datee =simpleDateFormat.format(date);
        String jie= datee.substring(0,7)+"%";
        return btoStationDayMapper.StationMothEnergy(jie);
    }

    @Override
    public List<BtoStationDayDTO> powerStationDay() {
        return null;
    }


}
