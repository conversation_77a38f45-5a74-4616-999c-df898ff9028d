<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.services.mapper.VStationListMapper">
    <resultMap type="entity.VStationList" id="vStationListMap">
        <result property="plantId" column="plant_id"/>
        <result property="date" column="date"/>
        <result property="energy" column="energy"/>
        <result property="peakPower" column="peak_power"/>
        <result property="eta" column="eta"/>
    </resultMap>

    <select id="VStationListSelecEta" resultType="entity.VStationList">

        SELECT
            a.plant_id,
            a.date,
            energy,
            peak_power,
            left(energy / (peak_power * 4),6) eta
        FROM
            v_day a
            LEFT JOIN v_station_list b ON a.plant_id = b.plant_id
        where a.plant_id = #{plantId}  and date like #{month};

    </select>

    <select id="VStationListSelecAll" resultType="entity.VStationList">

        SELECT
            plant_id,
            LEFT ( date1, 7 ) month1,
            sum(energy) energys,
            count( date1 ) days,
            left(avg(xl),6)  xlMonth
        FROM
            (
            SELECT
            a.plant_id,
            LEFT ( a.date, 10 ) date1,
            energy,
            peak_power,
            sum(
            LEFT ( energy / ( peak_power * 4 ), 6 )) xl
            FROM
            v_day a
            LEFT JOIN v_station_list b ON a.plant_id = b.plant_id
            WHERE
            a.plant_id = #{plantId} and a.date like #{year}
            GROUP BY
            date1
            ) q
        GROUP BY
            month1;

    </select>






</mapper>