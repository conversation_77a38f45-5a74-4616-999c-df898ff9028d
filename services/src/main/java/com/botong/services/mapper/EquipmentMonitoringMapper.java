package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoDeviceList;
import entityDTO.sheBeiJianCe;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EquipmentMonitoringMapper extends BaseMapper<BtoDeviceList> {
    List<sheBeiJianCe> EquipmentMonitoringplantUid(@Param("page") String page, @Param("pageSize") String pageSize, @Param("tableNme")String tableNme, @Param("Nowtime20")String Nowtime20, @Param("Nowtime10")String Nowtime10, @Param("plantUid") String plantUid);
}
