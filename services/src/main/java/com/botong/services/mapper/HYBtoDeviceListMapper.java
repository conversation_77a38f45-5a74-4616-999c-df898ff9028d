package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoDeviceList;
import entityDTO.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.EfficiencyVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Mapper
public interface HYBtoDeviceListMapper extends BaseMapper<BtoDeviceList> {

    Integer deviceOnline();

    Integer deviceAlert(@Param("curTime")String curTime);

    List<EfficiencyVo> workEfficiencyRanking();

    List<BtoDeviceList> DeviceInformation(@Param("plantUid") String plantUid);

    List<BtoDeviceList> DeviceInformationSN(String plantUid);

    List<TUBIAO> DeviceInformationTU(@Param("plantUid") String plantUid, @Param("tableName") String tableName );

//    List<BtoDeviceList> DeviceMonitorUlist();
    List<DeviceMonitorUListDTO> DeviceMonitorUlist();


    List<sheBeiJianCe> collectionMaxMin(@Param("snName") String snName, @Param("tableNme") String tableNme, @Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> collectioAvg(@Param("snName") String snName,@Param("tableName") String tableName,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> Devicecurrent(@Param("snName") String snName,@Param("tableName") String tableName,@Param("tabletime2")String tabletime2);

    List<HashMap<String,Object>> EquipmentMonitoringplantUid(@Param("plantId") String plantId);

    List<sheBeiJianCe> DeviceVoltage(@Param("snName") String snName,@Param("tableName") String tableName,@Param("tableName2")String tableName2);

    List<sheBeiJianCe> DeviceFrequency(@Param("snName") String snName,@Param("tableName") String tableName,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> Devicetemperature(@Param("snName") String snName,@Param("tableName") String tableName,@Param("tabletime2")String tabletime2);

    List<sheBeiJianCe> DeviceActivePower(@Param("snName") String snName, @Param("tableName") String tableName, @Param("tableName2")String tableName2);

    HashMap<String,String> deviceDetail(@Param("sn") String sn);

    /**
     * 根据电站ID查询所属SN
     * @param plantId
     * @return List<String>
     */
    ArrayList<String> selectSnByPlantId(@Param("plantId") String[] plantId);


    /**
     *    自检提示数
     * @return Integer
     */
    Integer selectSelfInspectionNum();

    /**
     * 查询单个电站总发电量和总功率(根据电站Uid)
     * @param tableName
     * @param plantUId
     * @param date
     * @return
     */
    List<HashMap<String,String>> totalEnergyAndPower(@Param("tableName") String tableName, @Param("plantId") String plantId, @Param("date") String date);

    String totalEnergy(@Param("plantId") String plantId);

    /**
     * 逆变器列表数据
     * @return
     */
    List<DeviceListDTO> deviceList(@Param("statusArray") String[] statusArray);
}
