package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.DeviceInformationMapper;
import com.botong.services.service.DeviceInformationService;
import entity.BtoDeviceList;
import entity.BtoStationList;
import entityDTO.TUBIAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class DeviceInformationServiceImpl extends ServiceImpl<DeviceInformationMapper, BtoDeviceList> implements DeviceInformationService {
    @Autowired
    private DeviceInformationMapper deviceInformationMapper;
    @Override

    public List<BtoDeviceList> DeviceInformation(String plantUid) {
        Date date = new Date();
        Date now20 = new Date(date.getTime() - 1200000);
        Date now10 = new Date(date.getTime() - 600000);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat10 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat simpleDateFormat20 = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        //表名
        String tableNme = simpleDateFormat.format(date);
        //截取当前时间前10分钟
        String Nowtime10 = (simpleDateFormat10.format(now10)).substring(0, 15) + "%";
        //当前时间前20分钟
        String Nowtime20 = (simpleDateFormat20.format(now20)).substring(0, 15) + "%";
        return deviceInformationMapper.DeviceInformation(plantUid, tableNme, Nowtime10, Nowtime20);
    }
    @Override
    public List<BtoDeviceList> DeviceInformationSN(String plantUid) {
        return deviceInformationMapper.DeviceInformationSN(plantUid);
    }

    @Override
    public List<TUBIAO> DeviceInformationTU(String plantUid) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        //表名
        String tableName = simpleDateFormat.format(date);
        return deviceInformationMapper.DeviceInformationTU(plantUid, tableName);
    }
}
