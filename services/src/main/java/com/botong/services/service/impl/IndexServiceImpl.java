package com.botong.services.service.impl;

import com.botong.services.mapper.IndexMapper;
import com.botong.services.service.IndexService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.Carousel;
import entityDTO.PeakPowerAndEnergy;
import entityDTO.StationDeviceNum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class IndexServiceImpl implements IndexService {
    @Autowired
    private IndexMapper indexMapper;

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'peakPowerAndStationEnergy' ", cacheManager = "GF_Web_Cache")
    public PeakPowerAndEnergy peakPowerAndStationEnergy() {
        //总装机容量
        String allPeakPower = indexMapper.getAllPeakPower();
        //日月年总发电量
        HashMap<String, Double> energyMap = indexMapper.getEnergy();
        String totalEnergy = energyMap.get("totalEnergy").toString();
        String todayEnergy = energyMap.get("todayEnergy").toString();
        String monthEnergy = energyMap.get("monthEnergy").toString();
        String yearEnergy = energyMap.get("yearEnergy").toString();
        //近六月发电量
        List<HashMap<String, Object>> sixMonthEnergy = indexMapper.getSixMonthEnergy();
        //近七日发电量
        List<HashMap<String, Object>> sevenDayEnergy = indexMapper.getSevenDayEnergy();
        PeakPowerAndEnergy peakPowerAndEnergy = new PeakPowerAndEnergy(allPeakPower, totalEnergy, todayEnergy, monthEnergy, yearEnergy, sevenDayEnergy, sixMonthEnergy);
        return peakPowerAndEnergy;
    }

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'stationAndDeviceNum' ", cacheManager = "GF_Web_Cache")
    public StationDeviceNum stationAndDeviceNum() {
        StationDeviceNum stationDeviceNum = new StationDeviceNum();
        Integer allStationNum = indexMapper.getAllStationNum();
        Integer normalStationNum = indexMapper.getNormalStationNum();
        Integer alterStationNum = indexMapper.getAlterStationNum();
        Integer offlineStationNum = indexMapper.getOfflineStationNum();
        Integer onlineStationNum = allStationNum - offlineStationNum;
        //正常运行率 = 正常 / 总数
        Double normalStationRate = Double.valueOf(normalStationNum) / Double.valueOf(allStationNum);
        //电站在线率
        Double onlineStationRate = Double.valueOf(onlineStationNum) / Double.valueOf(allStationNum);

        Integer allDeviceNum = indexMapper.getAllDeviceNum();
        Integer normalDeviceNum = indexMapper.getNormalDeviceNum();
        Integer alertDeviceNum = indexMapper.getAlertDeviceNum();
        Integer offlineDeviceNum = indexMapper.getOfflineDeviceNum();
        Integer onlineDeviceNum = allDeviceNum - offlineDeviceNum;
        //逆变器正常运行率 = 正常 / 总数
        Double normalDeviceRate = Double.valueOf(normalDeviceNum) / Double.valueOf(allDeviceNum);
        //逆变器在线率
        Double onlineDeviceRate = Double.valueOf(onlineDeviceNum) / Double.valueOf(allDeviceNum);
        //告警记录数
        Integer alertRecordNum = indexMapper.alertRecordNum();

        stationDeviceNum.setAllStationNum(allStationNum);
        stationDeviceNum.setNormalStationNum(normalStationNum);
        stationDeviceNum.setAlterStationNum(alterStationNum);
        stationDeviceNum.setOfflineStationNum(offlineStationNum);
        stationDeviceNum.setOnlineStationNum(onlineStationNum);
        stationDeviceNum.setNormalStationRate(normalStationRate);
        stationDeviceNum.setOnlineStationRate(onlineStationRate);

        stationDeviceNum.setAllDeviceNum(allDeviceNum);
        stationDeviceNum.setNormalDeviceNum(normalDeviceNum);
        stationDeviceNum.setAlertDeviceNum(alertDeviceNum);
        stationDeviceNum.setOfflineDeviceNum(offlineDeviceNum);
        stationDeviceNum.setOnlineDeviceNum(onlineDeviceNum);
        stationDeviceNum.setNormalDeviceRate(normalDeviceRate);
        stationDeviceNum.setOnlineDeviceRate(onlineDeviceRate);

        stationDeviceNum.setAlertRecordNum(alertRecordNum);

        return stationDeviceNum;
    }

    @Override
    @Cacheable(cacheNames = "GF_WEB", key = " 'realTimePowerCarousel:page_'+#p0+'pageSize_'+#p1", cacheManager = "GF_Web_Cache")
    public PageInfo<Carousel> realTimePowerCarousel(Integer page, Integer pageSize) {
        PageHelper.startPage(page, pageSize);
        List<Carousel> carouselList = indexMapper.realTimePowerCarousel();
        for (int i = 0,j=1; i < carouselList.size(); i++,j++) {
            Carousel carousel = carouselList.get(i);
            String co2 = String.valueOf((Double.parseDouble(carousel.getTodayEnergy()) * 0.3025) / 10000000);
            carouselList.get(i).setCo2(co2);
            carouselList.get(i).setStationNo(j+pageSize*(page-1));
        }
        PageInfo<Carousel> carouselListInfo = new PageInfo<>(carouselList);
        return carouselListInfo;
    }
}


