package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("APP修改电站信息 页面表单参数")
public class ModifyStationForm implements Serializable {
    @ApiModelProperty(value = "电站名称",notes = "电站名称")
    private String stationName;
    @ApiModelProperty(value = "装机容量",notes = "装机容量")
    private String peakPower;

    @ApiModelProperty(value = "电价",notes = "电价")
    private String electrovalency;

    @ApiModelProperty(value = "电站所在国家",notes = "电站所在国家")
    private String country;

    @ApiModelProperty(value = "电站所在省份",notes = "电站所在省份")
    private String state;

    @ApiModelProperty(value = "电站所在城市",notes = "电站所在城市")
    private String city;

    @ApiModelProperty(value = "电站所在县区镇",notes = "电站所在县区镇")
    private String town;

    @ApiModelProperty(value = "电站详细地址",notes = "电站详细地址")
    private String address;

    @ApiModelProperty(value = "经度",notes = "经度")
    private Float longitude;

    @ApiModelProperty(value = "纬度",notes = "纬度")
    private Float latitude;

    @ApiModelProperty(value = "数据更新时间",notes = "数据更新时间")
    private String updateTime;

    @ApiModelProperty(value = "电站ID",notes = "电站ID")
    private String plantId;

    @ApiModelProperty(value = "电表编号",notes = "电表编号")
    private String meterId;
}
