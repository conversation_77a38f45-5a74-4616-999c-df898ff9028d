package com.botong.controller;

import com.botong.services.service.HYARespondentsService;
import entityDTO.StationConditionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @description:河源app添加受访者
 * @date 2022/10/14 20:02
 */
@RestController
@RequestMapping("HYARespondents")
@Api(tags = "河源app添加受访者")
@CrossOrigin
public class HYARespondentsController {

    @Autowired
    HYARespondentsService hyaRespondentsService;

    @ApiOperation("查询受访者列表")
    @GetMapping("SelEctRespondentsList")
    public AjaxResult SelEctRespondentsList(String plantId){
        return AjaxResult.success("查询成功",hyaRespondentsService.SelEctRespondentsList(plantId));
    }


    @ApiOperation("添加受访者")
    @GetMapping("AddEctRespondentsList")
    public AjaxResult AddEctRespondentsList(String userUid,String username,String author,String plantId){
        return hyaRespondentsService.AddEctRespondentsList(userUid, username, author, plantId);
/*        if (hyaRespondentsService.AddEctRespondentsList(userUid,username,author,plantId)==1){
            return AjaxResult.success("创建成功",1);
        }else {
            return AjaxResult.success("插入失败",0);
        }*/
    }

    @ApiOperation("登录成功后查询是否是子账号")
    @GetMapping("loginSelsubAccount")
    public AjaxResult loginSelsubAccount(String userName){
        if (hyaRespondentsService.loginSelsubAccount(userName)==1){
            return AjaxResult.success("查询成功",1);
        }else {
            return AjaxResult.success("查询成功",0);
        }
    }

    @ApiOperation("访问者列表")
    @GetMapping("visitorList")
    public AjaxResult visitorList(String plantId){
        return AjaxResult.success("查询成功",hyaRespondentsService.visitorList(plantId));
    }

    @ApiOperation("校验子账号用户名是否存在")
    @GetMapping("checkSubAccount")
    public AjaxResult checkSubAccount(String userName,String plantId,String userUid){
        if (hyaRespondentsService.checkSubAccount(userName,plantId,userUid)==1){
            return AjaxResult.success("该账户名已存在",1);
        }else if(  hyaRespondentsService.checkSubAccount(userName,plantId,userUid)==3){
            return AjaxResult.success("用户名存在，但没有绑定电站",3);
        }else  if (hyaRespondentsService.checkSubAccount(userName,plantId,userUid)==0) {
            return AjaxResult.success("该账户名不存在",0);
        }else {
            return AjaxResult.success("存在重名情况",2);
        }
    }

    @ApiOperation("查询主子的子账号数量")
    @GetMapping("checkSubAccountNum")
    public AjaxResult checkSubAccountNum(String userName,String accountName){
        if (hyaRespondentsService.checkSubAccountNum(userName,accountName)==1){
            return AjaxResult.success("数量超出限制",1);
        }else {
            return AjaxResult.success("数量未超出限制",0);
        }
    }

    @ApiOperation("移除电站")
    @GetMapping("removePowerStation")
    public AjaxResult removePowerStation(String plantId,String userUid,String password){
        if (hyaRespondentsService.removePowerStation(plantId,userUid,password)==1){
            return AjaxResult.success("移除电站成功",1);
        }else{
            return AjaxResult.success("移除电站失败",0);
        }
    }

    @ApiOperation("河源APP——子账号主页--电站数量")
    @GetMapping("hYSubAccDayMonthAll")
    public AjaxResult hYSubAccDayMonthAll(String userUid) {
        return AjaxResult.success("查询成功", hyaRespondentsService.hYSubAccDayMonthAll(userUid));
    }

    @ApiOperation("河源APP——子账号日月年总发电量信息")
    @GetMapping("hYSubAccDayMonthAll2")
    public AjaxResult hYSubAccDayMonthAll2(String userUid) {
        HashMap<String, Double> hashMap = hyaRespondentsService.hYSubAccDayMonthAll2(userUid);
        return AjaxResult.success("查询成功", hashMap);
    }


    @ApiOperation("子账号-电站告警列表")
    @GetMapping("SubaccountPowerPlantAlarm")
    public AjaxResult SubaccountPowerPlantAlarm(String userUid){
        return AjaxResult.success("查询成功",hyaRespondentsService.SubaccountPowerPlantAlarm(userUid));
    }

    @ApiOperation("电站列表--子账号")
    @GetMapping("stationListAccsucess")
    public AjaxResult stationListAccsucess(String userUid){
        return AjaxResult.success("查询成功",hyaRespondentsService.stationListAccsucess(userUid));

    }


    @ApiOperation("查询子账号的电站列表-包括条件排序")
    @PostMapping("selectSubStationList")
    public AjaxResult selectSubStationList(String userUid,  Integer sortWith) {
        return AjaxResult.success("查询成功", hyaRespondentsService.selectSubStationList(userUid,sortWith));
    }

    @ApiOperation("子账号的电站列表条件筛选")
    @PostMapping("filterSubStationList")
    public AjaxResult filterSubStationList(String userUid, @RequestBody StationConditionDTO stationConditionDTO) {
        return AjaxResult.success("查询成功", hyaRespondentsService.filterSubStationList(userUid, stationConditionDTO));
    }

}
