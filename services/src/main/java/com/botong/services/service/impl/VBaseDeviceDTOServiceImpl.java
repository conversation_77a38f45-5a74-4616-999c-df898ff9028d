package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.VBaseDeviceDTOMapper;
import com.botong.services.mapper.VDayMapper;
import com.botong.services.service.VBaseDeviceDTOService;
import com.botong.services.service.VDayService;
import com.botong.services.utils.PageHelp1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entity.VDay;
import entityDTO.BtoDeviceMapDTO;
import entityDTO.VBaseDeviceDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class VBaseDeviceDTOServiceImpl extends ServiceImpl<VBaseDeviceDTOMapper, VBaseDeviceDTO> implements VBaseDeviceDTOService {

    @Autowired
    private VBaseDeviceDTOMapper vBaseDeviceDTOMapper;

    @Override
    public PageInfo<VBaseDeviceDTO> selectDay(String time1, String state1, String city1, String address1 ,Integer page,Integer pageSize){
        String[] split = time1.split("-");
        StringBuffer buffer = new StringBuffer();
        PageHelp1 pageHelp1 = new PageHelp1();
        for (int i = 0; i < split.length; i++) {
            buffer.append(split[i]);
        }
        String table = buffer.toString();
        String time = time1 + "%:00:00";
        ArrayList<List<VBaseDeviceDTO>> list1 = new ArrayList<>();
        String state = state1 + "%";
        String city = "%" + city1 + "%";
        String address = "%" + address1 + "%";
        String[] sns = vBaseDeviceDTOMapper.selectSn(state, city, address, table);
        for (String sn :sns) {
            List<VBaseDeviceDTO> list = vBaseDeviceDTOMapper.selectAll(time, state, city, address, table,sn );
            list1.add(list);
        }
        PageInfo info = pageHelp1.pageHelper(list1, page, pageSize);
        return info;
    }



    @Override
    public PageInfo<VBaseDeviceDTO> selectMonth(String month, String state1, String city1, String address1 ,Integer page,Integer pageSize){
        String time = month + "%";
        PageHelp1 pageHelp1 = new PageHelp1();
        ArrayList<List<VBaseDeviceDTO>> list1 = new ArrayList<>();
        String state = state1 + "%";
        String city = "%" + city1 + "%";
        String address = "%" + address1 + "%";
        String[] sns = vBaseDeviceDTOMapper.selectMonthSn(state, city, address);
        for (String sn :sns) {
            List<VBaseDeviceDTO> list = vBaseDeviceDTOMapper.selectMonth(time, state, city, address,sn );
            list1.add(list);
        }
        PageInfo info = pageHelp1.pageHelper(list1, page, pageSize);
        return info;
    }

    @Override
    public PageInfo<VBaseDeviceDTO> selectYear(String year, String state1, String city1, String address1 ,Integer page,Integer pageSize){
        String time = year + "%";
        PageHelp1 pageHelp1 = new PageHelp1();
        ArrayList<List<VBaseDeviceDTO>> list1 = new ArrayList<>();
        String state = state1 + "%";
        String city = "%" + city1 + "%";
        String address = "%" + address1 + "%";
        String[] sns = vBaseDeviceDTOMapper.selectMonthSn(state, city, address);
        for (String sn :sns) {
            List<VBaseDeviceDTO> list = vBaseDeviceDTOMapper.selectYear(time, state, city, address,sn );
            list1.add(list);
        }
        PageInfo info = pageHelp1.pageHelper(list1, page, pageSize);
        return info;
    }
}
