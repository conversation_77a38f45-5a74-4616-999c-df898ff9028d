package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoUserList;
import entityDTO.userListLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface userListLoginMapper extends BaseMapper<userListLogin> {


     int registeredUser(@Param("uuid") String uuid,@Param("cUserName") String cUserName,@Param("cUsertel") String cUsertel,@Param("nb") String nb,@Param("cUserEmail") String cUserEmail,@Param("updateTime") String updateTime,@Param("delFlag") String delFlag);

    Object login(@Param("username") String username, @Param("password") String password);

    List<userListLogin> frontPage(@Param("userUid") String userUid, @Param("time") String time, @Param("time2") String time2);

    int TotalNumberOfAlarms(@Param("userUid") String userUid, @Param("time2")  String tiem2);

    List<userListLogin> TotalNumberOfAlarmsDetails(@Param("userUid") String userUid, @Param("time2")  String tiem2);

    List<userListLogin> AlarmNumberClassification(@Param("userUid") String userUid, @Param("time2")  String tiem2);

    List<userListLogin> OfflineNumberClassification(@Param("userUid") String userUid,@Param("time")String time);

    List<userListLogin> frontPageplantid(@Param("userUid") String userUid);

    List<userListLogin> PowerStationList(@Param("time") String tiem,@Param("time2") String time2,@Param("plantId") List<String> plantId);

    List<userListLogin> ifSmartOM(@Param("plantId") List<String> plantId);

    List<userListLogin> ifSmartOMYueShang(@Param("plantUid") String plantUid,@Param("time") String time);

    List<userListLogin> ifSmartOMYueXia(@Param("plantUid") String plantUid,@Param("time") String time);

    List<userListLogin> ifSmartOMNianShang(@Param("plantUid") String plantUid,@Param("time") String time);

    List<userListLogin> ifSmartOMNianXia(@Param("plantUid") String plantUid,@Param("time") String time);

    List<userListLogin> ifSmartOMALL(@Param("plantUid") String plantUid);

    int updatePassword(@Param("userUid") String userUid,@Param("password") String password);

}
