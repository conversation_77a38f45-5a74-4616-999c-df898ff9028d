package com.botong.controller;

import com.botong.services.service.HYBtoDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("btodevice")
@Api(tags = "逆变器数据")
@CrossOrigin
public class HYBtoDeviceController {
    @Autowired
    private HYBtoDeviceService hybtoDeviceService;

//    @ApiOperation("获取所有逆变器日月年总发电信息")
//    @GetMapping("inverterElectricityInfo")
//    public AjaxResult inverterElectricityInfo(){
//        return AjaxResult.success("操作成功",hybtoDeviceService.inverterElectricityInfo());
//    }

}
