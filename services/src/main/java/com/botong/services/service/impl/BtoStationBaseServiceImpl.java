package com.botong.services.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.services.mapper.BtoStationBaseMapper;
import com.botong.services.service.BtoDeviceListService;
import com.botong.services.service.BtoDeviceService;
import com.botong.services.service.BtoStationBaseService;
import com.botong.services.service.BtoStationListService;
import com.botong.services.vo.MapVo;
import com.botong.services.vo.UserElectricityVo;
import entity.BtoDevice;
import entity.BtoStationBase;
import entity.BtoStationList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import vo.ElectricityStatisticsVo;
import vo.StationBaseVo;

import javax.xml.crypto.Data;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;


/**
 * 电站基本信息 impl
 *
 * <AUTHOR> @date 2022-08-15 15:35:23
 */
@Service
public class BtoStationBaseServiceImpl extends ServiceImpl<BtoStationBaseMapper, BtoStationBase> implements BtoStationBaseService {

    @Autowired
    private BtoStationBaseMapper btoStationBaseMapper;

    @Autowired
    private BtoDeviceService btoDeviceService;

    @Autowired
    private BtoStationListService btoStationListService;

    @Override
    public Object getProvincialStation(String type, String areaName) {
        List<BtoStationBase> btoStationBases = new ArrayList<>();
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
        if ("provincial".equals(type)) {
            //省级
            queryWrapper.eq("state", areaName);
            btoStationBases = baseMapper.selectList(queryWrapper);
        } else {
            //市级
            queryWrapper.clear();
            queryWrapper.eq("city", areaName);
            btoStationBases = baseMapper.selectList(queryWrapper);
        }
        return btoStationBases;
    }

    @Override
    public Object getAllStation() {
        return baseMapper.selectCount(null);
    }

    @Override
    public HashMap<String, Object> getDeforestation() {
        ElectricityStatisticsVo electricityStatisticsVo = btoDeviceService.inverterElectricityInfo().get(0);
        float allElectricity = Float.parseFloat(electricityStatisticsVo.getAllElectricity());
        float dayElectricity = Float.parseFloat(electricityStatisticsVo.getDayElectricity());
        HashMap<String, Object> hashMap = new HashMap<>();
        //节约标准煤（万吨）
        hashMap.put("coal", 0.0001229 * (allElectricity / 10));
        //日CO2减排（吨）
        hashMap.put("dayEmission", dayElectricity / 10 * 0.00009971220715594546);
        //累计CO2减排（万吨）
        hashMap.put("allEmission", 0.00009969593412803057 * allElectricity);
        //累计减少砍伐（万棵）
        hashMap.put("felling", 0.0001772885734520353 * allElectricity);
        return hashMap;
    }

    @Override
    public BtoStationBase getLatitudeAndLongitude(String plantId) {
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("longitude", "latitude");
        queryWrapper.eq("plant_id", plantId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public HashMap<String, Object> userStationInfo(String userUid) {
        HashMap<String, Object> hashMap = new HashMap<>();
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plant_id", userUid);
        List<BtoStationBase> btoStationBase = baseMapper.selectList(queryWrapper);
        String phoneNum =  btoStationListService.getPhoneNumByUid(userUid);
        hashMap.put("btoStationBase", btoStationBase);
        hashMap.put("phoneNum", phoneNum);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> userElectricityInfo(String plantId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        String time = "";
        Date data = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
        time = dateFormat.format(data);
        String tableName = "bto_device_";
        tableName += yyyyMMdd.format(data);
        UserElectricityVo userElectricityVo = baseMapper.userElectricityInfo(plantId, time += "%",tableName);
        hashMap.put("userInfo", userElectricityVo);
        //今日收益
        hashMap.put("todayIncome", Float.parseFloat(userElectricityVo.getTodayElectricity()) * 0.45);
        //累计收益
        hashMap.put("totalIncome", Float.parseFloat(userElectricityVo.getAllElectricity()) * 0.45);
        //累计植树
        hashMap.put("allTree", Float.parseFloat(userElectricityVo.getAllElectricity()) * 0.0017731072664899626);
        //减排
        hashMap.put("emission", Float.parseFloat(userElectricityVo.getAllElectricity()) * 0.000997047854397215);
        return hashMap;
    }

    @Override
    public HashMap<String, Object> userChartInfo(String plantId, String timeType, String time, String deviceSns,String tableName) {
        String[] split = deviceSns.split(",");
        HashMap<String, Object> hashMap = new HashMap<>();
        //日
        if ("day".equals(timeType)) {
            for (String sn : split) {
                hashMap.put(sn, btoDeviceService.getDayPowerBySn(sn, time,tableName));
            }
        } else if ("month".equals(timeType)) {
            //月
            hashMap = btoDeviceService.getMonthElectricitySn(plantId, time += "%");
        } else if ("year".equals(timeType)) {
            //年
            hashMap = btoDeviceService.getYearElectricitySn(plantId, time += "%");
        } else if ("all".equals(timeType)) {
            //总
            hashMap = btoDeviceService.getAllElectricitySn(deviceSns,time);
        } else {
            hashMap.put("error", "timeType格式有误");
        }


        return hashMap;
    }

    @Override
    public MapVo userMapInfo(String plantId) {
        return baseMapper.userMapInfo(plantId);
    }


    @Override
    public ArrayList<StationBaseVo> provincialNumber() {
        ArrayList<StationBaseVo> stationBaseVos = new ArrayList<>();
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.groupBy("state");
        queryWrapper.select("state");
        for (BtoStationBase btoStationBase : baseMapper.selectList(queryWrapper)) {
            StationBaseVo stationBaseVo = new StationBaseVo();
            stationBaseVo.setNames(btoStationBase.getState());
            queryWrapper.clear();
            queryWrapper.eq("state", btoStationBase.getState());
            stationBaseVo.setNums(baseMapper.selectCount(queryWrapper));
            stationBaseVos.add(stationBaseVo);
        }
        return stationBaseVos;
    }

    @Override
    public ArrayList<StationBaseVo> municipalNumber() {
        ArrayList<StationBaseVo> stationBaseVos = new ArrayList<>();
        QueryWrapper<BtoStationBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("city", "state");
        queryWrapper.groupBy("city");
        for (BtoStationBase btoStationBase : baseMapper.selectList(queryWrapper)) {
            queryWrapper.clear();
            StationBaseVo stationBaseVo = new StationBaseVo();
            stationBaseVo.setNames(btoStationBase.getCity());
            stationBaseVo.setProvince(btoStationBase.getState());
            queryWrapper.eq("city", btoStationBase.getCity());
            stationBaseVo.setNums(baseMapper.selectCount(queryWrapper));
            stationBaseVos.add(stationBaseVo);
        }
        return stationBaseVos;
    }

    @Override
    public Object stationBaseDetail() {
        return baseMapper.selectCount(null);
    }

}
