package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/7 15:56
 */
@Data
@ApiModel("运维器实时监控信息实体-用户APP")
public class OperatorMonitorInfoDTO implements Serializable {
    @ApiModelProperty("IMEI")
    private String IMEI;
    @ApiModelProperty("运维器信号量强度 单位（dBm）")
    private String semaphore;
    @ApiModelProperty("当前时间")
    private String dataTime;
    @ApiModelProperty("A点-电网电压")
    private String apv;
    @ApiModelProperty("B点-失压开关")
    private String bpv;
    @ApiModelProperty("C点-过流开关")
    private String cpv;
    @ApiModelProperty("D点-智慧运维器电压")
    private String dpv;
    @ApiModelProperty("故障信息-实体对象")
    private OperatorAlarmInfoDTO operatorAlarmInfo;

}
