package vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 运维器告警信息实体
 */
@Data
public class OperatorInfoVO implements Serializable {
    /**
     * 电站名称
     */
    private String stationName;
    /**
     * 电站UID
     */
    private String plantUid;
    /**
     * 运维器IMEI
     */
    private String imei;
    /**
     * 告警名称
     */
    private String alarmName;
    /**
     * 告警时间
     */
    private String alarmTime;
    /**
     * a点电压
     */
    private String aPointVoltage;
    /**
     * b点电压
     */
    private String bPointVoltage;
    /**
     * c点电压
     */
    private String cPointVoltage;
}
