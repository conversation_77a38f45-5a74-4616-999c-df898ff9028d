package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电站&逆变器数量(总、正常、在线、离线、告警)")
public class StationDeviceNum implements Serializable {

    @ApiModelProperty(value = "总电站数量", notes = "总电站数量")
    private Integer allStationNum;

    @ApiModelProperty(value = "在线电站数量", notes = "在线电站数量")
    private Integer onlineStationNum;

    @ApiModelProperty(value = "告警电站数量", notes = "告警电站数量")
    private Integer alterStationNum;

    @ApiModelProperty(value = "正常电站数量", notes = "正常电站数量")
    private Integer normalStationNum;

    @ApiModelProperty(value = "离线电站数量", notes = "离线电站数量")
    private Integer offlineStationNum;

    @ApiModelProperty(value = "电站正常运行率", notes = "电站正常运行率")
    private Double normalStationRate;

    @ApiModelProperty(value = "电站在线率", notes = "电站在线率")
    private Double onlineStationRate;

    @ApiModelProperty(value = "总逆变器数量", notes = "总逆变器数量")
    private Integer allDeviceNum;

    @ApiModelProperty(value = "在线逆变器数量", notes = "在线逆变器数量")
    private Integer onlineDeviceNum;

    @ApiModelProperty(value = "告警逆变器数量", notes = "告警逆变器数量")
    private Integer alertDeviceNum;

    @ApiModelProperty(value = "正常逆变器数量", notes = "正常逆变器数量")
    private Integer normalDeviceNum;

    @ApiModelProperty(value = "离线逆变器数量", notes = "离线逆变器数量")
    private Integer offlineDeviceNum;

    @ApiModelProperty(value = "逆变器正常运行率", notes = "逆变器正常运行率")
    private Double normalDeviceRate;

    @ApiModelProperty(value = "逆变器在线率", notes = "逆变器在线率")
    private Double onlineDeviceRate;

    @ApiModelProperty(value = "告警记录数", notes = "告警记录数")
    private Integer alertRecordNum;

}
