package entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value="HyStationInfo",description = "河源电站统计信息实体类")
public class HyStationInfo implements Serializable {
    /**
     * 电站UID
     */
    private String plantUid;
    /**
     * 电站名称
     */
    @ApiModelProperty(value = "电站名称")
    private String stationName;
    /**
     * 逆变器数量
     */
    @ApiModelProperty(value = "逆变器数量")
    private Integer snCount;
    /**
     * 装机容量
     */
    @ApiModelProperty(value = "装机容量")
    private String peakPower;

    /**
     * 日发电量
     */
    @ApiModelProperty(value = "日发电量")
    private Float energy;

    /**
     * 告警数量
     */
    @ApiModelProperty(value = "告警数量")
    private String alarmCount;

    /**
     * 总发电量
     */
    @ApiModelProperty(value = "总发电量")
    private Float totalEnergy;
    /**
     * 电站所属用户
     */
    @ApiModelProperty(value = "电站所属用户")
    private String userName;
    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
    private String dateTime;
    /**
     * 站点地址
     */
    @ApiModelProperty(value = "站点地址")
    private String address;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createDate;

    /**
     * 等效利用小时(需自己计算)
     */
    @ApiModelProperty(value = "等效利用小时",notes = "(需自己计算)")
    private Double equivalentUseHour;
    /**
     *  收入(需自己计算)
     */
    @ApiModelProperty(value = "收入",notes = "(需自己计算)")
    private Double income;
    /**
     * 二氧化碳减排 (吨)(需自己计算)
     */
    @ApiModelProperty(value = "二氧化碳减排 (吨)",notes = "(需自己计算)")
    private Double totalReduceCo2;
    /**
     * 减少砍伐树木 (棵)(需自己计算)
     */
    @ApiModelProperty(value = "减少砍伐树木 (棵)",notes = "(需自己计算)")
    private Double totalPlantTreeNum;
    /**
     * 连续离线时长(需自己计算)
     */
    @ApiModelProperty(value = "连续离线时长",notes = "(需自己计算)")
    private String offlineTime;

    @ApiModelProperty(value = "电厂(交易对象)编号",notes = "")
    private String meterId;

}
