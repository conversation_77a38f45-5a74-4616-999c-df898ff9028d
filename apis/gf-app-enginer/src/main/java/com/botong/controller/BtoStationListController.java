package com.botong.controller;

import com.botong.services.service.BtoStationEnginerService;
import entityDTO.RegisterStationForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("BtoStationEnginer")
@Api(tags = "电站管理")
public class BtoStationListController {
    @Autowired
    private BtoStationEnginerService btoStationEnginerService;

    @ApiOperation("工程师注册电站")
    @PostMapping ("registerBtoStation")
    public AjaxResult RegisterBtoStation(@RequestBody RegisterStationForm registerStationForm) {
        if(btoStationEnginerService.registerStation(registerStationForm)==1){
            return AjaxResult.success("操作成功","1");
        }else {
            return AjaxResult.success("创建失败","0");
        }
    }

    @ApiOperation("工程师注册电站_第二个版本优化，跳过注册用户——河源")
    @PostMapping ("engnieerBtoStation")
    public AjaxResult engnieerBtoStation(@RequestBody RegisterStationForm registerStationForm) {
        if(btoStationEnginerService.registerStation2(registerStationForm)==1){
            return AjaxResult.success("操作成功","1");
        }else {
            return AjaxResult.success("创建失败","0");
        }
    }

    @ApiOperation("根据电站名称查询电站uid")
    @GetMapping("selectPlantUidByName")
    public AjaxResult selectPlantUidByName(String name) {
        String plantUid = btoStationEnginerService.selectPlantUidByName(name);
        if(plantUid!=null){
            return AjaxResult.success("查询成功",plantUid);
        }else {
            return AjaxResult.success("查询失败","fail");
        }
    }
    @ApiOperation("根据电站id查询整线值")
    @GetMapping("selectPlantIdBySpecial")
    public AjaxResult selectPlantUidBySpecial(String PlantId) {
        String special = btoStationEnginerService.selectPlantIdBySpecial(PlantId);
        if(special==null || special.equals("")){
            return AjaxResult.success("special不存在","fail");
        }else {
            return AjaxResult.success("查询成功",special);
        }
    }


    //注册电站随机生成id
    public static String getNumber(Integer length) {
        String uid = "";
        for (Integer i = 0; i < length; i++) {
            String randChar = String.valueOf(Math.round(Math.random() * 9));
            uid = uid.concat(randChar);
        }
        return uid;
    }













    @ApiOperation("查询所有电站_测试——废弃")
    @GetMapping("SelStationAll")
    public AjaxResult SelStationAll(){
        return AjaxResult.success("查询成功",btoStationEnginerService.SelStationAll());
    }


    @ApiOperation("查重电站名")
    @GetMapping("CheckStationName")
    public AjaxResult CheckStationName(String StationName){
        return AjaxResult.success("查询成功",btoStationEnginerService.CheckStationName(StationName));
    }

    @ApiOperation("工程师需求——电站告警")
    @GetMapping("RequirementsPowerStationAlarms")
    public AjaxResult RequirementsPowerStationAlarms(Integer page,Integer pagesize){
        return AjaxResult.success("查询成功",btoStationEnginerService.RequirementsPowerStationAlarms(page,pagesize));
    }

}
