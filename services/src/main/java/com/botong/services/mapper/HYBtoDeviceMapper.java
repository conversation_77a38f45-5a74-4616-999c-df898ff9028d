package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.BtoDevice;
import entityDTO.BtoStationDayDTO;
import entityDTO.returnPrimaryId;
import entityDTO.xuDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.ElectricityStatisticsVo;

import java.util.List;

@Mapper
public interface HYBtoDeviceMapper extends BaseMapper<BtoDevice> {
    List<ElectricityStatisticsVo> inverterElectricityInfo(@Param("tableName") String tableName, @Param("time") String time);

    float getCurrentPowerBySn(@Param("tableName") String tableName, @Param("sn") String sn);

    BtoDevice getDayMonthYearAllInfo(@Param("tableName") String tableName,@Param("dataloggerSn") String dataloggerSn);

    xuDto powerStationDay(@Param("time") String time);

    int updatePassword(@Param("userUid") String userUid,@Param("password") String password);

    List<returnPrimaryId> returnPrimaryId(@Param("plantId") String plantId, @Param("plantUid") String plantUid);

}
