package com.botong.controller;


import com.botong.services.service.HYBtoStationBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

import javax.annotation.Resource;

@RestController
@RequestMapping("btostationbase")
@Api(tags = "电站基本信息")
@CrossOrigin
public class HYBtoStationBaseController {
    @Resource
    private HYBtoStationBaseService hyBtoStationBaseService;

    @ApiOperation("获取所有的电站数量")
    @GetMapping("getAllStation")
    public AjaxResult getProvincialStation() {
        return AjaxResult.success("操作成功", hyBtoStationBaseService.getAllStation());
    }
    @ApiOperation("获取砍伐减排信息")
    @GetMapping("getDeforestation")
    public AjaxResult getDeforestation() {
        return AjaxResult.success("操作成功",hyBtoStationBaseService.getDeforestation());
    }
    @ApiOperation("市级电站数量")
    @GetMapping("municipalNumber")
    public AjaxResult municipalNumber() {
        return AjaxResult.success("操作成功", hyBtoStationBaseService.municipalNumber());
    }

    @ApiOperation("根据省或市获取相应的电站，省级type：provincial，市级type：municipal")
    @GetMapping("getProvincialStation")
    public AjaxResult getProvincialStations() {
        return AjaxResult.success("操作成功", hyBtoStationBaseService.getProvincialStations());
    }
}
