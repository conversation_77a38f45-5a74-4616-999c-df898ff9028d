package entityDTO;

import cn.hutool.core.date.DateTime;
import lombok.Data;

import java.io.Serializable;

@Data
public class IncomeStatisticDTO implements Serializable {
    //电站id
    private String plant_id;
    //日发电量
    private float today_energy;
    //月发电量
    private float month_energy;
    //年发电量
    private float year_energy;
    //时间
    private String time;

    private String plant_uid;
    private String name;
    private String sn;
    private String date;
    private String energy;
}
