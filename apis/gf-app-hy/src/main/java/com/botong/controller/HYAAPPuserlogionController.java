package com.botong.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@RestController
@RequestMapping("userlogion")
@Api(tags = "登录")
public class HYAAPPuserlogionController {

    @Autowired
    private com.botong.services.service.HYAuserlogionService hyAuserlogionService;


    @ApiOperation("用户登录")
    @PostMapping("Login")
    public AjaxResult userlogin(String username,String password){

        if (hyAuserlogionService.userlogin(username,password)==1){
            return AjaxResult.success("操作成功",hyAuserlogionService.userlogin(username,password));
        }else {
            return AjaxResult.success("操作失败,用户或者密码错误");
        }
    }


}
