package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.UserInfoMapper;
import com.botong.services.service.BtoDeviceService;
import com.botong.services.service.HYBtoDeviceService;
import com.botong.services.service.HYUserInfoService;
import com.botong.services.service.UserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.DateElectricityVo;

import java.util.HashMap;

@Service
public class HYUserInfoServiceImpl extends ServiceImpl<UserInfoMapper, DateElectricityVo> implements HYUserInfoService {


    @Autowired
    private HYBtoDeviceService btoDeviceService;

    @Override
    public HashMap<String, Object> getUserDerivativeInfo(String dataloggerSn) {
        DateElectricityVo dayMonthYearAllInfo = btoDeviceService.getDayMonthYearAllInfo(dataloggerSn);
        HashMap<String, Object> hashMap = new HashMap<>();
        //发电量
        hashMap.put("electricity", dayMonthYearAllInfo);
        //节煤，0.0001229
        hashMap.put("coal", "0.0001229");
        //二氧化碳减排量0.0996959
        hashMap.put("emission", "0.0996959");
        //辐照量
        hashMap.put("spoke", null);
        return hashMap;
    }
}
