package com.botong.controller;

import com.botong.services.service.BtoDeviceService;
import com.botong.services.service.HYBtoDeviceService;
import com.botong.services.service.HYUserInfoService;
import com.botong.services.service.UserInfoService;
import entityDTO.AlarmAnalysisConditionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import utils.AjaxResult;

@RestController
@RequestMapping("userInfo")
@Api(tags = "用户综合信息")
@CrossOrigin
public class UserInfoController {

    @Autowired
    private HYUserInfoService userInfoService;

    @Autowired
    private HYBtoDeviceService btoDeviceService;

    @ApiOperation("获取电量衍生日月年信息")
    @GetMapping("getUserDerivativeInfo")
    public AjaxResult inverterElectricityInfo(String dataloggerSn){return AjaxResult.success("操作成功",userInfoService.getUserDerivativeInfo(dataloggerSn));}

    @ApiOperation("获取用户的综合信息")
    @GetMapping("getUserComplexInfo")
    public AjaxResult getUserComplexInfo(String plantId){return AjaxResult.success("操作成功",btoDeviceService.getUserComplexInfo(plantId));}

    @ApiOperation("电站功率日")
    @GetMapping("powerStationDay")
    public AjaxResult powerStationDay(){
        return AjaxResult.success("查询成功",btoDeviceService.powerStationDay());
    }

    @ApiOperation("修改密码")
    @PostMapping("updatePassword")
    public AjaxResult updatePassword(String userUid,String password){
        if (btoDeviceService.updatePassword(userUid,password)==1){
            return AjaxResult.success("修改成功",btoDeviceService.updatePassword(userUid,password));
        }else {
            return AjaxResult.success("修改失败，用户可能不存在！",btoDeviceService.updatePassword(userUid,password));
        }
    }


    @ApiOperation("返回主要id")
    @GetMapping("returnPrimaryId")
    public AjaxResult returnPrimaryId(String plantId,String plantUid){
        return AjaxResult.success("查询成功",btoDeviceService.returnPrimaryId(plantId,plantUid));
    }



}