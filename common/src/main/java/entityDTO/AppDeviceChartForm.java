package entityDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 河源APP 逆变器图表信息--请求页面参数实体
 * <AUTHOR>
 */
@Data
@ApiModel("河源APP 逆变器图表信息--请求页面参数实体")
public class AppDeviceChartForm implements Serializable {

    @ApiModelProperty(value="时间类型标记",notes = "默认值(day、month、year、all)")
    private String timeType;

    @ApiModelProperty(value="传递时间",notes = "传递时间")
    private String time;

    @ApiModelProperty(value="电站Id",notes ="电站Id" )
    private String plantId;

    @ApiModelProperty(value="逆变器SN",notes ="逆变器SN(字符串数组类型)" )
    private String[] deviceSns;
}
