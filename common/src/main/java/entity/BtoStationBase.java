package entity;


import lombok.Data;
import lombok.EqualsAndHashCode;


import java.util.Date;

/**
 * 电站基本信息
 * 
 * <AUTHOR> @date 2022-08-15 15:35:23
 */
@Data
public class BtoStationBase {




	/**
	 * 电站名称
	 */
	private String name;

	/**
	 * 用户id
	 */
	private String userId;

	/**
	 * 电站id
	 */
	private String plantId;

	/**
	 * 三晶电站id
	 */
	private String plantNo;

	/**
	 * 电站uid
	 */
	private String plantUid;

	/**
	 * 用户uid
	 */
	private String userUid;

	/**
	 * 电站描述
	 */
	private String description;

	/**
	 * 电站备注
	 */
	private String notes;

	/**
	 * 电站当前状态（0：离线，1：正常运行，2：告警运行）
	 */
	private Integer status;

	/**
	 * 国家
	 */
	private String country;

	/**
	 * 省或州
	 */
	private String state;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 地址一
	 */
	private String address1;

	/**
	 * 地址二
	 */
	private String address2;

	/**
	 * 经度
	 */
	private Float longitude;

	/**
	 * 纬度
	 */
	private Float latitude;

	/**
	 * 邮编
	 */
	private String postal;

	/**
	 * 海拔高度
	 */
	private String elevation;

	/**
	 * 电站类型（0：并网，1：储能，2：混合，3：交流耦合）
	 */
	private Integer plantType;

	/**
	 * 并网类型
	 */
	private String gridType;

	/**
	 * 直流安装功率
	 */
	private Float installedDc;

	/**
	 * 交流安装功率
	 */
	private Float installedAc;

	/**
	 * 安装面积（m²）
	 */
	private String installedPanel;

	/**
	 * 建站日期
	 */
	private Date createDate;

	/**
	 * 图片 url
	 */
	private String imageUrl;

	/**
	 * 装机容量
	 */
	private String peakPower;

	/**
	 * 货币单位
	 */
	private String currency;

	/**
	 * 时区
	 */
	private String timezone;

	/**
	 * 业主单位
	 */
	private String ownerorganization;

	/**
	 * 业主联系人
	 */
	private String ownercontact;

	/**
	 * 开发商单位
	 */
	private String designerorganization;

	/**
	 * 开发商联系人
	 */
	private String designercontact;

	/**
	 * 安装商单位
	 */
	private String installerorganization;

	/**
	 * 安装商联系人
	 */
	private String installercontact;

	/**
	 * 安装商
	 */
	private String installer;

	/**
	 * 运维商单位
	 */
	private String operatororganization;

	/**
	 * 运维商联系人
	 */
	private String operatorcontact;

	/**
	 * 运维商
	 */
	private String operator;

	/**
	 * 融资单位
	 */
	private String financierorganizaton;

	/**
	 * 融资联系人
	 */
	private String financierconcact;

	/**
	 * 用电户单位
	 */
	private String offtakerorganization;

	/**
	 * 用电户联系人
	 */
	private String offtakercontact;

	/**
	 * 并网单位
	 */
	private String jurisdictionorganization;

	/**
	 * 并购联系人
	 */
	private String jurisdictioncontact;

	/**
	 * 请求时的语言
	 */
	private String locale;

	/**
	 * 
	 */
	private String arrays;

	/**
	 * 
	 */
	private String inverters;

	/**
	 * 
	 */
	private String trackerType;

	/**
	 * 
	 */
	private Date updateTime;

	/**
	 * 
	 */
	private Date createTime;


}
