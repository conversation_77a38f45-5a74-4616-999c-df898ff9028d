package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class BtoDeviceMapDTO implements Serializable {
    /**
     * 逆变器状态
     */
    private String status;

    /**
     * 总发电量
     */
    private String totalEnergy;
    /**
     * 装机容量
     */
    private String peakPower;
    /**
     * 逆变器sn
     */
    public String sn;
    /**
     * 发电量
     */
    public String todayEnergy;

    /**
     * 时间
     */
    public String time;

    /**
     * 电站ID
     */
    public String plantId;

    /**
     * 姓名
     */
    public String stationName;

    /**
     * 功率
     */
    public String power;

    /**
     * 信号强度
     */
    public String Rssi;

    /**
     * 功率曲线1-9
     */
   private String  power1;
   private String  power2;
   private String  power3;
   private String  power4;
   private String  power5;
   private String  power6;
   private String  power7;
   private String  power8;
   private String  power9;
    /**
     * 直流电流曲线1-9
     */
   private String  ipv1;
   private String  ipv2;
   private String  ipv3;
   private String  ipv4;
   private String  ipv5;
   private String  ipv6;
   private String  ipv7;
   private String  ipv8;
   private String  ipv9;
    /**
     * 直流电压曲线1-9
     */
   private String  vpv1;
   private String  vpv2;
   private String  vpv3;
   private String  vpv4;
   private String  vpv5;
   private String  vpv6;
   private String  vpv7;
   private String  vpv8;
   private String  vpv9;
    /**
     * 交流电流曲线1-3
     */
    private String iac1;
    private String iac2;
    private String iac3;
    /**
     * 交流电压曲线1-3
     */
    private String vac1;
    private String vac2;
    private String vac3;
}