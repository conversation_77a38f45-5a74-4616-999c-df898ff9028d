package entityDTO;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.awt.*;

@Data
public class deviceInformationDTO {
    private String time;
    /*
    * 电站名称
    * */
    String name;
    /*
     * plantId
     * */
    String plantId;
    /*
     * 电站容量
     * */
    String peakPower;
    /*
     * 电站类型
     * */
    String planType;
    /*
     * 国家
     * */
    String country;
    /*
     * 省
     * */
    String state;
    /*
     * 市
     * */
    String city;
    /*
     * 镇/区
     * */
    String town;
    /*
     * 具体地址
     * */
    String address;
    /*
     * 是否收藏
     * */
    String ctrl;
    /*
     * 数据更新时间
     * */
    String updateTime;
    /*
     * 电价
     * */
    String electrovalency;

    float longitude;

    float latitude;

    String wisdomSn;

    String createDate;

    String todayEnergy;

    String totalEnergy;

    String currentPower;

    String password;

    String userUid;


    private String plantUID;
    private String meterId;
}
