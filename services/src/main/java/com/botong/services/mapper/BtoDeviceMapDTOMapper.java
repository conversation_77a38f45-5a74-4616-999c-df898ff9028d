package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entityDTO.BtoDeviceMapDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BtoDeviceMapDTOMapper extends BaseMapper<BtoDeviceMapDTO> {

    BtoDeviceMapDTO selectBtoDeviceMap(@Param("table") String table,@Param("b") String b , @Param("plantId") String plantId ,@Param("sn") String sn);

    List<String> selectDistinct(@Param("b") String b , @Param("plantId") String plantId,@Param("table") String table);

    List<BtoDeviceMapDTO> selectBtoDeviceMapPlus(@Param("table") String table,@Param("time") String time ,@Param("sn") String sn);

    List<BtoDeviceMapDTO> selectBtoDeviceMapPlusMonth(@Param("plantId") String plantId,@Param("time") String time);

    List<BtoDeviceMapDTO> selectBtoDeviceMapPlusYear(@Param("plantId") String plantId,@Param("time") String time);

    List<BtoDeviceMapDTO> selectBtoDeviceMap2(@Param("table") String table,@Param("b") String b , @Param("plantId") String plantId ,@Param("sn") String sn);}
