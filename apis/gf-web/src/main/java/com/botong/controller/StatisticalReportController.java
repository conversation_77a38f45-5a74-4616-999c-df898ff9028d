package com.botong.controller;

import com.botong.services.service.StatisticalReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@CrossOrigin
@RestController
@Api(tags = "统计报表")
@RequestMapping("StatisticalReport")
public class StatisticalReportController {

    @Autowired
    private StatisticalReportService statisticalReportService;

    @ApiOperation("发电统计——日")
    @GetMapping("PowerGenerationStatisticsDay")
    public AjaxResult PowerGenerationStatisticsDay(String time1,String time2,String sn,String page,String pageSize){
        return AjaxResult.success("操作成功",statisticalReportService.PowerGenerationStatisticsDay(time1,time2,sn,page,pageSize));
    }

    @ApiOperation("发电统计——月")
    @GetMapping("PowerGenerationStatisticsMonth")
    public AjaxResult PowerGenerationStatisticsMonth(String date,String plantId,String page,String pageSize){
        return AjaxResult.success("操作成功",statisticalReportService.PowerGenerationStatisticsMonth(date,plantId,page,pageSize));
    }

    @ApiOperation("发电统计——年")
    @GetMapping("PowerGenerationStatisticsYear")
    public AjaxResult PowerGenerationStatisticsYear(String date,String plantId,String page,String pageSize){
        return AjaxResult.success("操作成功",statisticalReportService.PowerGenerationStatisticsYear(date,plantId,page,pageSize));
    }
}
