package entityDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *  真·逆变器数据列表
 * <AUTHOR>
 */
@Data
@ApiModel("逆变器列表数据--河源Web")
public class DeviceListDTO implements Serializable {
    @ApiModelProperty(value = "逆变器SN",notes = "逆变器SN")
    private String deviceSn;

    @ApiModelProperty(value = "电站名",notes = "电站名")
    private String stationName;

    @ApiModelProperty(value = "逆变器状态",notes = "逆变器状态（0：离线，1、3：在线，2：告警）")
    private String status;

    @ApiModelProperty(value = "实时功率",notes = "实时功率")
    private String realTimePower;

    @ApiModelProperty(value = "日发电量",notes = "日发电量")
    private String todayEnergy;

    @ApiModelProperty(value = "月发电量",notes = "月发电量")
    private String monthEnergy;

    @ApiModelProperty(value = "年发电量",notes = "年发电量")
    private String yearEnergy;

    @ApiModelProperty(value = "总发电量",notes = "总发电量")
    private String totalEnergy;

    @ApiModelProperty(value = "更新时间",notes = "数据最后更新时间")
    private String updateTime ;
}
