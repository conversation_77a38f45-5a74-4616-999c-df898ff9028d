package entityDTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class AlarmHandlerDTO implements Serializable {
    /**
     * 处理告警的备注信息
     */
    private String remarks;
    /**
     * 逆变器SN
     */
    private String sn;
    /**
     * 告警状态
     */
    private String status;
    /**
     * 告警信息
     */
    private String alarmMessage;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 电站名称（添加备注后需要进行拼接remarks）
     */
    private String stationName;
}
