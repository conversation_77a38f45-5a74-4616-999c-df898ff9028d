package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.HYVDayMapper;
import com.botong.services.mapper.VDayMapper;
import com.botong.services.service.HYVDayService;
import com.botong.services.service.VDayService;
import entity.VDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HYVDayServiceImpl extends ServiceImpl<VDayMapper, VDay> implements HYVDayService {

    @Autowired
    private HYVDayMapper vDayMapper;

    @Override
    public List<VDay> selectMonth(String plantId){
        String format = DateUtil.format(new Date(), "yyyy-MM");
      //  String format = "2022-07";
                String month = format + "%";
        List<VDay> vDays = vDayMapper.selectMonth(month,plantId);
        return vDays;
    }


    @Override
    public List<VDay> selectYear(String plantId) {
        List<VDay> vDays = vDayMapper.selectYear(plantId);
        return vDays;
    }

}
