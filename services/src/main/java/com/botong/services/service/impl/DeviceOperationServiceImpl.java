package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.DeviceOperationMapper;
import com.botong.services.service.DeviceOperationService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import entityDTO.ModifyStationForm;
import entityDTO.deviceInformationDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import utils.AjaxResult;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class DeviceOperationServiceImpl extends ServiceImpl<DeviceOperationMapper, deviceInformationDTO>implements DeviceOperationService {

    @Autowired
    private DeviceOperationMapper deviceOperationMapper;

    @Override
    public PageInfo<deviceInformationDTO> selectDeviceByCtrl(Integer page, Integer pageSize) {
        PageHelper.startPage(Integer.valueOf(page),Integer.valueOf(pageSize));
        Date date = new Date();
        Date nearDate = new Date(date.getTime() - 10 * 60 * 1000);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyyMMdd");
        String nearTime = simpleDateFormat.format(nearDate).substring(0, 15).concat("%");
        String dateTime= simpleDateFormat2.format(date);
        List<deviceInformationDTO> deviceInformationLunbo=deviceOperationMapper.selectDeviceByCtrl(dateTime,nearTime);
        PageInfo<deviceInformationDTO> deviceInformationPageInfo=new PageInfo<>(deviceInformationLunbo);
        return deviceInformationPageInfo;
    }

    @Override
    public Boolean collectDeviceOperation(String plantId) {
        return deviceOperationMapper.collectDeviceOperation(plantId);
    }

    @Override
    public Boolean cancelCollectDeviceOperation(String plantId) {
        return deviceOperationMapper.cancelCollectDeviceOperation(plantId);
    }

    @Override
    public Object selectDeviceInformation(String plantId) {
        return deviceOperationMapper.selectDeviceInformation(plantId);
    }

    @Override
    @CacheEvict(cacheNames = "HY_APP3M", allEntries = true, cacheManager = "HY_APP_Cache_3M")
    public Boolean modifyDeviceOperation(ModifyStationForm modifyStationForm) {
        Date date=new Date();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String updateTime = simpleDateFormat.format(date);
        modifyStationForm.setUpdateTime(updateTime);
        return deviceOperationMapper.modifyDeviceOperation(modifyStationForm);
    }


    @Override
    public Boolean deleteDeviceOperation(String plantId, String password, String userUid) {
        return deviceOperationMapper.deleteDeviceOperation(plantId,password, userUid);
    }
}



