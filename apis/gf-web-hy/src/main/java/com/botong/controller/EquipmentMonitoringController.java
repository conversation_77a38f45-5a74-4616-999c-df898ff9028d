package com.botong.controller;

import com.botong.services.service.EquipmentMonitoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import utils.AjaxResult;

@CrossOrigin
@RestController
@RequestMapping("EquipmentMonitoring")
@Api(tags = "设备监控")
public class EquipmentMonitoringController {
    @Autowired
    private EquipmentMonitoringService equipmentMonitoringService;

//    @ApiOperation("通讯监控-设备监控_分页plantUid")
//    @GetMapping("EquipmentMonitoringplantUid")
//    public AjaxResult EquipmentMonitoringplantUid(String page, String pageSize, String plantUid) {
//        return AjaxResult.success("操作成功", equipmentMonitoringService.EquipmentMonitoringplantUid(page,pageSize,plantUid));
//    }
}
