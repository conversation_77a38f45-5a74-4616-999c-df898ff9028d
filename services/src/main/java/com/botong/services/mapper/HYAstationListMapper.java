package com.botong.services.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.DevicePvInfo;
import entityDTO.AddDeviceForm;
import entityDTO.AlarmAnalysis;
import entityDTO.HYAstationList;
import entityDTO.userEnginerDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import vo.LonLatVo;
import vo.StationInfoVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Mapper
public interface HYAstationListMapper extends BaseMapper<HYAstationList> {
    List<HYAstationList> selectListt(@Param("tableName") String tableName, @Param("nearTime") String nearTime);

    List<HYAstationList> selectStaionByCondition(@Param("tableName") String tableName, @Param("plantType") Integer plantType, @Param("stationName") String stationName,@Param("minPeakPower") String minPeakPower,
              @Param("maxPeakPower") String maxPeakPower,@Param("town") String town,@Param("status") String status);

    StationInfoVo selectStationInfo(String stationId);

    LonLatVo map(String stationId);

    ArrayList<String> getDeviceIdByStationId(String stationId);

    DevicePvInfo getDevicePvInfoBySN(@Param("SN") String sn);

    int updateDevicePvInfo(@Param("devicePvInfo") DevicePvInfo devicePvInfo);

    HashMap<String,String> getDeviceInfoByPlantId(@Param("plantId") String plantId);

    int addDevicePvInfo(@Param("addDeviceForm") AddDeviceForm addDeviceForm);

    int addDeviceListInfo(@Param("addDeviceForm") AddDeviceForm addDeviceForm);

    List<HashMap<String,String>> selectCompanyId();

    List<HashMap<String,String>> deviceModel();

    int isdeviceListExist(@Param("deviceSN") String deviceSN);

    int isdeviceModulesExist(@Param("sn") String sn);

    List<HashMap<String,String>> deviceList(@Param("plantId") String plantId);

}
