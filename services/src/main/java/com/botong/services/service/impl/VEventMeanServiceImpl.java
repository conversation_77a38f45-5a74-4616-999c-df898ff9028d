package com.botong.services.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.services.mapper.VEventMeanMapper;
import com.botong.services.service.VEventMeanService;
import entity.VEventMean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class VEventMeanServiceImpl extends ServiceImpl<VEventMeanMapper, VEventMean> implements VEventMeanService {

    @Autowired
    private VEventMeanMapper vEventMeanMapper;

    @Override
    public int selectCountDay(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time = "%" + format + "%";
        int i = vEventMeanMapper.selectCountDay(time);
        return i;
    }



    @Override
    public int selectCountSun(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time =  format + "%";
        int i = vEventMeanMapper.selectCountSun(time);
        return i;
    }

    @Override
    public int selectCountInHand(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time =  format + "%";
        int i = vEventMeanMapper.selectCountInHand(time);
        return i;
    }

    @Override
    public int selectCountHand(){
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String time =  format + "%";
        int i = vEventMeanMapper.selectCountHand(time);
        return i;
    }





    @Override
    public Map<String, Object> stationEvent() {
        //调用接口
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String format2 = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm");
        String format3 = DateUtil.format(new Date(), "yyyyMMdd");
        String time =   format + "%";    //2022-11-01%
        String time2 =   format2 + "%";  //2022-11-01 17%
        String time3 =   format3;  //20221101
        time2=time2.substring(0,13);
        //修改主机相无电网
//        vEventMeanMapper.updateHostNoGrid(time,time2,time3);
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("NumberOfAlarmStations",vEventMeanMapper.selectCountSun(time));
        objectObjectHashMap.put("selectCountHand",vEventMeanMapper.selectCountInHand(time));
        objectObjectHashMap.put("selectCountInHand",vEventMeanMapper.selectCountHand(time));
        objectObjectHashMap.put("selectCountDay",vEventMeanMapper.selectCountDay(time));
        return objectObjectHashMap;
    }

    //修改主机无电网
    @Override
    public int updateHostNoGrid() {
        //调用接口
        String format = DateUtil.format(new Date(), "yyyy-MM-dd");
        String format2 = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm");
        String format3 = DateUtil.format(new Date(), "yyyyMMdd");
        String time =   format + "%";    //2022-11-01%
        String time2 =   format2 + "%";  //2022-11-01 17%
        String time3 =   format3 ;  //20221101
        time2=time2.substring(0,13)+"%";
        return vEventMeanMapper.updateHostNoGrid(time,time2,time3);
    }


}
