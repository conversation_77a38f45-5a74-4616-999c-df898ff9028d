package com.botong.services.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.botong.services.mapper.DeviceMonitorMapper;
import com.botong.services.service.DeviceMonitorService;
import entityDTO.sheBeiJianCe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeviceMonitorServiceImpl extends ServiceImpl<DeviceMonitorMapper, sheBeiJianCe> implements DeviceMonitorService {

    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Override
    public List<sheBeiJianCe> collectionMaxMin(String snName, String tableNme, String tabletime2) {
        return deviceMonitorMapper.collectionMaxMin(snName, tableNme, tabletime2);
    }
    @Override
    public List<sheBeiJianCe> collectioAvg(String snName, String tableNme, String tabletime2) {
        return deviceMonitorMapper.collectioAvg(snName, tableNme, tabletime2);
    }

    @Override
    public List<sheBeiJianCe> Devicecurrent(String snName, String tableNme, String tabletime2) {
        return deviceMonitorMapper.Devicecurrent(snName, tableNme, tabletime2);
    }
}
